// src/lib/ebay/hooks/useBytescaleFilenames.ts
import { useCallback } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@convex/_generated/api';
import type { FunctionReturnType } from 'convex/server'; // Import helper type
//import { logger } from '@/lib/logger';
import type { Id } from '@convex/_generated/dataModel';
import type { ItemType } from '@/generated/trading/trading';

//const log = logger.create('hooks:useBytescaleFilenames');

// Define the DraftDataType based on the expected Convex query result
type DraftDataType = FunctionReturnType<typeof api.drafts.getDraftByUuid>;

interface UseBytescaleFilenamesProps {
  draftData: DraftDataType | null | undefined; // Use specific type, allow null/undefined
  imageIds: Id<'images'>[];
}

export function useBytescaleFilenames({ draftData, imageIds }: UseBytescaleFilenamesProps) {
  // Extract bytescale filenames safely from draft data
  const getBytescaleFilenamesFromDraft = useCallback(() => {
    const typedEbayDraft = draftData?.ebayDraft as Partial<ItemType> | undefined;
    if (!typedEbayDraft?.PictureDetails?.PictureURL) {
      return [];
    }

    // Extract only valid Bytescale filenames from URLs
    return typedEbayDraft.PictureDetails.PictureURL.filter((url: string) => {
      return url && (url.includes('upcdn.io') || url.includes('bytescale'));
    })
      .map((url: string) => {
        // Extract the filename (last part of URL)
        const parts = url.split('/');
        return parts[parts.length - 1] || '';
      })
      .filter((filename: string) => !!filename); // Remove empty strings
  }, [draftData?.ebayDraft]);

  // Get filenames from Bytescale URLs - for loading from draft URLs
  const draftBytescaleIds = getBytescaleFilenamesFromDraft();

  // Only query when we have filenames and need them
  const shouldQueryBytescaleIds = draftBytescaleIds.length > 0 && imageIds.length === 0;

  // Query for matching images by Bytescale filename
  const matchingImagesByBytescaleId = useQuery(
    api.images.findImagesByBytescaleId,
    shouldQueryBytescaleIds ? { bytescaleIds: draftBytescaleIds } : 'skip',
  );

  return {
    draftBytescaleIds,
    shouldQueryBytescaleIds,
    matchingImagesByBytescaleId,
    getBytescaleFilenamesFromDraft,
  };
}
