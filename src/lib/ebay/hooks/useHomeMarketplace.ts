import { useState, useEffect } from 'react';
import { useActiveEbayAccount } from '@/lib/ebay/hooks/useActiveEbayAccount';
import type { MarketplaceId } from '@/lib/ebay/types/marketplaces';
import { AVAILABLE_MARKETPLACES } from '@/lib/ebay/types/marketplaces';
import type { UserResponse } from '@/generated/ebay-sdk/commerce/identity/v1/api/types/UserResponse';
import { logger } from '@/lib/logger';

const log = logger.create('hooks:useHomeMarketplace');

/**
 * Hook to get the user's home marketplace from stored eBay account data
 * Returns the marketplace where the user originally registered their eBay account
 * NO LONGER CALLS IDENTITY API - uses stored account data instead
 */
export function useHomeMarketplace() {
  const { activeAccount, isLoading: isLoadingAccount } = useActiveEbayAccount();
  const [homeMarketplace, setHomeMarketplace] = useState<MarketplaceId | null>(null);

  useEffect(() => {
    const typedEbayData = activeAccount?.ebayData as (UserResponse & { registrationMarketplaceId?: string }) | undefined;
    if (!typedEbayData?.registrationMarketplaceId) {
      setHomeMarketplace(null);
      return;
    }

    const registrationMarketplace = typedEbayData.registrationMarketplaceId;

    // Use the registration marketplace directly if it's in our supported list
    const supportedMarketplaceIds = AVAILABLE_MARKETPLACES.map((m) => m.id);

    if (supportedMarketplaceIds.includes(registrationMarketplace as MarketplaceId)) {
      log.debug('Using user home marketplace from eBay registration data', {
        marketplace: registrationMarketplace,
      });
      setHomeMarketplace(registrationMarketplace as MarketplaceId);
    } else {
      log.info('User home marketplace not supported by our app', {
        marketplace: registrationMarketplace,
      });
      setHomeMarketplace(null); // Don't force a fallback - let the app handle this properly
    }
  }, [activeAccount?.ebayData]);

  return {
    homeMarketplace,
    isLoading: isLoadingAccount,
    error: null,
  };
}
