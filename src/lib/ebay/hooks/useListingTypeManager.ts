import { useState, useEffect, useCallback } from 'react';
import { logger } from '@/lib/logger';
import type { UseFormReturn } from 'react-hook-form';
import type { ListingEditorFormValues } from '@/lib/ebay/trading/schemas';
import type { ItemType, ListingTypeCodeType } from '@/generated/trading/trading';
import { Doc } from '@convex/_generated/dataModel';

const log = logger.create('hooks:useListingTypeManager');

// Supported listing types for our application
export const SUPPORTED_LISTING_TYPES = ['FixedPriceItem', 'Chinese'] as const;
export type SupportedListingType = typeof SUPPORTED_LISTING_TYPES[number];

/**
 * Type guard to check if a listing type is supported by our application.
 * Logs a warning for unsupported types to aid debugging.
 */
export function isSupportedListingType(type: unknown): type is SupportedListingType {
  if (typeof type === 'string' && SUPPORTED_LISTING_TYPES.includes(type as SupportedListingType)) {
    return true;
  }
  
  // Log warning for unsupported types to help identify schema mismatches
  if (typeof type === 'string' && ['AdType', 'LeadGeneration', 'CustomCode'].includes(type)) {
    const log = logger.create('hooks:useListingTypeManager');
    log.warn('Unsupported listing type detected - will coerce to FixedPriceItem', { 
      receivedType: type,
      supportedTypes: SUPPORTED_LISTING_TYPES 
    });
  }
  
  return false;
}

interface UseListingTypeManagerProps {
  uuid: string;
  form: UseFormReturn<ListingEditorFormValues>;
  updateDraftMutation: (args: {
    uuid: string;
    updates: Partial<Doc<'userListings'>>;
  }) => Promise<unknown>;
}

/**
 * Manages the eBay listing type (FixedPriceItem or Chinese) for the listing editor form.
 * Handles syncing between form state and local state, and updates the draft in the backend.
 */
export function useListingTypeManager({
  uuid,
  form,
  updateDraftMutation,
}: UseListingTypeManagerProps) {
  // Watch the ListingType field in the form for external changes
  const watchedListingType = form.watch('ListingType');
  // Local state for the current listing type (only support the two main types for now)
  const [listingType, setListingType] = useState<SupportedListingType>(
    isSupportedListingType(watchedListingType) ? watchedListingType : 'FixedPriceItem',
  );

  // Sync local state if ListingType changes externally (e.g., via form.reset)
  useEffect(() => {
    if (isSupportedListingType(watchedListingType) && watchedListingType !== listingType) {
      log.debug('External ListingType change detected', { watchedListingType });
      setListingType(watchedListingType);
    }
  }, [watchedListingType, listingType]);

  /**
   * Handles user-initiated changes to the listing type.
   * Updates form state, local state, and persists changes to the backend draft.
   */
  const handleListingTypeChange = useCallback(
    (newType: SupportedListingType) => {
      log.debug('handleListingTypeChange triggered', { newType });
      setListingType(newType);
      form.setValue('ListingType', newType, { shouldDirty: true });

      // Prepare updates for the backend draft
      const updates: Partial<Doc<'userListings'>> = {
        ebayDraft: {
          ListingType: newType as ListingTypeCodeType,
        } as Partial<ItemType>,
      };

      if (newType === 'FixedPriceItem') {
        // For FixedPriceItem, set duration to GTC and ensure quantity is at least 1
        updates.ebayDraft!.ListingDuration = 'GTC';
        const currentQuantity = form.getValues('Quantity');
        if (!currentQuantity || (typeof currentQuantity === 'number' && currentQuantity < 1)) {
          updates.ebayDraft!.Quantity = 1;
          form.setValue('Quantity', 1);
        } else {
          updates.ebayDraft!.Quantity = currentQuantity;
        }
      } else {
        // For Chinese (auction), set duration to 7 days, quantity to 1, and clear price-related fields
        updates.ebayDraft!.ListingDuration = 'Days_7';
        updates.ebayDraft!.Quantity = 1;
        form.setValue('Quantity', 1);
        updates.ebayDraft!.BuyItNowPrice = undefined;
        updates.ebayDraft!.ReservePrice = undefined;
        // Note: BestOfferEnabled must be set in nested structure
        updates.ebayDraft!.BestOfferDetails = { BestOfferEnabled: false };
        form.setValue('BuyItNowPrice', undefined);
        form.setValue('ReservePrice', undefined);
        form.setValue('BestOfferEnabled', false);
      }

      // Persist the updates to the backend draft
      updateDraftMutation({
        uuid,
        updates,
      })
        .then(() => {
          log.debug('Successfully updated listing type via updateDraft', { newType });
        })
        .catch((error: unknown) => {
          log.error('Failed to update listing type via updateDraft', { uuid, error });
        });
    },
    [form, updateDraftMutation, uuid],
  );

  return {
    listingType,
    handleListingTypeChange,
  };
}
