import { useAuth } from '@clerk/nextjs';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/../convex/_generated/api';
import { useEffect, useState } from 'react';
import { logger } from '@/lib/logger';

const log = logger.create('hooks:useActiveEbayAccount');

/**
 * Hook to get the currently active eBay account for the authenticated user
 * Returns the active account, loading state, and error state
 *
 * CANONICAL SOURCE OF TRUTH: All components should use this hook
 * instead of directly querying ebayAccounts.getActiveAccount or
 * ebayAccountSelection.getSelectedAccount
 *
 * @param refreshKey Optional key to force refetch of queries when changed
 */
export function useActiveEbayAccount(refreshKey?: number) {
  const { userId } = useAuth();
  const [recoveryAttempted, setRecoveryAttempted] = useState(false);
  const [recoveryInProgress, setRecoveryInProgress] = useState(false);

  // Get currently selected account - only if we have a userId
  // Include refreshKey in the query args to force refetch when it changes
  const selected = useQuery(
    api.ebayAccounts.getActiveAccount,
    userId ? { userId, refreshKey } : 'skip',
  );

  // Get user credentials to check if recovery is needed
  const credentials = useQuery(api.ebayAuth.listCredentials, userId ? { userId } : 'skip');

  // Auto-recovery mutation
  const autoSelectAccount = useMutation(api.ebayAccountSelection.autoSelectAccount);

  // Auto-recovery logic with proper guards against loops
  useEffect(() => {
    if (
      userId &&
      !recoveryAttempted &&
      !recoveryInProgress &&
      selected === null &&
      credentials &&
      credentials.length > 0
    ) {
      // User has credentials but no active account - attempt recovery
      log.info('Triggering auto-recovery', { 
        userId: userId.slice(0, 8), 
        credentialsCount: credentials.length,
        selected 
      });
      setRecoveryAttempted(true);
      setRecoveryInProgress(true);

      autoSelectAccount()
        .then((result) => {
          log.info('Auto-recovery successful', result);
        })
        .catch((error) => {
          log.error('Auto-recovery failed', error);
          // Don't retry on failure - let user manually select account
        })
        .finally(() => {
          setRecoveryInProgress(false);
        });
    }
  }, [userId, selected, credentials, recoveryAttempted, recoveryInProgress, autoSelectAccount]);

  const isLoading = selected === undefined || credentials === undefined;
  const activeAccount = selected || null;
  const hasActiveAccount = !!activeAccount;

  return {
    activeAccount,
    isLoading,
    hasActiveAccount,
    // Add error state for better UX
    error: !userId
      ? new Error('Not authenticated')
      : isLoading
        ? null
        : !activeAccount && credentials && credentials.length === 0
          ? new Error('No eBay accounts connected. Please connect an eBay account.')
          : !activeAccount && credentials && credentials.length > 0
            ? new Error(
                'No active eBay account selected. Please select an account from the dropdown.',
              )
            : null,
  };
}
