// src/lib/ebay/hooks/useAutosave.ts (Refactored for V3, updated comments and JSDoc)
import { useEffect, useCallback, useRef } from 'react';
import { useDebouncedCallback } from 'use-debounce';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { Doc } from '@convex/_generated/dataModel';
import type { ListingEditorFormValues } from '@/lib/ebay/trading/schemas';
import type { ProductDetails } from '@/lib/ebay/types/product-types';
import type { ItemType, NameValueListType } from '@/generated/trading/trading';
import _ from 'lodash';
import { useListingEditorStore } from '@/lib/state/useListingEditorMachine';
import { isSupportedListingType } from './useListingTypeManager';

const log = logger.create('hooks:useAutosaveV3');

/**
 * Function signature for the mutation that updates a user listing draft.
 */
type UpdateDraftMutationFnV3 = (args: {
  uuid: string;
  updates: Partial<Doc<'userListings'>>;
}) => Promise<unknown>;

/**
 * Props for the useAutosave hook.
 */
interface UseAutosaveProps {
  uuid: string;
  form: UseFormReturn<ListingEditorFormValues>;
  updateDraftMutation: UpdateDraftMutationFnV3;
  isLoadingDraft: boolean;
  /**
   * Product selected by the user, not stored in the form.
   */
  selectedProduct: ProductDetails | null;
  /**
   * Current item aspects, as a map of aspect name to string[] values.
   */
  itemAspects: Record<string, string[]>;
  /**
   * The current draft data fetched from the backend, used for comparison.
   */
  draftData: Doc<'userListings'> | null | undefined;
}

/**
 * Return type for useAutosave.
 */
interface UseAutosaveReturn {
  isSaving: boolean;
  triggerSave: () => Promise<void>;
}

/**
 * Extracts ListingEditorFormValues from a userListings draft document.
 * Used to reset the form to match the latest saved draft.
 *
 * @param draft The userListings draft document, or null/undefined.
 * @returns Partial form values suitable for react-hook-form's reset().
 */
function getFormValuesFromDraft(
  draft: Doc<'userListings'> | null | undefined,
): Partial<ListingEditorFormValues> {
  if (!draft) return {};
  const ebayDraft = (draft.ebayDraft as Partial<ItemType>) ?? {};
  return {
    Title: ebayDraft.Title,
    SubTitle: ebayDraft.SubTitle,
    Location: ebayDraft.Location,
    PostalCode: ebayDraft.PostalCode,
    Country: ebayDraft.Country,
    Quantity: ebayDraft.Quantity,
    ListingDuration: ebayDraft.ListingDuration,
    DispatchTimeMax: ebayDraft.DispatchTimeMax,
    BestOfferEnabled: ebayDraft.BestOfferDetails?.BestOfferEnabled ?? false,
    StartPrice: ebayDraft.StartPrice,
    BuyItNowPrice: ebayDraft.BuyItNowPrice,
    ReservePrice: ebayDraft.ReservePrice,
    SellerProfiles: ebayDraft.SellerProfiles,
    ListingType: isSupportedListingType(ebayDraft.ListingType) ? ebayDraft.ListingType : null,
    ConditionDescription: ebayDraft.ConditionDescription,
    ConditionDescriptors: ebayDraft.ConditionDescriptors,
    ShippingPackageDetails: ebayDraft.ShippingPackageDetails,
    // Top-level fields mapped to form structure
    PrimaryCategory: { CategoryID: draft.primaryCategoryId ?? '' },
    ConditionID: draft.conditionId,
    ProductListingDetails: draft.productListingDetails,
    // ItemAspects and Images are handled outside the form
  };
}

/**
 * useAutosave
 *
 * Autosaves listing draft changes to the backend when the form or related state changes.
 * - Debounces saves to avoid excessive network calls.
 * - Compares current form values, item aspects, images, and product selection to the last saved draft.
 * - Only sends changed fields to the updateDraftMutation.
 * - Handles both manual and profile-based ReturnPolicy/ShippingDetails logic.
 * - Resets the form after a successful save and data refresh.
 *
 * @param props See UseAutosaveProps for details.
 * @returns { triggerSave } - Function to manually trigger a save.
 */
export function useAutosave({
  uuid,
  form,
  updateDraftMutation,
  isLoadingDraft,
  selectedProduct,
  itemAspects,
  draftData,
}: UseAutosaveProps): Omit<UseAutosaveReturn, 'isSaving'> {
  const isMounted = useRef(false);
  const justSavedRef = useRef(false);

  // XState store actions and state for save status
  const setSaveStatus = useListingEditorStore((state) => state.setSaveStatus);
  const saveStatus = useListingEditorStore((state) => state.saveDraftStatus);

  /**
   * Triggers a save if there are changes between the current form state and the last saved draft.
   * Compares all relevant fields, including item aspects and product selection.
   * Handles both manual and profile-based ReturnPolicy/ShippingDetails.
   * Sets save status in the XState store and shows toast notifications.
   */
  const triggerSave = useCallback(async () => {
    // Skip save if loading, missing data, or already saving
    if (isLoadingDraft || !uuid || !draftData || saveStatus === 'pending') {
      log.warn('Save skipped', {
        isLoadingDraft,
        uuid,
        hasDraft: !!draftData,
        isSaving: saveStatus === 'pending',
      });
      return;
    }

    setSaveStatus('pending');
    justSavedRef.current = false;
    log.debug('Attempting to save...', { uuid });

    try {
      const currentFormValues = form.getValues();
      const updates: Partial<Doc<'userListings'>> = {};
      const ebayDraftUpdates: Partial<ItemType> = {};

      /**
       * Compares a form value to the saved value and adds it to ebayDraftUpdates if changed.
       * @param fieldPath Path in ebayDraft (string or array for nested fields)
       * @param formValue Current value from the form
       * @param savedValue Value from the last saved draft
       */
      const compareAndAdd = (
        fieldPath: string | string[],
        formValue: unknown,
        savedValue: unknown,
      ) => {
        if (!_.isEqual(formValue, savedValue)) {
          _.set(ebayDraftUpdates, fieldPath, formValue ?? undefined);
        }
      };

      // Cast ebayDraft to proper type for type-safe field access
      const typedEbayDraft = draftData.ebayDraft as Partial<ItemType> | undefined;

      // Compare all direct ebayDraft fields
      compareAndAdd('Title', currentFormValues.Title, typedEbayDraft?.Title);
      compareAndAdd('SubTitle', currentFormValues.SubTitle, typedEbayDraft?.SubTitle);
      compareAndAdd('Location', currentFormValues.Location, typedEbayDraft?.Location);
      compareAndAdd('PostalCode', currentFormValues.PostalCode, typedEbayDraft?.PostalCode);
      compareAndAdd('Country', currentFormValues.Country, typedEbayDraft?.Country);
      compareAndAdd('Quantity', currentFormValues.Quantity, typedEbayDraft?.Quantity);
      compareAndAdd(
        'ListingDuration',
        currentFormValues.ListingDuration,
        typedEbayDraft?.ListingDuration,
      );
      compareAndAdd(
        'DispatchTimeMax',
        currentFormValues.DispatchTimeMax,
        typedEbayDraft?.DispatchTimeMax,
      );
      compareAndAdd(
        'BestOfferDetails.BestOfferEnabled',
        currentFormValues.BestOfferEnabled,
        typedEbayDraft?.BestOfferDetails?.BestOfferEnabled,
      );
      compareAndAdd('StartPrice', currentFormValues.StartPrice, typedEbayDraft?.StartPrice);
      compareAndAdd(
        'BuyItNowPrice',
        currentFormValues.BuyItNowPrice,
        typedEbayDraft?.BuyItNowPrice,
      );
      compareAndAdd(
        'ReservePrice',
        currentFormValues.ReservePrice,
        typedEbayDraft?.ReservePrice,
      );
      compareAndAdd(
        'SellerProfiles',
        currentFormValues.SellerProfiles,
        typedEbayDraft?.SellerProfiles,
      );
      compareAndAdd('ListingType', currentFormValues.ListingType, typedEbayDraft?.ListingType);
      compareAndAdd(
        'ConditionDescription',
        currentFormValues.ConditionDescription,
        typedEbayDraft?.ConditionDescription,
      );
      // Clean up ConditionDescriptors to remove empty/undefined values before sending to Convex
      const cleanConditionDescriptors = (
        descriptors: ListingEditorFormValues['ConditionDescriptors'],
      ) => {
        if (!descriptors?.ConditionDescriptor) return descriptors;

        const cleanedDescriptors = descriptors.ConditionDescriptor.map(
          (desc: { Name?: string; Value?: (string | undefined | null)[] }) => ({
            ...desc,
            // Filter out undefined, null, or empty string values from Value arrays
            Value:
              desc.Value?.filter(
                (val: string | undefined | null): val is string =>
                  val !== undefined && val !== null && val !== '',
              ) || [],
          }),
        )
          // Only keep descriptors that have a Name (descriptor ID) and either:
          // 1. Have non-empty values, OR
          // 2. Are Certification Number (ID 27503) which is optional for graded cards
          .filter(
            (desc: { Name?: string; Value?: string[] }) =>
              desc.Name && ((desc.Value && desc.Value.length > 0) || desc.Name === '27503'),
          );

        return cleanedDescriptors.length > 0
          ? { ConditionDescriptor: cleanedDescriptors }
          : undefined;
      };

      compareAndAdd(
        'ConditionDescriptors',
        cleanConditionDescriptors(currentFormValues.ConditionDescriptors),
        typedEbayDraft?.ConditionDescriptors,
      );
      compareAndAdd(
        'ShippingPackageDetails',
        currentFormValues.ShippingPackageDetails,
        typedEbayDraft?.ShippingPackageDetails,
      );

      // Handle ReturnPolicy and ShippingDetails logic for manual/profile-based listings
      if (
        currentFormValues.SellerProfiles === null ||
        currentFormValues.SellerProfiles === undefined
      ) {
        // Manual mode: compare and update ReturnPolicy and ShippingDetails
        compareAndAdd(
          'ReturnPolicy',
          currentFormValues.ReturnPolicy,
          typedEbayDraft?.ReturnPolicy,
        );
        compareAndAdd(
          'ShippingDetails',
          currentFormValues.ShippingDetails,
          typedEbayDraft?.ShippingDetails,
        );
      } else {
        // Profile mode: ensure manual fields are cleared if previously set
        if (typedEbayDraft?.ReturnPolicy !== undefined) {
          _.set(ebayDraftUpdates, 'ReturnPolicy', undefined);
        }
        if (typedEbayDraft?.ShippingDetails !== undefined) {
          _.set(ebayDraftUpdates, 'ShippingDetails', undefined);
        }
      }

      // Compare itemAspects (convert to NameValueListType[] for comparison)
      const currentAspectsNVL: NameValueListType[] = Object.entries(itemAspects).map(
        ([name, values]) => ({ Name: name, Value: values }),
      );
      if (!_.isEqual(currentAspectsNVL, draftData.itemAspects)) {
        updates.itemAspects = currentAspectsNVL;
      }

      // Compare productListingDetails (selected product EPID)
      const currentEpid = selectedProduct?.epid;
      const savedEpid = draftData.productListingDetails?.ProductReferenceID;
      if (currentEpid !== savedEpid) {
        updates.productListingDetails = currentEpid
          ? { ProductReferenceID: currentEpid, IncludeStockPhotoURL: true }
          : undefined;
        // If a product is set, clear itemAspects (product select handler may also do this)
        if (currentEpid) {
          updates.itemAspects = undefined;
        }
      }

      // Merge ebayDraft updates if any fields changed
      const hasEbayDraftUpdates = Object.keys(ebayDraftUpdates).length > 0;
      if (hasEbayDraftUpdates) {
        updates.ebayDraft = { ...(draftData.ebayDraft || {}), ...ebayDraftUpdates };
      }

      // Only call mutation if there are actual changes
      if (Object.keys(updates).length > 0) {
        log.debug('Calling updateDraft with changes', {
          numChanges: Object.keys(updates).length,
          changedKeys: Object.keys(updates),
        });
        await updateDraftMutation({ uuid, updates });
        justSavedRef.current = true;
        setSaveStatus('success');
        toast.success('Draft saved');
      } else {
        log.debug('No changes detected, skipping save.');
        setSaveStatus('idle');
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      log.error('Error during save', { uuid, error: errorMsg });
      setSaveStatus('error', errorMsg);
      toast.error(`Save failed: ${errorMsg}`);
    }
  }, [
    isLoadingDraft,
    uuid,
    draftData,
    saveStatus,
    form,
    selectedProduct,
    itemAspects,
    updateDraftMutation,
    setSaveStatus,
  ]);

  // Debounced version of triggerSave to avoid rapid repeated saves
  const debouncedTriggerSave = useDebouncedCallback(triggerSave, 1000);

  // Watch form values and trigger debounced autosave when dirty and not pending
  const watchedFormValues = form.watch();
  useEffect(() => {
    if (!isMounted.current || isLoadingDraft || saveStatus === 'pending') {
      return;
    }
    if (form.formState.isDirty) {
      log.debug('Form values changed and form is dirty, triggering debounced autosave...');
      debouncedTriggerSave();
    } else {
      log.debug('Form values changed but form is not dirty, skipping debounced autosave.');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedFormValues, isLoadingDraft, saveStatus, form.formState.isDirty]);

  // After a successful save and data refresh, reset the form to match the latest draft
  useEffect(() => {
    if (justSavedRef.current && draftData) {
      log.debug('Save completed and draftData updated, resetting form state.');
      const formValuesFromDraft = getFormValuesFromDraft(draftData);
      form.reset(formValuesFromDraft);
      justSavedRef.current = false;
    }
  }, [draftData, form]);

  // Track mount state to avoid running autosave on initial render
  useEffect(
    () => {
      isMounted.current = true;
      // Optionally reset status to idle on mount
      // setSaveStatus('idle');
      return () => {
        isMounted.current = false;
      };
    },
    [
      // Add setSaveStatus to deps if uncommenting above
    ],
  );

  // Only return triggerSave (isSaving is managed in XState)
  return {
    triggerSave,
  };
}
