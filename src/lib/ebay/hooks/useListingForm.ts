/**
 * React hook for managing the eBay listing editor form state.
 *
 * This hook initializes and manages a react-hook-form instance for the eBay listing editor,
 * automatically applying draft data and eBay account defaults for location fields.
 * It ensures the form is reset with the correct values when the draft or account changes.
 *
 * @param {Object} params
 * @param {Doc<'userListings'> | null | undefined} params.draftData - The current listing draft data, or null/undefined if not present.
 * @returns {{
 *   form: ReturnType<typeof useForm<ListingEditorFormValues>>,
 *   isResettingForm: boolean
 * }}
 *
 * The returned `form` object is a react-hook-form instance for the listing editor.
 * The `isResettingForm` boolean is true while the form is being reset to new values.
 */
import { useState, useEffect, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ListingEditorFormSchema, type ListingEditorFormValues } from '@/lib/ebay/trading/schemas';
import { logger } from '@/lib/logger';
import type { Doc } from '@convex/_generated/dataModel';
import { useActiveEbayAccount } from '@/lib/ebay/hooks/useActiveEbayAccount';
import type { ItemType } from '@/generated/trading/trading';
import { isSupportedListingType } from './useListingTypeManager';

const log = logger.create('hooks:useListingForm');

interface UseListingFormProps {
  /** The current user listing draft data, or null/undefined if not loaded. */
  draftData: Doc<'userListings'> | null | undefined;
}

export function useListingForm({ draftData }: UseListingFormProps) {
  // Indicates if the form is currently being reset to new values.
  const [isResettingForm, setIsResettingForm] = useState(false);

  // Tracks the last draftData used to reset the form, to avoid unnecessary resets.
  const lastResetDataRef = useRef<Doc<'userListings'> | null | undefined>(null);

  // Get the active eBay account and loading state.
  const { activeAccount, isLoading: isLoadingActiveAccount } = useActiveEbayAccount();

  // Initialize react-hook-form for the listing editor, using Zod schema validation.
  const form = useForm<ListingEditorFormValues>({
    resolver: zodResolver(ListingEditorFormSchema),
    defaultValues: undefined,
    mode: 'onBlur',
  });

  useEffect(() => {
    // Prevent resetting the form if a reset is already in progress.
    if (isResettingForm) return;

    // Wait for the active eBay account to finish loading before applying defaults.
    if (isLoadingActiveAccount) {
      log.debug('Waiting for active eBay account data to load...');
      return;
    }

    // Build new default values for the form, using draft data if available.
    const typedEbayDraft = (draftData?.ebayDraft as Partial<ItemType>) || {};
    const newDefaultValues: Partial<ListingEditorFormValues> = {
      // Only spread fields that are compatible between ItemType and form values
      Title: typedEbayDraft.Title,
      SubTitle: typedEbayDraft.SubTitle,
      Description: typedEbayDraft.Description,
      Location: typedEbayDraft.Location,
      PostalCode: typedEbayDraft.PostalCode,
      Country: typedEbayDraft.Country,
      StartPrice: typedEbayDraft.StartPrice,
      BuyItNowPrice: typedEbayDraft.BuyItNowPrice,
      ReservePrice: typedEbayDraft.ReservePrice,
      DispatchTimeMax: typedEbayDraft.DispatchTimeMax,
      // Filter ListingType to only editable values (for now, only auction and fixed-price)
      ListingType: isSupportedListingType(typedEbayDraft.ListingType) 
        ? typedEbayDraft.ListingType 
        : null,
      ConditionID: draftData?.conditionId ?? null,
      ConditionDescriptors: typedEbayDraft?.ConditionDescriptors,
      PrimaryCategory: {
        CategoryID: draftData?.primaryCategoryId ?? '',
      },
      // Extract BestOfferEnabled from nested BestOfferDetails structure
      BestOfferEnabled: typedEbayDraft?.BestOfferDetails?.BestOfferEnabled ?? false,
      // Map Quantity from backend to frontend form
      Quantity: typedEbayDraft?.Quantity ?? null,
    };

    // NOTE: Default location is now handled by the foundation system
    // The deprecated account-level defaultLocation/defaultPostalCode/defaultCountry
    // fields have been removed in favor of foundation blueprints

    // Compare current form values and new defaults to determine if a reset is needed.
    const currentFormValues = form.getValues();
    const formValuesString = JSON.stringify(currentFormValues);
    const newDefaultValuesString = JSON.stringify({
      ...currentFormValues,
      ...newDefaultValues,
    });

    // Only reset the form if the draft data reference or calculated values have changed.
    if (draftData !== lastResetDataRef.current || newDefaultValuesString !== formValuesString) {
      setIsResettingForm(true);
      log.debug('Resetting form with new draft data and/or defaults', { uuid: draftData?.uuid });
      form.reset(newDefaultValues as ListingEditorFormValues);
      lastResetDataRef.current = draftData;
      // Use a timeout to ensure isResettingForm is set back to false after the reset.
      setTimeout(() => setIsResettingForm(false), 0);
    } else {
      log.debug('Draft data reference and calculated form values unchanged, skipping form reset');
    }
  }, [
    draftData,
    form,
    isResettingForm,
    activeAccount?._id,
    activeAccount?.ebayUserId,
    isLoadingActiveAccount,
    activeAccount,
  ]);

  return {
    form,
    isResettingForm,
  };
}
