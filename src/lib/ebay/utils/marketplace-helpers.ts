import { api } from '@convex/_generated/api';
import { getQueryClient } from '@/lib/convex-utils';
import { AVAILABLE_MARKETPLACES, MarketplaceId } from '@/lib/ebay/types/marketplaces';
import type { UserResponse } from '@/generated/ebay-sdk/commerce/identity/v1/api/types/UserResponse';
import { logger } from '@/lib/logger';
import { Doc } from '@convex/_generated/dataModel';

const log = logger.create('utils:marketplace-helpers');

/**
 * Get the effective marketplace for a user in server actions
 * Uses the same smart default logic as the client:
 * 1. User's current selection from workspace context
 * 2. User's home marketplace from eBay registration
 * 3. ERROR if no marketplace can be determined (no unsafe fallbacks)
 *
 * @throws Error if marketplace cannot be determined - this prevents wrong marketplace operations
 */
export async function getEffectiveMarketplace(
  userId: string,
  ebayUserId?: string,
  allowUsFallback: boolean = false, // Explicit opt-in for US fallback in specific cases
): Promise<MarketplaceId> {
  try {
    const convexClient = await getQueryClient();

    // 1. Try to get user's current marketplace selection
    if (ebayUserId) {
      const workspaceContext = await convexClient.query(
        api.userWorkspaceContext.getWorkspaceContext,
        {
          userId,
          ebayUserId,
        },
      );

      if (workspaceContext?.currentMarketplaceId) {
        log.debug('Using user selected marketplace', {
          marketplace: workspaceContext.currentMarketplaceId,
          userId,
        });
        return workspaceContext.currentMarketplaceId as MarketplaceId;
      }
    }

    // 2. Try to get user's home marketplace from eBay account
    const ebayAccounts = await convexClient.query(
      api.ebayAccountSelection.getEbayAccountsForUser,
      {},
    );
    // An active account is one that hasn't been disconnected (disconnectedAt is undefined)
    const activeAccount = ebayAccounts?.find(
      (account: Doc<'ebayAccounts'>) => account.disconnectedAt === undefined,
    );

    const typedEbayData = activeAccount?.ebayData as (UserResponse & { registrationMarketplaceId?: string }) | undefined;
    if (typedEbayData?.registrationMarketplaceId) {
      const registrationMarketplace = typedEbayData.registrationMarketplaceId;

      // Use the registration marketplace directly if it's supported
      const supportedMarketplaceIds = AVAILABLE_MARKETPLACES.map((m) => m.id);

      if (supportedMarketplaceIds.includes(registrationMarketplace as MarketplaceId)) {
        log.debug('Using home marketplace from eBay registration', {
          registrationMarketplace,
          userId,
        });
        return registrationMarketplace as MarketplaceId;
      } else {
        log.error('User home marketplace not supported by app', {
          registrationMarketplace,
          userId,
        });
        throw new Error(
          `User's eBay home marketplace (${registrationMarketplace}) is not supported by this app. Please contact support.`,
        );
      }
    }

    // 3. If we get here, user hasn't connected eBay or data is incomplete
    if (allowUsFallback) {
      log.warn('No marketplace determined, using US fallback with explicit permission', {
        userId,
        allowUsFallback,
      });
      return 'EBAY_US';
    }

    // 4. Throw error rather than guessing wrong marketplace
    const errorMessage =
      'Cannot determine user marketplace - user must connect eBay account or select marketplace';
    log.error(errorMessage, {
      userId,
      hasEbayUserId: !!ebayUserId,
      hasActiveAccount: !!activeAccount,
      registrationMarketplace: typedEbayData?.registrationMarketplaceId,
    });
    throw new Error(errorMessage);
  } catch (error) {
    // If it's our intentional error, re-throw it
    if (error instanceof Error && error.message.includes('Cannot determine user marketplace')) {
      throw error;
    }

    // For other errors, log and decide based on fallback setting
    log.error('Error getting effective marketplace', {
      error,
      userId,
      allowUsFallback,
    });

    if (allowUsFallback) {
      log.warn('Using emergency US fallback due to error', { userId });
      return 'EBAY_US';
    }

    throw new Error('Failed to determine marketplace and fallback not allowed');
  }
}

/**
 * Get marketplace with US fallback allowed (for backwards compatibility)
 * Use sparingly - only when US fallback is actually safe/desired
 */
export async function getEffectiveMarketplaceWithUsFallback(
  userId: string,
  ebayUserId?: string,
): Promise<MarketplaceId> {
  return getEffectiveMarketplace(userId, ebayUserId, true);
}

/**
 * Check if user has a determinable marketplace without fallbacks
 * Useful for validation before operations that require marketplace
 */
export async function hasValidMarketplace(
  userId: string,
  ebayUserId?: string,
): Promise<{ hasMarketplace: boolean; marketplace?: MarketplaceId; reason?: string }> {
  try {
    const marketplace = await getEffectiveMarketplace(userId, ebayUserId, false);
    return { hasMarketplace: true, marketplace };
  } catch (error) {
    return {
      hasMarketplace: false,
      reason: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
