/**
 * Data transformation utilities for eBay Trading API listings
 * Pure functions for checksum calculation, change detection, and data source selection.
 *
 * NOTE: State determination logic has been moved to XState machine in:
 * src/lib/state/listingStateMachine.ts
 */
import { generateChecksum } from '@/lib/checksums';
import type { ItemType } from '@/generated/trading/trading';

/**
 * Timestamp fields used for state determination.
 * Mirrors relevant fields from userListings schema.
 */
export interface EntityTimestamps {
  lastSyncedWithEbay?: number | null;
  lastPushedToEbay?: number | null;
  lastModifiedLocally?: number | null;
  endedAt?: number | null; // Timestamp when listing ended on eBay
  syncError?: string | null; // Stores error message
}

/**
 * Base entity interface that all eBay entities extend
 */
export interface BaseEntity extends EntityTimestamps {
  ebayUserId: string;
  isArchived?: boolean;
}

/**
 * Interface for entities that maintain both eBay data and local drafts
 */
export interface TwoTableEntity<T> extends BaseEntity {
  ebayData?: T;
  ebayDraft?: T;
  checksum?: string;
}

/**
 * Detects changes in eBay data using checksums and determines
 * if timestamps should be updated.
 *
 * This function is CRITICAL for handling eBay's eventually consistent system.
 * It helps detect when our pushed changes have propagated through eBay's system
 * by comparing checksums of the data we receive from eBay with what we had before.
 *
 * IMPORTANT: This is the ONLY function that should be used to handle entity timestamps
 * and change detection. Do not manually update timestamps or implement separate
 * timestamp logic elsewhere.
 *
 * The function handles:
 * 1. Checksum calculation and comparison
 * 2. Timestamp update decisions based on eBay's eventual consistency
 * 3. Draft state clearing when changes are confirmed
 * 4. Safe error state management
 *
 * @param newData New data from eBay API
 * @param existingData Existing data from Convex
 * @param lastPushedToEbay Last time data was pushed to eBay
 * @returns Object with checksum, change detection flags, and timestamp info
 */
export function detectEbayDataChanges<T>(
  newData: T,
  existingData?: T,
  lastPushedToEbay?: number,
): {
  checksum: string;
  previousChecksum?: string;
  hasChanged: boolean;
  shouldUpdateTimestamp: boolean;
  canClearDraft: boolean;
} {
  if (!newData) {
    throw new Error('Cannot detect changes with null or undefined newData');
  }

  const newChecksum = generateChecksum(newData);
  const previousChecksum = existingData ? generateChecksum(existingData) : undefined;
  const hasChanged = !previousChecksum || newChecksum !== previousChecksum;

  // Only update timestamp if:
  // 1. We haven't pushed anything yet (no lastPushedToEbay), OR
  // 2. If we have pushed, only update if eBay is returning DIFFERENT data
  //    (meaning our changes have propagated through eBay's system)
  const shouldUpdateTimestamp = !lastPushedToEbay || hasChanged;

  // We can clear the draft when:
  // 1. We have pushed changes to eBay (lastPushedToEbay exists), AND
  // 2. We are now seeing those changes reflected back in eBay's data (hasChanged is true), AND
  // 3. This is not the first sync (existingData exists)
  const canClearDraft = !!lastPushedToEbay && hasChanged && !!existingData;

  return {
    checksum: newChecksum,
    previousChecksum,
    hasChanged,
    shouldUpdateTimestamp,
    canClearDraft,
  };
}


/**
 * Type-safe version of getEntityDataSource specifically for eBay listing data.
 * Returns properly typed ItemType data from either ebayDraft or ebayData.
 */
export function getEbayEntityDataSource(
  entity: {
    ebayData?: Partial<ItemType>;
    ebayDraft?: unknown; // Convex returns this as any
  } & EntityTimestamps,
  preferDraft: boolean = false,
): Partial<ItemType> | undefined {
  if (!entity) {
    return undefined;
  }

  // Type-safe data source selection
  if (preferDraft && entity.ebayDraft) {
    return entity.ebayDraft as Partial<ItemType>;
  }

  return (entity.ebayDraft as Partial<ItemType>) || (entity.ebayData as Partial<ItemType>);
}

/**
 * Clears error state for an entity
 * @returns Modified timestamps with error fields cleared
 */
export function clearEntityError<T extends EntityTimestamps>(entity: T): Partial<T> {
  return {
    ...entity,
    syncError: undefined,
  };
}

// Re-export state management functions from XState machine to maintain single source of truth
export {
  EntityStates,
  type EntityState,
  determineEntityState,
  canEditEntity,
  canPushToEbay,
  getEntityStateLabel,
  isEntityDraft,
  isEntityActive,
  isEntityPending,
  isEntityError,
  isEntityEnded,
} from '../state/listingStateMachine';
