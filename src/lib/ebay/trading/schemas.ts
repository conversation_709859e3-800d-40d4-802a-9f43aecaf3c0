import { z } from 'zod';
import { ItemTypeSchema } from '@/generated/trading/trading';
import { ReturnPolicyTypeSchema } from '@/generated/trading/trading';

/**
 * Base schema for listing editor form, derived from eBay Trading API ItemType.
 * Picks essential fields needed for listing creation/editing.
 */
const BaseListingEditorFormShape = ItemTypeSchema.pick({
  Title: true,
  SubTitle: true,
  PrimaryCategory: true,
  ConditionID: true,
  ConditionDescription: true,
  ConditionDescriptors: true,
  StartPrice: true,
  BuyItNowPrice: true,
  ReservePrice: true,
  Quantity: true,
  Description: true,
  Location: true,
  PostalCode: true,
  DispatchTimeMax: true,
  SellerProfiles: true,
  ProductListingDetails: true,
  ListingDuration: true,
  ListingType: true,
  Currency: true,
  Country: true,
  ShippingDetails: true,
  ReturnPolicy: true,
});

// NEW: Schema for imageStatus, matching userListings table structure
const ImageStatusSchema = z
  .object({
    imageIds: z.array(z.string()), // Array of Id<"images">, treated as strings for Zod/form
    // We can add other imageStatus fields here if the form needs to manage them directly
    // e.g., ebayPictureUrls: z.optional(z.array(z.string())).nullable(),
  })
  .deepPartial()
  .optional()
  .nullable(); // Use deepPartial and optional/nullable for draft flexibility

/**
 * Zod schema for the ListingEditor form.
 * Extends the base shape with specific validation rules while maintaining draft compatibility.
 * All fields are optional and nullable to support saving incomplete drafts.
 *
 * Key features:
 * - Validates field formats and ranges according to eBay requirements
 * - Supports partial saves for drafts
 * - Includes cross-field validation rules
 * - Handles both fixed price and auction listing types
 * - Validates pricing rules and shipping details
 *
 * @see {@link ListingEditorFormValues} for the TypeScript type
 * @see {@link ListingEditorDefaultValues} for default values type
 */
export const ListingEditorFormSchema = BaseListingEditorFormShape.extend({
  Title: z
    .string({ required_error: 'Title is required.' })
    .min(5, { message: 'Title must be at least 5 characters.' })
    .max(80, { message: 'Title cannot exceed 80 characters.' })
    .optional()
    .nullable(),
  SubTitle: z
    .string()
    .max(55, { message: 'Subtitle cannot exceed 55 characters.' })
    .optional()
    .nullable(),
  PrimaryCategory: z
    .object(
      {
        CategoryID: z.string().min(1, { message: 'Please select a primary category.' }),
      },
      { required_error: 'Primary category is required.' },
    )
    .optional()
    .nullable(),
  ConditionID: z
    .number({ invalid_type_error: 'Condition must be a number.' })
    .int()
    .positive({ message: 'Invalid Condition ID.' })
    .optional()
    .nullable(),
  ConditionDescription: z.string().optional().nullable(),
  ConditionDescriptors: z
    .object({
      ConditionDescriptor: z
        .array(
          z.object({
            Name: z.string(),
            Value: z.array(z.string()).optional(),
            AdditionalInfo: z.string().optional(),
          }),
        )
        .optional(),
    })
    .optional()
    .nullable(),
  StartPrice: z
    .object({
      Value: z
        .union([
          z
            .number({ invalid_type_error: 'Price must be a number.' })
            .positive({ message: 'Price must be positive.' })
            .multipleOf(0.01, { message: 'Price can have at most 2 decimal places.' }),
          z.literal(0),
        ])
        .optional()
        .nullable(),
      CurrencyID: z.string().length(3).optional().nullable(),
    })
    .optional()
    .nullable(),
  BuyItNowPrice: z
    .object({
      Value: z
        .union([
          z
            .number({ invalid_type_error: 'Price must be a number.' })
            .positive({ message: 'Buy It Now price must be positive.' })
            .multipleOf(0.01, { message: 'Price can have at most 2 decimal places.' }),
          z.literal(0),
        ])
        .optional()
        .nullable(),
      CurrencyID: z.string().length(3).optional().nullable(),
    })
    .optional()
    .nullable(),
  ReservePrice: z
    .object({
      Value: z
        .union([
          z
            .number({ invalid_type_error: 'Price must be a number.' })
            .positive({ message: 'Reserve price must be positive.' })
            .multipleOf(0.01, { message: 'Price can have at most 2 decimal places.' }),
          z.literal(0),
        ])
        .optional()
        .nullable(),
      CurrencyID: z.string().length(3).optional().nullable(),
    })
    .optional()
    .nullable(),
  Quantity: z
    .number({ invalid_type_error: 'Quantity must be a number.' })
    .int()
    .positive({ message: 'Quantity must be at least 1.' })
    .optional()
    .nullable(),
  Description: z.string().optional().nullable(),
  Location: z.string().max(100, { message: 'Location too long.' }).optional().nullable(),
  PostalCode: z.string().max(20, { message: 'Postal code too long.' }).optional().nullable(),
  DispatchTimeMax: z
    .number({
      required_error: 'Handling time is required.',
      invalid_type_error: 'Handling time must be a number.',
    })
    .int('Handling time must be a whole number of days.')
    .min(1, 'Handling time must be at least 1 day.'),
  SellerProfiles: z
    .object({
      SellerShippingProfile: z
        .object({
          ShippingProfileID: z.coerce.number().positive().optional().nullable(),
        })
        .optional()
        .nullable(),
      SellerReturnProfile: z
        .object({
          ReturnProfileID: z.coerce.number().positive().optional().nullable(),
        })
        .optional()
        .nullable(),
      SellerPaymentProfile: z
        .object({
          PaymentProfileID: z.coerce.number().positive().optional().nullable(),
        })
        .optional()
        .nullable(),
    })
    .optional()
    .nullable(),
  ListingDuration: z.string().optional().nullable(),
  // Support current critical types + graceful handling of other eBay types
  ListingType: z.enum(['FixedPriceItem', 'Chinese', 'AdType', 'LeadGeneration', 'CustomCode']).optional().nullable(),
  Currency: z.string().length(3).optional().nullable(),
  Country: z.string().length(2).optional().nullable(),
  ProductListingDetails: z
    .object({
      ProductReferenceID: z.string().optional().nullable(),
      IncludeeBayProductDetails: z.boolean().optional().nullable(),
    })
    .optional()
    .nullable(),
  ShippingDetails: z
    .object({
      ShippingType: z
        .enum([
          'Flat',
          'Calculated',
          'Freight',
          'Free',
          'NotSpecified',
          'FlatDomesticCalculatedInternational',
          'CalculatedDomesticFlatInternational',
        ])
        .optional()
        .nullable(),
      ShippingServiceOptions: z
        .array(
          z.object({
            ShippingServicePriority: z.number().int().optional().nullable(),
            ShippingService: z.string().optional().nullable(),
            ShippingServiceCost: z
              .object({
                Value: z
                  .union([z.number().nonnegative().multipleOf(0.01), z.literal(0), z.literal(-1)])
                  .optional()
                  .nullable(),
                CurrencyID: z.string().length(3).optional().nullable(),
              })
              .optional()
              .nullable(),
            ShippingServiceAdditionalCost: z
              .object({
                Value: z
                  .union([z.number().nonnegative().multipleOf(0.01), z.literal(0)])
                  .optional()
                  .nullable(),
                CurrencyID: z.string().length(3).optional().nullable(),
              })
              .optional()
              .nullable(),
            FreeShipping: z.boolean().optional().nullable(),
          }),
        )
        .optional()
        .nullable(),
      InternationalShippingServiceOption: z
        .array(
          z.object({
            ShippingServicePriority: z.number().int().optional().nullable(),
            ShippingService: z.string().optional().nullable(),
            ShippingServiceCost: z
              .object({
                Value: z
                  .union([z.number().nonnegative().multipleOf(0.01), z.literal(0)])
                  .optional()
                  .nullable(),
                CurrencyID: z.string().length(3).optional().nullable(),
              })
              .optional()
              .nullable(),
            ShippingServiceAdditionalCost: z
              .object({
                Value: z
                  .union([z.number().nonnegative().multipleOf(0.01), z.literal(0)])
                  .optional()
                  .nullable(),
                CurrencyID: z.string().length(3).optional().nullable(),
              })
              .optional()
              .nullable(),
            ShipToLocation: z.array(z.string()).optional().nullable(),
          }),
        )
        .optional()
        .nullable(),
      CalculatedShippingRate: z
        .object({
          OriginatingPostalCode: z.string().max(20).optional().nullable(),
          PackagingHandlingCosts: z
            .object({
              Value: z
                .union([z.number().nonnegative().multipleOf(0.01), z.literal(0)])
                .optional()
                .nullable(),
              CurrencyID: z.string().length(3).optional().nullable(),
            })
            .optional()
            .nullable(),
          PackageDepth: z
            .object({
              Value: z.number().optional().nullable(),
              unit: z.string().optional().nullable(),
            })
            .optional()
            .nullable(),
          PackageLength: z
            .object({
              Value: z.number().optional().nullable(),
              unit: z.string().optional().nullable(),
            })
            .optional()
            .nullable(),
          PackageWidth: z
            .object({
              Value: z.number().optional().nullable(),
              unit: z.string().optional().nullable(),
            })
            .optional()
            .nullable(),
          WeightMajor: z
            .object({
              Value: z.number().optional().nullable(),
              unit: z.string().optional().nullable(),
            })
            .optional()
            .nullable(),
          WeightMinor: z
            .object({
              Value: z.number().optional().nullable(),
              unit: z.string().optional().nullable(),
            })
            .optional()
            .nullable(),
          InternationalPackagingHandlingCosts: z
            .object({
              Value: z
                .union([z.number().nonnegative().multipleOf(0.01), z.literal(0)])
                .optional()
                .nullable(),
              CurrencyID: z.string().length(3).optional().nullable(),
            })
            .optional()
            .nullable(),
        })
        .optional()
        .nullable(),
      SalesTax: z
        .object({
          SalesTaxPercent: z.number().nonnegative().optional().nullable(),
          ShippingIncludedInTax: z.boolean().optional().nullable(),
        })
        .optional()
        .nullable(),
      ExcludeShipToLocation: z.array(z.string()).optional().nullable(),
    })
    .optional()
    .nullable(),
  // Add ShippingPackageDetails field to support package type selection and dimensions
  ShippingPackageDetails: z
    .object({
      ShippingPackage: z.string().optional().nullable(),
      PackageDepth: z
        .object({ Value: z.number().optional().nullable(), unit: z.string().optional().nullable() })
        .optional()
        .nullable(),
      PackageLength: z
        .object({ Value: z.number().optional().nullable(), unit: z.string().optional().nullable() })
        .optional()
        .nullable(),
      PackageWidth: z
        .object({ Value: z.number().optional().nullable(), unit: z.string().optional().nullable() })
        .optional()
        .nullable(),
      WeightMajor: z
        .object({ Value: z.number().optional().nullable(), unit: z.string().optional().nullable() })
        .optional()
        .nullable(),
      WeightMinor: z
        .object({ Value: z.number().optional().nullable(), unit: z.string().optional().nullable() })
        .optional()
        .nullable(),
      ShippingIrregular: z.boolean().optional().nullable(),
    })
    .optional()
    .nullable(),
  ReturnPolicy: ReturnPolicyTypeSchema.optional().nullable(),
  BestOfferEnabled: z.boolean().optional().nullable(),
  itemDescriptionLexical: z.string().optional().nullable(),
  imageStatus: ImageStatusSchema,
}).superRefine((data, ctx) => {
  // Cross-field validation for pricing rules
  if (data.StartPrice?.Value && data.ReservePrice?.Value && data.ReservePrice.Value > 0) {
    if (data.ReservePrice.Value < data.StartPrice.Value) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['ReservePrice', 'Value'],
        message: 'Reserve price cannot be lower than the starting price.',
      });
    }
  }
});

/**
 * TypeScript type for the listing editor form values
 * Inferred from the Zod schema
 */
export type ListingEditorFormValues = z.infer<typeof ListingEditorFormSchema>;

/**
 * TypeScript type for default values in the listing editor
 * Makes all fields optional and allows null/undefined values
 */
export type ListingEditorDefaultValues = Partial<{
  [K in keyof ListingEditorFormValues]: ListingEditorFormValues[K] | null | undefined;
}>;
