'use client';

import { createMachine, assign, fromPromise } from 'xstate';
import type { Doc, Id } from '@convex/_generated/dataModel';

interface WorkspaceData {
  sourceData?: Doc<'sources'> | null;
  skuData?: Doc<'skus'> | null;
  blueprintData?: Doc<'blueprints'> | null;
}

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface NavigationContext {
  currentPage: string | null;
  breadcrumbTrail: BreadcrumbItem[];
  workspaceData: WorkspaceData;
  // Store the last params used in a NAVIGATE event so we can regenerate breadcrumbs later
  currentParams?: Record<string, string> | null;
  // New context for SKU switching coordination
  pendingSkuSwitch?: {
    skuId: Id<'skus'>;
    sourceId: Id<'sources'>;
    shouldNavigate: boolean;
  };
  currentPathname?: string;
  // Store input functions in context
  setCurrentWorkingSkuId?: (skuId: Id<'skus'> | null) => Promise<void>;
  router?: { push: (url: string) => void };
}

type NavigationEvent =
  | { type: 'NAVIGATE'; page: string; params?: Record<string, string> }
  | { type: 'WORKSPACE_CHANGED'; workspaceData: WorkspaceData }
  // New events for SKU switching coordination
  | { type: 'SWITCH_SKU'; skuId: Id<'skus'>; sourceId: Id<'sources'>; pathname?: string }
  | { type: 'SKU_SWITCH_SUCCESS' }
  | { type: 'SKU_SWITCH_ERROR'; error: string }
  | { type: 'UPDATE_PATHNAME'; pathname: string };

// Breadcrumb generators
const generateWorkbenchBreadcrumbs = (workspaceData: WorkspaceData): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [{ label: 'WorkBench', href: '/workbench' }];

  if (workspaceData?.sourceData) {
    items.push({
      label: workspaceData.sourceData.code,
      href: `/stockroom/skus?source=${workspaceData.sourceData._id}`,
    });

    if (workspaceData?.skuData) {
      items.push({
        label: workspaceData.skuData.fullSku,
        href: `/stockroom/skus/id/${workspaceData.sourceData._id}/${workspaceData.skuData._id}`,
      });
    }
  }

  return items;
};

const generateStockroomBreadcrumbs = (workspaceData: WorkspaceData): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [{ label: 'StockRoom', href: '/stockroom/skus' }];

  if (workspaceData?.sourceData) {
    items.push({
      label: workspaceData.sourceData.code,
      href: `/stockroom/skus?source=${workspaceData.sourceData._id}`,
    });
  }

  return items;
};

const generateSkuDetailBreadcrumbs = (workspaceData: WorkspaceData): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [{ label: 'StockRoom', href: '/stockroom/skus' }];

  if (workspaceData?.sourceData) {
    items.push({
      label: workspaceData.sourceData.code,
      href: `/stockroom/skus?source=${workspaceData.sourceData._id}`,
    });

    if (workspaceData?.skuData) {
      items.push({
        label: workspaceData.skuData.fullSku,
      });
    }
  }

  return items;
};

const generateListingsBreadcrumbs = (
  workspaceData: WorkspaceData,
  params?: Record<string, string>,
): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [{ label: 'Listings', href: '/listings' }];

  if (params?.tab) {
    const tabLabel = params.tab === 'drafts' ? 'Drafts' : params.tab === 'ended' ? 'Ended' : 'Live';
    items.push({ label: tabLabel });
  }

  return items;
};

const generateFoundationsBreadcrumbs = (): BreadcrumbItem[] => {
  return [
    { label: 'Account & Settings', href: '/ebay_account' },
    { label: 'Foundations' },
  ];
};

const generateBlueprintsBreadcrumbs = (): BreadcrumbItem[] => {
  return [
    { label: 'StockRoom', href: '/stockroom/skus' },
    { label: 'Blueprints' },
  ];
};

const generateAdvancedSearchBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Advanced Search' }];
};

const generatePhotoBreadcrumbs = (workspaceData: WorkspaceData): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [{ label: 'Photos', href: '/photos' }];

  if (workspaceData?.sourceData) {
    items.push({
      label: workspaceData.sourceData.code,
      href: `/photos?source=${workspaceData.sourceData._id}`,
    });
  }

  return items;
};

const generateDashboardBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Dashboard' }];
};

// Additional page breadcrumb generators
const generateListingDetailBreadcrumbs = (params?: Record<string, string>): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [{ label: 'Listings', href: '/listings' }];

  if (params?.listingId) {
    items.push({ label: `Listing ${params.listingId}` });
  }

  return items;
};

const generateListingEditBreadcrumbs = (params?: Record<string, string>): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [
    { label: 'Listings', href: '/listings' },
    { label: 'Drafts', href: '/listings?tab=drafts' },
  ];

  if (params?.title) {
    // Truncate title to 40 characters max
    const title = params.title.length > 40 ? params.title.substring(0, 37) + '...' : params.title;
    items.push({ label: title });
  } else if (params?.uuid) {
    items.push({ label: 'Edit Draft' });
  }

  return items;
};

const generateListingQuickBreadcrumbs = (): BreadcrumbItem[] => {
  return [
    { label: 'Listings', href: '/listings' },
    { label: 'Quick Create' },
  ];
};

const generatePolicyEditBreadcrumbs = (
  policyType: string,
  params?: Record<string, string>,
): BreadcrumbItem[] => {
  const policyTypeMap: Record<string, string> = {
    return: 'Return Policies',
    payment: 'Payment Policies',
    fulfillment: 'Fulfillment Policies',
  };

  const items: BreadcrumbItem[] = [
    { label: 'Account & Settings', href: '/ebay_account' },
    { label: 'Policies', href: '/ebay_account/policies' },
    {
      label: policyTypeMap[policyType] || 'Policies',
      href: `/ebay_account/policies/${policyType}-policy`,
    },
  ];

  if (params?.policyId) {
    items.push({ label: 'Edit Policy' });
  }

  return items;
};

// eBay Account breadcrumb generators
const generateEbayAccountBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Account & Settings', href: '/ebay_account' }];
};

const generateTradingAccountProfileBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Account & Settings', href: '/ebay_account' }, { label: 'User Profile' }];
};

const generateUserIdentityBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Account & Settings', href: '/ebay_account' }, { label: 'Identity' }];
};

const generateRateTableBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Account & Settings', href: '/ebay_account' }, { label: 'Rate Tables' }];
};

const generatePrivilegeBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Account & Settings', href: '/ebay_account' }, { label: 'Privileges' }];
};

const generateProgramsBreadcrumbs = (): BreadcrumbItem[] => {
  return [{ label: 'Account & Settings', href: '/ebay_account' }, { label: 'Programs' }];
};

// Policy breadcrumb generators
const generateReturnPolicyBreadcrumbs = (params?: Record<string, string>): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [
    { label: 'Account & Settings', href: '/ebay_account' },
    { label: 'Policies', href: '/ebay_account/policies' },
    { label: 'Return Policies', href: '/ebay_account/policies/return-policy' },
  ];

  if (params?.creating) {
    items.push({ label: 'Create New Policy' });
  }

  return items;
};

const generatePaymentPolicyBreadcrumbs = (params?: Record<string, string>): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [
    { label: 'Account & Settings', href: '/ebay_account' },
    { label: 'Policies', href: '/ebay_account/policies' },
    { label: 'Payment Policies', href: '/ebay_account/policies/payment-policy' },
  ];

  if (params?.creating) {
    items.push({ label: 'Create New Policy' });
  }

  return items;
};

const generateFulfillmentPolicyBreadcrumbs = (
  params?: Record<string, string>,
): BreadcrumbItem[] => {
  const items: BreadcrumbItem[] = [
    { label: 'Account & Settings', href: '/ebay_account' },
    { label: 'Policies', href: '/ebay_account/policies' },
    { label: 'Fulfillment Policies', href: '/ebay_account/policies/fulfillment-policy' },
  ];

  if (params?.creating) {
    items.push({ label: 'Create New Policy' });
  }

  return items;
};

// Generate breadcrumbs based on page and workspace data
const generateBreadcrumbs = (
  page: string,
  workspaceData: WorkspaceData,
  params?: Record<string, string>,
): BreadcrumbItem[] => {
  switch (page) {
    case 'workbench':
      return generateWorkbenchBreadcrumbs(workspaceData);
    case 'stockroom':
      return generateStockroomBreadcrumbs(workspaceData);
    case 'skuDetail':
      return generateSkuDetailBreadcrumbs(workspaceData);
    case 'listings':
      return generateListingsBreadcrumbs(workspaceData, params);
    case 'foundations':
      return generateFoundationsBreadcrumbs();
    case 'blueprints':
      return generateBlueprintsBreadcrumbs();
    case 'advancedSearch':
      return generateAdvancedSearchBreadcrumbs();
    case 'photos':
      return generatePhotoBreadcrumbs(workspaceData);
    case 'dashboard':
      return generateDashboardBreadcrumbs();
    // eBay Account pages
    case 'ebayAccount':
      return generateEbayAccountBreadcrumbs();
    case 'tradingAccountProfile':
      return generateTradingAccountProfileBreadcrumbs();
    case 'userIdentity':
      return generateUserIdentityBreadcrumbs();
    case 'rateTable':
      return generateRateTableBreadcrumbs();
    case 'privilege':
      return generatePrivilegeBreadcrumbs();
    case 'programs':
      return generateProgramsBreadcrumbs();
    // Policy pages
    case 'returnPolicy':
      return generateReturnPolicyBreadcrumbs(params);
    case 'paymentPolicy':
      return generatePaymentPolicyBreadcrumbs(params);
    case 'fulfillmentPolicy':
      return generateFulfillmentPolicyBreadcrumbs(params);
    // Additional pages
    case 'listingDetail':
      return generateListingDetailBreadcrumbs(params);
    case 'listingEdit':
      return generateListingEditBreadcrumbs(params);
    case 'listingQuick':
      return generateListingQuickBreadcrumbs();
    case 'returnPolicyEdit':
      return generatePolicyEditBreadcrumbs('return', params);
    case 'paymentPolicyEdit':
      return generatePolicyEditBreadcrumbs('payment', params);
    case 'fulfillmentPolicyEdit':
      return generatePolicyEditBreadcrumbs('fulfillment', params);
    default:
      return [];
  }
};

// SKU switching coordination services
const createSkuSwitchService = () =>
  fromPromise(
    async ({
      input,
    }: {
      input: {
        skuId: Id<'skus'>;
        sourceId: Id<'sources'>;
        pathname?: string;
        setCurrentWorkingSkuId: (skuId: Id<'skus'> | null) => Promise<void>;
        router?: { push: (url: string) => void };
      };
    }) => {
      const { skuId, sourceId, pathname, setCurrentWorkingSkuId, router } = input;

      // Determine if we need to navigate based on current pathname
      const isOnSkuDetailPage = pathname?.includes('/stockroom/skus/id/');

      if (isOnSkuDetailPage && router) {
        // Navigate to the new SKU's detail page
        router.push(`/stockroom/skus/id/${sourceId}/${skuId}`);
      } else {
        // Just set the context for other pages
        await setCurrentWorkingSkuId(skuId);
      }

      return { success: true, navigated: isOnSkuDetailPage };
    },
  );

export const navigationMachine = createMachine({
  types: {} as {
    context: NavigationContext;
    events: NavigationEvent;
    input: {
      setCurrentWorkingSkuId?: (skuId: Id<'skus'> | null) => Promise<void>;
      router?: { push: (url: string) => void };
    };
  },
  id: 'navigation',
  initial: 'active',
  context: ({ input }) => ({
    currentPage: null,
    breadcrumbTrail: [],
    workspaceData: {},
    currentParams: null,
    currentPathname: undefined,
    pendingSkuSwitch: undefined,
    setCurrentWorkingSkuId: input.setCurrentWorkingSkuId,
    router: input.router,
  }),
  states: {
    active: {
      on: {
        NAVIGATE: {
          actions: assign({
            currentPage: ({ event }) => event.page || null,
            currentParams: ({ event }) => event.params || null,
            breadcrumbTrail: ({ context, event }) =>
              event.page
                ? generateBreadcrumbs(event.page, context.workspaceData, event.params)
                : [],
          }),
        },
        WORKSPACE_CHANGED: {
          actions: assign({
            workspaceData: ({ event }) => event.workspaceData || {},
            breadcrumbTrail: ({ context, event }) =>
              context.currentPage
                ? generateBreadcrumbs(context.currentPage, event.workspaceData || {}, context.currentParams || undefined)
                : [],
          }),
        },
        UPDATE_PATHNAME: {
          actions: assign({
            currentPathname: ({ event }) => event.pathname,
          }),
        },
        SWITCH_SKU: {
          target: 'switchingSku',
          actions: assign({
            pendingSkuSwitch: ({ event }) => ({
              skuId: event.skuId,
              sourceId: event.sourceId,
              shouldNavigate: event.pathname?.includes('/stockroom/skus/id/') || false,
            }),
            currentPathname: ({ event }) => event.pathname || undefined,
          }),
        },
      },
    },
    switchingSku: {
      invoke: {
        src: createSkuSwitchService(),
        input: ({ context }) => ({
          skuId: context.pendingSkuSwitch!.skuId,
          sourceId: context.pendingSkuSwitch!.sourceId,
          pathname: context.currentPathname,
          setCurrentWorkingSkuId: context.setCurrentWorkingSkuId!,
          router: context.router,
        }),
        onDone: {
          target: 'active',
          actions: assign({
            pendingSkuSwitch: undefined,
            // Update workspace data with the new SKU
            workspaceData: ({ context }) => ({
              ...context.workspaceData,
              skuData: null, // Will be updated by the workspace context hooks
            }),
          }),
        },
        onError: {
          target: 'active',
          actions: assign({
            pendingSkuSwitch: undefined,
          }),
        },
      },
    },
  },
});

export type { BreadcrumbItem, WorkspaceData };
