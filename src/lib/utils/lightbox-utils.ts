import { getImageDisplayTitle } from './image-display';
import { parseImageRecognitionData } from './image-ai-parser';

// Interface for image objects that can be used with lightbox
interface LightboxImageSource {
  _id: string;
  bytescaleUrl: string;
  title?: string;
  originalFilename: string;
  imageRecognitionText?: string;
}

// Interface for lightbox slide with metadata
export interface LightboxSlide {
  src: string;
  title?: string;
  description?: string;
}

/**
 * Creates lightbox slides with rich metadata from image objects
 * 
 * @param images Array of image objects to convert to slides
 * @returns Array of lightbox slides with titles and AI-generated descriptions
 */
export function createLightboxSlidesWithMetadata(images: LightboxImageSource[]): LightboxSlide[] {
  return images.map((img) => {
    const title = getImageDisplayTitle(img);
    const aiData = parseImageRecognitionData(img.imageRecognitionText);
    
    let description = '';
    if (aiData) {
      const parts = [];
      if (aiData.identifiedItem) parts.push(`📦 ${aiData.identifiedItem}`);
      if (aiData.brand) parts.push(`🏷️ ${aiData.brand}${aiData.model ? ` ${aiData.model}` : ''}`);
      if (aiData.transcribedText) parts.push(`💬 "${aiData.transcribedText}"`);
      description = parts.join(' • ');
    }
    
    return {
      src: img.bytescaleUrl,
      title,
      description: description || undefined,
    };
  });
}