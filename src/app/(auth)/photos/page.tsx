'use client';

import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '@convex/_generated/api';
import { Id } from '@convex/_generated/dataModel';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { useUser } from '@clerk/nextjs';
import { UnifiedSearchBar } from '@/components/ui/unified-search-bar';
import { EmptyState } from '@/components/ui/empty-state';
import { Search, Trash2, ImageIcon, XCircle, ScanText, FileText, CheckCheck } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { RepositoryUploader } from '@/components/ebay/RepositoryUploader';
import { ImageEditor } from '@/components/ebay/ImageEditor';
// Removed server action import - now using Convex action
import { SelectionDrawer } from '@/components/ebay/SelectionDrawer';
import { ImageSelectionContextMenu } from '@/components/ebay/ImageSelectionContextMenu';
import { useNavigation } from '@/lib/state/NavigationProvider';
import { useCurrentWorkingSkuId, useCurrentWorkingSourceId } from '@/lib/state/useWorkspaceContext';

// --- NEW: Import Lightbox and Styles ---
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';
import Zoom from 'yet-another-react-lightbox/plugins/zoom';
import Captions from 'yet-another-react-lightbox/plugins/captions';
import 'yet-another-react-lightbox/plugins/captions.css';

// Import new AI-enhanced grid
import { AIEnhancedImageGrid } from '@/components/ebay/AIEnhancedImageGrid';
import { createLightboxSlidesWithMetadata, type LightboxSlide } from '@/lib/utils/lightbox-utils';

const log = logger.create('photos:page');

// Context display component to show current filtering
interface ContextDisplayProps {
  sourceDoc?: { code: string; name: string } | null;
  skuDoc?: { fullSku: string; whatIsIt?: string } | null;
  imageCount: number;
  totalImages: number;
  filteredImages: ImageDocument[];
  allImages: ImageDocument[];
}

function ContextDisplay({
  sourceDoc,
  skuDoc,
  imageCount,
  totalImages,
  filteredImages,
  allImages: _allImages,
}: ContextDisplayProps) {

  if (!sourceDoc && !skuDoc) {
    return (
      <div className="mb-4 p-3 bg-muted/50 rounded-lg border">
        <div className="text-sm text-muted-foreground">
          📸 Viewing all images ({imageCount} of {totalImages} images)
        </div>
        <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
          <span>Select a source in the context bar above to filter images</span>
          <span>•</span>
          <div className="flex items-center gap-1">
            <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
            <span>{filteredImages.filter((img) => img.skuId).length} associated</span>
          </div>
          <span>•</span>
          <div className="flex items-center gap-1">
            <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
            <span>{filteredImages.filter((img) => !img.skuId).length} unassigned</span>
          </div>
          <span>•</span>
          <span>{filteredImages.filter((img) => img.imageRecognitionText).length} AI analyzed</span>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-4 p-3 bg-muted/50 rounded-lg border">
      <div className="text-sm font-medium flex items-center gap-2">
        📸 Viewing
        {sourceDoc && (
          <>
            <span className="text-muted-foreground">→</span>
            <span className="text-blue-600">
              {sourceDoc.code} • {sourceDoc.name}
            </span>
          </>
        )}
        {skuDoc && (
          <>
            <span className="text-muted-foreground">→</span>
            <span className="text-green-600">{skuDoc.fullSku}</span>
          </>
        )}
        <span className="text-muted-foreground">
          ({imageCount} of {totalImages} images)
        </span>
      </div>
      <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
        <span>{skuDoc ? `SKU: ${skuDoc.whatIsIt}` : 'Filtered by context selection'}</span>
        <span>•</span>
        <div className="flex items-center gap-1">
          <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
          <span>{filteredImages.filter((img) => img.skuId).length} associated</span>
        </div>
        <span>•</span>
        <div className="flex items-center gap-1">
          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
          <span>{filteredImages.filter((img) => !img.skuId).length} unassigned</span>
        </div>
        <span>•</span>
        <span>{filteredImages.filter((img) => img.imageRecognitionText).length} AI analyzed</span>
      </div>
    </div>
  );
}

interface ImageDocument {
  _id: Id<'images'>;
  _creationTime: number;
  ebayUserId: string;
  sourceId: Id<'sources'>; // Required field for source association
  bytescaleUrl: string;
  bytescaleId: string;
  originalFilename: string;
  title?: string;
  description?: string;
  tags?: string[];
  skuId?: Id<'skus'>; // Proper typing for SKU association
  sortOrder?: number;
  epsUrl?: string;
  epsUploadStatus?: string;
  isFavorite?: boolean;
  uploadedAt: number;
  updatedAt: number;
  imageRecognitionText?: string;
  // Add the missing metadata fields
  width?: number;
  height?: number;
  fileSize?: number;
  mimeType?: string;
  // Soft delete fields
  deletedAt?: number;
  cleanupError?: string;
  lastCleanupAttempt?: number;
}

// Removed DraggableImageProps - using inline props for simplified RepositoryImageCard

// Removed SkuDropTarget - not needed for repository view

export default function ImageRepositoryPage() {
  // Setup navigation
  const { navigate } = useNavigation();
  useEffect(() => {
    navigate('photos');
  }, [navigate]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedImages, setSelectedImages] = useState<Id<'images'>[]>([]);
  const [editImageId, setEditImageId] = useState<Id<'images'> | null>(null);
  // Remove showAllImages state - we'll use SKU presence to determine view mode
  const [newTag, setNewTag] = useState('');
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    tags: [] as string[],
  });
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [imagesToDelete, setImagesToDelete] = useState<Id<'images'>[]>([]);

  // View mode state for the new grid
  const [viewMode, setViewMode] = useState<'compact' | 'rich'>('compact');

  // Lightbox state
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxSlides, setLightboxSlides] = useState<LightboxSlide[]>([]);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Drawer state
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const [activeEbayUserId, setActiveEbayUserId] = useState<string | null>(null);

  // Use global session store for context display
  const { currentWorkingSkuId, setCurrentWorkingSkuId } = useCurrentWorkingSkuId();
  const { currentWorkingSourceId } = useCurrentWorkingSourceId();

  const { user } = useUser();

  const clerkUserId = user?.id;

  const activeEbayAccount = useQuery(
    api.ebayAccounts.getActiveAccount,
    clerkUserId ? { userId: clerkUserId } : 'skip',
  );

  useEffect(() => {
    if (activeEbayAccount?.ebayUserId) {
      setActiveEbayUserId(activeEbayAccount.ebayUserId);
    } else {
      setActiveEbayUserId(null);
    }
  }, [activeEbayAccount]);

  // Note: No need to reset view state since we use SKU presence directly

  // Query current context data for display
  const currentSkuDoc = useQuery(
    api.skus.getSkuDocById,
    currentWorkingSkuId ? { skuId: currentWorkingSkuId } : 'skip',
  );

  const currentSourceDoc = useQuery(
    api.skus.getSourceById,
    currentWorkingSourceId ? { sourceId: currentWorkingSourceId } : 'skip',
  );

  const allImages = useQuery(api.images.getUserImages, activeEbayUserId ? {} : 'skip');

  const deleteImage = useMutation(api.images.deleteImage);
  const updateImageMetadata = useMutation(api.images.updateImageMetadata);
  const cleanupOrphaned = useAction(api.images.cleanupOrphanedRecords);

  // Image selection handler
  const handleToggleImageSelection = useCallback((imageId: Id<'images'>) => {
    setSelectedImages((prev) => {
      const isCurrentlySelected = prev.includes(imageId);
      return isCurrentlySelected ? prev.filter((id) => id !== imageId) : [...prev, imageId];
    });
  }, []);

  const handleBulkDeleteImages = useCallback(async () => {
    if (selectedImages.length === 0) return;

    log.info('Starting bulk delete operation', {
      imageCount: selectedImages.length,
      imageIds: selectedImages,
    });

    try {
      // Show loading toast
      const loadingToast = toast.loading(
        `Deleting ${selectedImages.length} image${selectedImages.length === 1 ? '' : 's'}...`,
      );

      // Delete images and collect results
      const results = await Promise.allSettled(
        selectedImages.map(async (id) => {
          log.info('Deleting image', { imageId: id });
          const result = await deleteImage({ id });
          log.info('Delete result', { imageId: id, result });

          if (!result.success) {
            throw new Error(result.error || 'Unknown deletion error');
          }

          return result;
        }),
      );

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Count successes and failures
      const successful = results.filter((r) => r.status === 'fulfilled').length;
      const failed = results.filter((r) => r.status === 'rejected').length;

      log.info('Bulk delete completed', { successful, failed, total: selectedImages.length });

      if (failed === 0) {
        toast.success(`${successful} image${successful === 1 ? '' : 's'} deleted successfully`);
      } else if (successful === 0) {
        toast.error(`Failed to delete all ${failed} images`);
      } else {
        toast.warning(`${successful} images deleted, ${failed} failed`);
      }

      // Clear selection and close dialog
      setSelectedImages([]);
      setShowDeleteConfirm(false);

      // Log any specific failures
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          log.error('Individual image deletion failed', {
            imageId: selectedImages[index],
            error: result.reason,
          });
        }
      });
    } catch (error) {
      log.error('Failed to delete multiple images', { error, count: selectedImages.length });
      toast.error(
        'Failed to delete images: ' + (error instanceof Error ? error.message : String(error)),
      );
    }
  }, [selectedImages, deleteImage, setShowDeleteConfirm]);

  const handleSaveEditedImage = useCallback(async () => {
    if (!editImageId) return;

    try {
      await updateImageMetadata({
        id: editImageId,
        title: editForm.title,
        description: editForm.description,
        tags: editForm.tags,
      });

      toast.success('Image updated');
      setEditImageId(null);
    } catch (error) {
      log.error('Failed to update image', { error, imageId: editImageId });
      toast.error('Failed to update image');
    }
  }, [editImageId, editForm, updateImageMetadata]);

  const handleEditImage = useCallback(
    (imageId: Id<'images'>) => {
      log.info('[WorkBenchPage] Opening edit dialog for', { imageId });
      const imageToEditData = allImages?.find((img) => img._id === imageId);
      if (imageToEditData) {
        setEditImageId(imageToEditData._id);
        setEditForm({
          title: imageToEditData.title || '',
          description: imageToEditData.description || '',
          tags: imageToEditData.tags || [],
        });
      } else {
        log.warn('Could not find image data for edit', { imageId });
        toast.error('Could not find image data to edit.');
      }
    },
    [allImages, setEditImageId, setEditForm],
  );

  const handleAddTag = useCallback(() => {
    if (!newTag.trim() || editForm.tags.includes(newTag.trim())) return;

    setEditForm((prev) => ({
      ...prev,
      tags: [...prev.tags, newTag.trim()],
    }));
    setNewTag('');
  }, [newTag, editForm.tags]);

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setEditForm((prev) => ({
      ...prev,
      tags: prev.tags.filter((tag) => tag !== tagToRemove),
    }));
  }, []);

  // Removed drag and drop handlers - not needed for repository view

  // Context-aware filtering: show images based on current working context
  const filteredImages = useMemo(() => {
    if (!allImages) return [];

    let contextImages = allImages;

    // Filter by current working source if selected
    if (currentWorkingSourceId) {
      contextImages = contextImages.filter((img) => img.sourceId === currentWorkingSourceId);
      log.debug('Filtering by source', {
        sourceId: currentWorkingSourceId,
        count: contextImages.length,
      });
    }

    // Further filter by current working SKU if selected
    if (currentWorkingSkuId) {
      contextImages = contextImages.filter((img) => img.skuId === currentWorkingSkuId);
      log.debug('Filtering by SKU', { skuId: currentWorkingSkuId, count: contextImages.length });
    }

    // Apply search filters
    return contextImages.filter((image) => {
      const matchesSearch =
        searchQuery === '' ||
        image.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        image.originalFilename.toLowerCase().includes(searchQuery.toLowerCase()) ||
        image.imageRecognitionText?.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesSearch;
    });
  }, [allImages, currentWorkingSourceId, currentWorkingSkuId, searchQuery]);

  // Note: SKU association management moved to workbench - repository is view-only

  // Note: Bulk SKU association removed - use workbench for SKU management

  const handleImagesUpdated = useCallback(() => {}, []);

  // Image editor state
  const [imageToEdit, setImageToEdit] = useState<ImageDocument | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.handleEditComplete = function () {
        log.debug('Edit complete event received');
        window.dispatchEvent(new CustomEvent('image-edited'));
      };

      window.handleEditCancel = function () {
        log.debug('Edit cancel event received');
        window.dispatchEvent(new CustomEvent('image-edit-cancelled'));
      };

      const handleImageEdited = () => {
        log.debug('Setting imageToEdit to null after edit completed');
        setImageToEdit(null);
      };

      const handleEditCancelled = () => {
        log.debug('Setting imageToEdit to null after edit cancelled');
        setImageToEdit(null);
      };

      const handleRepositoryUpdated = () => {
        handleImagesUpdated();
      };

      window.addEventListener('image-edited', handleImageEdited);
      window.addEventListener('image-edit-cancelled', handleEditCancelled);
      window.addEventListener('photos-updated', handleRepositoryUpdated);

      return () => {
        window.removeEventListener('image-edited', handleImageEdited);
        window.removeEventListener('image-edit-cancelled', handleEditCancelled);
        window.removeEventListener('photos-updated', handleRepositoryUpdated);
      };
    }
  }, [handleImagesUpdated, setImageToEdit]);

  const handleOpenImageEditor = useCallback(
    (imageId: Id<'images'>) => {
      log.info('[WorkBenchPage] Opening Pintura image editor for', { imageId });
      const imageToEditData = allImages?.find((img) => img._id === imageId);
      if (imageToEditData) {
        setImageToEdit(imageToEditData as ImageDocument);
      } else {
        log.warn('Could not find image data for Pintura editor', { imageId });
        toast.error('Could not find image data to edit.');
      }
    },
    [allImages, setImageToEdit],
  );

  // Preview single image handler
  const handlePreviewImage = useCallback(
    (imageOrId: ImageDocument | Id<'images'>) => {
      // Handle both image object and ID
      const clickedImage =
        typeof imageOrId === 'string' ? allImages?.find((img) => img._id === imageOrId) : imageOrId;

      if (!clickedImage) {
        toast.error('Image not found');
        return;
      }

      // If we have a SKU selected, load all images for that SKU
      if (currentWorkingSkuId && allImages) {
        const skuImages = allImages.filter((img) => img.skuId === currentWorkingSkuId);
        if (skuImages.length > 0) {
          const slides = createLightboxSlidesWithMetadata(skuImages);
          const clickedIndex = skuImages.findIndex((img) => img._id === clickedImage._id);
          setLightboxSlides(slides);
          setLightboxIndex(clickedIndex >= 0 ? clickedIndex : 0);
          setLightboxOpen(true);
          return;
        }
      }

      // Default: single image preview
      setLightboxSlides(createLightboxSlidesWithMetadata([clickedImage]));
      setLightboxIndex(0);
      setLightboxOpen(true);
    },
    [allImages, currentWorkingSkuId],
  );

  const trayImages = useMemo(() => {
    if (!allImages) return [];
    return allImages.map((img) => ({ _id: img._id, bytescaleUrl: img.bytescaleUrl }));
  }, [allImages]);

  const handlePreviewSelection = useCallback(() => {
    if (selectedImages.length === 0 || !allImages) {
      toast.error('No images selected for preview.');
      return;
    }
    const selectedSet = new Set(selectedImages);
    const imagesToPreviewData = allImages.filter((img) => selectedSet.has(img._id));
    if (imagesToPreviewData.length === 0) {
      log.error('Could not find any image data for the selected IDs');
      toast.error('Could not find data for selected images.');
      return;
    }
    const slides = createLightboxSlidesWithMetadata(imagesToPreviewData);
    log.info(`Opening lightbox for ${slides.length} selected image(s)`);
    setIsDrawerOpen(false);
    setLightboxSlides(slides);
    setLightboxIndex(0);
    setLightboxOpen(true);
  }, [selectedImages, allImages, setIsDrawerOpen]);

  // Convex action for individual image recognition (replaces server action)
  const runImageRecognition = useAction(api.images.runImageRecognitionOnImage);

  const handleBulkImageRecognition = useCallback(async (imageIds: Id<'images'>[]) => {
    if (imageIds.length === 0) return;
    log.info('Running User-Initiated Bulk Image Recognition', { count: imageIds.length });
    const toastId = toast.loading(`Running AI analysis for ${imageIds.length} image(s)...`);
    let successCount = 0;
    let failCount = 0;
    let alreadyProcessedCount = 0;
    // User-initiated actions always force re-run
    const results = await Promise.allSettled(
      imageIds.map((id) => runImageRecognition({ imageId: id, forceRerun: true })),
    );
    results.forEach((result, index) => {
      const imageId = imageIds[index];
      if (result.status === 'fulfilled') {
        log.info(`AI analysis finished for image ${imageId}`, { result: result.value });
        if (result.value.imageRecognitionText) {
          successCount++;
        } else if (result.value.alreadyProcessed) {
          alreadyProcessedCount++;
        } else {
          log.warn(
            'Image recognition fulfilled but no new text and not marked as already processed',
            { imageId, resultValue: result.value },
          );
        }
      } else {
        failCount++;
        log.error(`AI analysis failed for image ${imageId}`, { error: result.reason });

        // Log specific error types for debugging
        if (result.reason instanceof Error) {
          if (result.reason.message.includes('JSON') || result.reason.message.includes('parse')) {
            log.info(`Image ${imageId} failed due to complex content (JSON parsing issue)`);
          }
        }
      }
    });
    let message = '';
    if (successCount > 0) message += `${successCount} succeeded. `;
    if (alreadyProcessedCount > 0) message += `${alreadyProcessedCount} already processed. `;
    if (failCount > 0) message += `${failCount} failed. `;
    if (failCount > 0 || (successCount === 0 && alreadyProcessedCount === 0)) {
      toast.error(`AI Analysis Batch Result: ${message.trim()}`, { id: toastId, duration: 5000 });
    } else if (successCount > 0 || alreadyProcessedCount > 0) {
      toast.success(`AI Analysis Batch Result: ${message.trim()}`, { id: toastId });
    } else {
      toast.info('No images required AI analysis processing.', { id: toastId });
    }
  }, [runImageRecognition]);

  // NEW: Handle batch processing of all unrecognized images
  const triggerBatchRecognition = useAction(api.images.triggerBatchImageRecognition);

  const handleBatchProcessUnrecognized = useCallback(
    async (forceReprocess = false) => {
      const toastId = toast.loading(
        forceReprocess
          ? 'Processing AI analysis for recent images (including already processed)...'
          : 'Processing AI analysis for unrecognized images...',
      );

      try {
        const result = await triggerBatchRecognition({
          limit: 20,
          forceReprocess,
        });

        if (result.success) {
          const message = `Batch completed: ${result.processed} processed, ${result.failed} failed, ${result.skipped} skipped`;
          if (result.failed > 0) {
            toast.warning(message, { id: toastId, duration: 6000 });
          } else {
            toast.success(message, { id: toastId });
          }
        } else {
          toast.error('Batch processing failed', { id: toastId });
        }
      } catch (error) {
        log.error('Failed to trigger batch processing', { error });
        toast.error('Failed to start batch processing', { id: toastId });
      }
    },
    [triggerBatchRecognition],
  );

  const handleClearSelection = useCallback(() => {
    setSelectedImages([]);
    log.info('Selection cleared via button (drawer remains open)');
  }, []);

  if (!activeEbayUserId) {
    return <div className="p-8 text-center">Loading eBay account information...</div>;
  }

  if (!allImages) {
    return <div className="p-8 text-center">Loading Photos...</div>;
  }

  return (
    <>
      <div className="relative min-h-screen pb-24">
        <div className="py-4 md:py-8 px-4 md:px-8">
          <div className="mb-8">
            <div className="flex justify-between items-center mb-6">
              <div></div>
            </div>

            {/* Context Display */}
            <ContextDisplay
              sourceDoc={currentSourceDoc}
              skuDoc={currentSkuDoc}
              imageCount={filteredImages.length}
              totalImages={allImages?.length || 0}
              filteredImages={filteredImages}
              allImages={allImages || []}
            />

            {/* Compact Upload Section */}
            {activeEbayUserId && currentSourceDoc && (
              <div
                id="upload-section"
                className="mb-4 p-3 border rounded-lg bg-muted/30 flex items-center justify-between"
              >
                <span className="text-sm font-medium">Upload to {currentSourceDoc.code}</span>
                <RepositoryUploader
                  ebayUserId={activeEbayUserId}
                  uploadCompleteEventName="photos-updated"
                  sourceId={currentWorkingSourceId || undefined}
                />
              </div>
            )}

            {!currentSourceDoc && (
              <div className="mb-4 p-3 border rounded-lg bg-muted/30 text-center">
                <span className="text-sm text-muted-foreground">
                  Select a source above to upload images
                </span>
              </div>
            )}

            <div className="space-y-4">
              {/* Unified Search Bar */}
              <UnifiedSearchBar>
                <div className="flex gap-4 items-center">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search images..."
                      className="pl-8"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>

                  {/* View mode toggles */}
                  <div className="flex gap-1 border rounded-md p-1">
                    <Button
                      variant={viewMode === 'compact' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('compact')}
                      title="Compact grid view"
                      className="px-3"
                    >
                      <svg
                        className="h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <rect x="3" y="3" width="7" height="7" strokeWidth="2" />
                        <rect x="14" y="3" width="7" height="7" strokeWidth="2" />
                        <rect x="3" y="14" width="7" height="7" strokeWidth="2" />
                        <rect x="14" y="14" width="7" height="7" strokeWidth="2" />
                      </svg>
                    </Button>
                    <Button
                      variant={viewMode === 'rich' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('rich')}
                      title="Rich metadata view"
                      className="px-3"
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Show "View All in Source" button only when both source and SKU are selected */}
                  {currentSourceDoc && currentWorkingSkuId && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentWorkingSkuId(null)}
                      title={`View all images in ${currentSourceDoc.name}`}
                    >
                      View All in {currentSourceDoc.code}
                    </Button>
                  )}
                  {searchQuery && (
                    <Button variant="ghost" size="sm" onClick={() => setSearchQuery('')}>
                      Clear Search
                    </Button>
                  )}
                  {/* NEW: Batch AI Processing Buttons */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBatchProcessUnrecognized(false)}
                    title="Process AI analysis for images that haven't been analyzed yet"
                  >
                    <ScanText className="h-4 w-4 mr-1" />
                    Batch AI (New)
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBatchProcessUnrecognized(true)}
                    title="Reprocess AI analysis for all recent images"
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    Batch AI (All)
                  </Button>
                  {/* NEW: Cleanup Orphaned Records Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={async () => {
                      const loadingToast = toast.loading('Cleaning up orphaned records...');
                      try {
                        const result = await cleanupOrphaned();
                        toast.dismiss(loadingToast);
                        if (result.success) {
                          toast.success(`Cleaned up ${result.cleaned} orphaned records`);
                          if (result.errors.length > 0) {
                            toast.warning(`${result.errors.length} errors occurred during cleanup`);
                          }
                        } else {
                          toast.error('Cleanup failed');
                        }
                      } catch (error) {
                        toast.dismiss(loadingToast);
                        toast.error('Cleanup failed: ' + String(error));
                      }
                    }}
                    title="Clean up database records for images that no longer exist in Bytescale"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    Cleanup Orphaned
                  </Button>
                </div>
              </UnifiedSearchBar>

              {/* Always-visible hint bar */}
              <div className="p-2 border rounded-lg bg-muted/20 flex items-center justify-between text-sm text-muted-foreground">
                {selectedImages.length > 0 ? (
                  <>
                    <span>
                      {selectedImages.length} image{selectedImages.length !== 1 ? 's' : ''} selected
                    </span>
                    <span className="text-xs">Right-click for actions</span>
                  </>
                ) : (
                  <>
                    <span>Click images to select, then right-click for bulk actions</span>
                    <span className="text-xs opacity-75">Tip: Right-click works too</span>
                  </>
                )}
              </div>

              {/* Show sections only if we have images or are filtering */}
              {filteredImages.length > 0 ? (
                <ImageSelectionContextMenu
                  selectedImageIds={selectedImages}
                  isCreatingSku={false}
                  activeSkuId={null}
                  onCreateSku={() => {}} // Disabled - use workbench for SKU creation
                  onAssociate={() => {}} // Disabled - use workbench for associations
                  onDelete={() => {
                    setImagesToDelete(selectedImages);
                    setShowDeleteConfirm(true);
                  }}
                  onEdit={handleEditImage}
                  onAdvancedEdit={handleOpenImageEditor}
                  onRunImageRecognition={handleBulkImageRecognition}
                  onPreviewSelection={handlePreviewSelection}
                  onClearSelection={() => setSelectedImages([])}
                  onTriggerSkuSelection={() => {}} // Disabled - use workbench
                >
                  <AIEnhancedImageGrid
                    images={filteredImages}
                    selectedIds={selectedImages}
                    onToggleSelect={handleToggleImageSelection}
                    onDoubleClick={handlePreviewImage}
                    viewMode={viewMode}
                  />
                </ImageSelectionContextMenu>
              ) : (
                <EmptyState
                  icon={ImageIcon}
                  title="No images found"
                  context={currentSourceDoc ? `in ${currentSourceDoc.name}` : undefined}
                  description={
                    searchQuery
                      ? 'Try adjusting your search query'
                      : 'Upload some images to get started'
                  }
                  action={
                    searchQuery
                      ? {
                          label: 'Clear Search',
                          onClick: () => setSearchQuery(''),
                          variant: 'outline',
                        }
                      : undefined
                  }
                />
              )}
            </div>
          </div>
        </div>

        {/* Floating Action Button to Open Drawer */}
        {selectedImages.length > 0 && (
          <Button
            className="fixed bottom-6 right-6 z-40 h-12 w-12 rounded-full shadow-lg"
            size="icon"
            onClick={() => setIsDrawerOpen(true)}
            title={`Actions for ${selectedImages.length} selected images`}
          >
            <CheckCheck className="h-6 w-6" />
          </Button>
        )}
      </div>

      {/* Lightbox */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        slides={lightboxSlides}
        index={lightboxIndex}
        plugins={[Zoom, Captions]}
        zoom={{
          scrollToZoom: true,
          maxZoomPixelRatio: 5,
          zoomInMultiplier: 2,
          doubleTapDelay: 300,
          doubleClickDelay: 300,
          doubleClickMaxStops: 2,
          keyboardMoveDistance: 50,
          wheelZoomDistanceFactor: 100,
          pinchZoomDistanceFactor: 100,
        }}
      />

      {/* Selection Drawer */}
      <SelectionDrawer
        isOpen={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
        selectedImageIds={selectedImages}
        allImages={trayImages}
        activeSkuId={null} // Repository view is read-only for SKU associations
        isCreatingSku={false}
        onAssociate={() => {}} // Disabled - use workbench for associations
        onDelete={() => {
          setImagesToDelete(selectedImages);
          setShowDeleteConfirm(true);
        }}
        onEdit={handleEditImage}
        onAdvancedEdit={handleOpenImageEditor}
        onRunImageRecognition={handleBulkImageRecognition}
        onPreviewSelection={handlePreviewSelection}
        onJustClear={handleClearSelection}
        onTriggerSkuSelection={() => {}} // Disabled - use workbench
        onCreateSkuFromSelection={() => {}} // Disabled - use workbench
      />

      <Dialog open={!!editImageId} onOpenChange={(open) => !open && setEditImageId(null)}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Image</DialogTitle>
            <DialogDescription>Update the metadata for this image</DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Title</label>
              <Input
                value={editForm.title}
                onChange={(e) => setEditForm((prev) => ({ ...prev, title: e.target.value }))}
                placeholder="Image title"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea
                value={editForm.description}
                onChange={(e) => setEditForm((prev) => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Tags</label>
              <div className="flex flex-wrap gap-1 mb-2">
                {editForm.tags.length > 0 ? (
                  editForm.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <XCircle
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </Badge>
                  ))
                ) : (
                  <span className="text-xs text-muted-foreground">No tags added yet</span>
                )}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add a tag"
                  onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                />
                <Button type="button" size="sm" onClick={handleAddTag} disabled={!newTag.trim()}>
                  Add
                </Button>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setEditImageId(null)}>
              Cancel
            </Button>
            <Button onClick={handleSaveEditedImage}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={showDeleteConfirm}
        onOpenChange={(open) => !open && setShowDeleteConfirm(false)}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {imagesToDelete.length} image
              {imagesToDelete.length === 1 ? '' : 's'}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleBulkDeleteImages}>
              Delete {imagesToDelete.length} Image{imagesToDelete.length === 1 ? '' : 's'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Image Editor */}
      {imageToEdit && (
        <ImageEditor
          image={imageToEdit}
          onCompleteId="handleEditComplete"
          onCancelId="handleEditCancel"
          isOpen={!!imageToEdit}
        />
      )}
    </>
  );
}
