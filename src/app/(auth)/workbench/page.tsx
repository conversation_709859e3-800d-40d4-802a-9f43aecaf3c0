'use client';

// Imports from React, Convex, Clerk, Shadcn UI, DND Kit, etc.
import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { useQuery, useMutation, useConvex, useAction } from 'convex/react';
import { useUser } from '@clerk/nextjs';
import { api } from '@convex/_generated/api';
import { Doc, Id } from '@convex/_generated/dataModel';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { PackageIcon, CheckCheck, ImageIcon } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
// Remove individual context menu imports - using shared component now
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

// --- NEW: Import Lightbox and Styles ---
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';
import Zoom from 'yet-another-react-lightbox/plugins/zoom';
import Captions from 'yet-another-react-lightbox/plugins/captions';
import 'yet-another-react-lightbox/plugins/captions.css';

// Import the breadcrumb hook
import { useNavigation } from '@/lib/state/NavigationProvider';

// Import session state hooks
import {
  useCurrentWorkingSkuId,
  useCurrentWorkingSourceId,
  useCurrentWorkingBlueprintId,
} from '@/lib/state/useWorkspaceContext';
import { EmptyState } from '@/components/ui/empty-state';

// Reusable Components
import { RepositoryUploader } from '@/components/ebay/RepositoryUploader';
import { SkuManager } from '@/components/ebay/SkuManager';
import { ImageCard, ImageDocumentForCard } from '@/components/ebay/ImageCard';
import { SelectionDrawer } from '@/components/ebay/SelectionDrawer';
import { ImageEditor } from '@/components/ebay/ImageEditor';
import { ImageSelectionContextMenu } from '@/components/ebay/ImageSelectionContextMenu';
import { createLightboxSlidesWithMetadata, type LightboxSlide } from '@/lib/utils/lightbox-utils';
// Removed server action import - now using Convex action

const log = logger.create('workbench');

// --- Quick SKU Dialog Component (Isolated to prevent re-renders) ---
interface QuickSkuDialogProps {
  isOpen: boolean;
  selectedImageCount: number;
  onClose: () => void;
  onSubmit: (inputValue: string) => void;
}

const QuickSkuDialog = React.memo(
  ({ isOpen, selectedImageCount, onClose, onSubmit }: QuickSkuDialogProps) => {
    // Move input state into this component to prevent parent re-renders
    const [inputValue, setInputValue] = useState('');

    const dialogTitle = useMemo(() => {
      return `Create SKU from ${selectedImageCount} Image${selectedImageCount === 1 ? '' : 's'}`;
    }, [selectedImageCount]);

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && inputValue.trim()) {
          e.preventDefault();
          onSubmit(inputValue);
        } else if (e.key === 'Escape') {
          e.preventDefault();
          setInputValue('');
          onClose();
        }
      },
      [inputValue, onSubmit, onClose],
    );

    const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);
    }, []);

    const handleClose = useCallback(() => {
      setInputValue('');
      onClose();
    }, [onClose]);

    const handleSubmit = useCallback(() => {
      onSubmit(inputValue);
    }, [inputValue, onSubmit]);

    // Reset input when dialog opens
    useEffect(() => {
      if (isOpen) {
        setInputValue('');
      }
    }, [isOpen]);

    if (!isOpen) return null;

    return (
      <Dialog
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) {
            handleClose();
          }
        }}
      >
        <DialogContent
          className="sm:max-w-[425px]"
          onOpenAutoFocus={() => {
            // Let the dialog handle initial focus, then focus our input
            setTimeout(() => {
              const input = document.querySelector('[data-sku-input]') as HTMLInputElement;
              if (input) {
                input.focus();
                input.select();
              }
            }, 100);
          }}
        >
          <DialogHeader>
            <DialogTitle>{dialogTitle}</DialogTitle>
            <DialogDescription>Type what this item is to create a new SKU.</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              data-sku-input
              value={inputValue}
              onChange={handleInputChange}
              placeholder="e.g., Red T-Shirt, Vintage Camera, Set of 3 Books..."
              onKeyDown={handleKeyDown}
              className="text-base"
            />
            <p className="text-xs text-muted-foreground mt-2">
              Press <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Enter</kbd> to create •{' '}
              <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd> to cancel
            </p>
          </div>
          <DialogFooter className="gap-2">
            <Button variant="outline" onClick={handleClose} size="sm">
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!inputValue.trim()}
              size="sm"
              className="min-w-[100px]"
            >
              Create SKU
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  },
);

QuickSkuDialog.displayName = 'QuickSkuDialog';

// --- Main Workbench Page Component ---
export default function WorkBenchPage() {
  // Debug: Track renders
  const renderCount = useRef(0);
  renderCount.current += 1;
  console.log('WorkBenchPage render #', renderCount.current);

  // Set up navigation for this page
  const { navigate } = useNavigation();

  useEffect(() => {
    navigate('workbench');
  }, [navigate]);

  // Global session state for current working SKU
  const { currentWorkingSkuId, setCurrentWorkingSkuId } = useCurrentWorkingSkuId();
  const { currentWorkingSourceId } = useCurrentWorkingSourceId();
  const { currentWorkingBlueprintId } = useCurrentWorkingBlueprintId();

  // Local state
  const [activeEbayUserId, setActiveEbayUserId] = useState<string | null>(null);
  const [selectedImages, setSelectedImages] = useState<Id<'images'>[]>([]);

  // State for drawer visibility
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Restore state for Edit and Delete actions
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [imagesToDelete, setImagesToDelete] = useState<Id<'images'>[]>([]);
  const [editImageId, setEditImageId] = useState<Id<'images'> | null>(null);
  const [editForm, setEditForm] = useState({
    title: '',
    description: '',
    tags: [] as string[],
  });

  // State for Lightbox (adapted from ImageViewer state)
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxSlides, setLightboxSlides] = useState<LightboxSlide[]>([]);

  // State for Pintura Image Editor
  const [imageToEdit, setImageToEdit] = useState<Doc<'images'> | null>(null);

  // State for quick SKU creation (right-click)
  const [showQuickSkuDialog, setShowQuickSkuDialog] = useState(false);
  const [isCreatingSku, setIsCreatingSku] = useState(false);

  // Clerk User
  const { user } = useUser();
  const convex = useConvex();
  
  // Convex action for image recognition (replaces server action)
  const runImageRecognition = useAction(api.images.runImageRecognitionOnImage);

  // Fetch active eBay account
  const activeEbayAccount = useQuery(
    api.ebayAccounts.getActiveAccount,
    user?.id ? { userId: user.id } : 'skip',
  );

  useEffect(() => {
    if (activeEbayAccount?.ebayUserId) {
      setActiveEbayUserId(activeEbayAccount.ebayUserId);
    }
  }, [activeEbayAccount]);

  // Debug effect to track selection changes
  useEffect(() => {
    console.log('Selection state changed', {
      selectedCount: selectedImages.length,
      isCreatingSku,
      currentWorkingSkuId: currentWorkingSkuId?.toString(),
    });
  }, [selectedImages.length, isCreatingSku, currentWorkingSkuId]);

  // Get the default JDAI source when no source is selected
  const defaultSource = useQuery(
    api.skus.getSourceByCode,
    !currentWorkingSourceId && activeEbayUserId ? { code: 'JDAI' } : 'skip',
  );

  // Mutations to ensure default source and blueprints exist for new users
  const ensureDefaultSourceMutation = useMutation(api.skus.ensureDefaultSource);
  const ensureDefaultBlueprintsMutation = useMutation(api.blueprints.ensureDefaultBlueprints);

  // Effect to create default source and blueprints for new users if they don't exist
  useEffect(() => {
    // If user has eBay account but no source selected and no default source found
    if (activeEbayUserId && !currentWorkingSourceId && defaultSource === null) {
      console.log('Creating default source for new user');
      ensureDefaultSourceMutation()
        .then(() => {
          console.log('Default source created successfully');
        })
        .catch((error) => {
          console.error('Failed to create default source:', error);
        });
    }

    // Ensure default blueprints exist for any user with an eBay account
    if (activeEbayUserId) {
      ensureDefaultBlueprintsMutation()
        .then(() => {
          console.log('Default blueprints ensured');
        })
        .catch((error) => {
          console.error('Failed to ensure default blueprints:', error);
        });
    }
  }, [
    activeEbayUserId,
    currentWorkingSourceId,
    defaultSource,
    ensureDefaultSourceMutation,
    ensureDefaultBlueprintsMutation,
  ]);

  // Determine which source to use: current selection or default JDAI
  const effectiveSourceId = currentWorkingSourceId || defaultSource?._id;

  // Fetch images for the effective source (current selection or JDAI default)
  const allImages = useQuery(
    api.images.getSourceImages,
    effectiveSourceId ? { sourceId: effectiveSourceId } : 'skip',
  );

  // Fetch current source data for display (using effective source)
  const sourceData = useQuery(
    api.skus.getSourceById,
    effectiveSourceId ? { sourceId: effectiveSourceId } : 'skip',
  );

  // Fetch current SKU data for displaying SKU name in context menu
  const currentSkuData = useQuery(
    api.skus.getSkuDocById,
    currentWorkingSkuId ? { skuId: currentWorkingSkuId } : 'skip',
  );

  // Prepare simplified image data for the tray (only needs id and url)
  const trayImages = useMemo(() => {
    if (!allImages) return [];
    return allImages.map((img) => ({ _id: img._id, bytescaleUrl: img.bytescaleUrl }));
  }, [allImages]);

  // --- REPLACE useMemo with useQuery for skuImageMap ---
  const skuImageMap =
    useQuery(
      api.skus.getSkuThumbnails,
      // Skip if no active user, query doesn't need other args
      activeEbayUserId ? {} : 'skip',
    ) ?? {}; // Use empty object as default if query returns undefined/null

  // Separate unassigned images into parents and regular unassigned
  const { parentImages, unassignedImages } = useMemo(() => {
    if (!allImages) return { parentImages: [], unassignedImages: [] };

    const unassigned = allImages.filter((image) => !image.skuId);
    const parents: Doc<'images'>[] = [];
    const regular: Doc<'images'>[] = [];

    // For a single source, this is fine - sources should have manageable image counts
    unassigned.forEach((image) => {
      // Check if this image has children (has been edited/cropped)
      const hasChildren = allImages.some((otherImage) => otherImage.parentImageId === image._id);
      if (hasChildren) {
        parents.push(image);
      } else {
        regular.push(image);
      }
    });

    console.log('Image separation calculation', {
      currentSource: currentWorkingSourceId?.toString() || 'none',
      totalImages: allImages.length,
      unassignedCount: regular.length,
      parentCount: parents.length,
      assignedCount: allImages.length - unassigned.length,
    });

    return { parentImages: parents, unassignedImages: regular };
  }, [allImages, currentWorkingSourceId]);

  // Mutations
  const associateImageMutation = useMutation(api.images.associateImageWithSku);
  const ensureSkuMutation = useMutation(api.skus.ensureSku);
  // Restore mutations needed for delete and edit
  const bulkDeleteImages = useMutation(api.images.deleteImage);
  const bulkArchiveImages = useMutation(api.images.archiveImage);
  const updateImageMetadata = useMutation(api.images.updateImageMetadata);
  const logImageRecognitionToSkuMutation = useMutation(api.skuMutations.logImageRecognitionResult);

  // Get all tags (simple version for stub)

  // Use a ref to store the current isCreatingSku state to avoid stale closures
  const isCreatingSkuRef = useRef(isCreatingSku);
  isCreatingSkuRef.current = isCreatingSku;

  // Callback to toggle image selection
  const handleToggleImageSelection = useCallback((imageId: Id<'images'>) => {
    console.log('handleToggleImageSelection called', {
      imageId,
      isCreatingSku: isCreatingSkuRef.current,
      timestamp: Date.now(),
    });

    // Prevent selection changes during SKU creation
    if (isCreatingSkuRef.current) {
      console.log('Image selection blocked - SKU creation in progress');
      return;
    }

    setSelectedImages((prev) => {
      const isCurrentlySelected = prev.includes(imageId);
      const newSelection = isCurrentlySelected
        ? prev.filter((id) => id !== imageId)
        : [...prev, imageId];

      console.log('Image selection state update', {
        imageId,
        wasSelected: isCurrentlySelected,
        prevCount: prev.length,
        newSelectionCount: newSelection.length,
        newSelection: newSelection.map((id) => id.toString()),
      });

      return newSelection;
    });
  }, []); // Empty dependency array since we're using refs

  // Implement Bulk Delete Logic (without confirmation first)

  // Implement Bulk Associate Logic (Minimal Trigger)
  const handleBulkAssociate = useCallback(async () => {
    if (selectedImages.length === 0 || !currentWorkingSkuId || !allImages) {
      if (!currentWorkingSkuId) {
        toast.error(`No SKU selected. Please select a valid SKU first.`);
      } else if (selectedImages.length === 0) {
        toast.error('No images selected for association.');
      } else if (!allImages) {
        toast.error('Image data is not loaded yet.');
      }
      return;
    }

    const imageCount = selectedImages.length;
    const targetSkuId = currentWorkingSkuId;
    const imageIdsToAssociate = [...selectedImages];

    // --- Get image data for potential EPS trigger ---
    const selectedSet = new Set(imageIdsToAssociate);
    const imagesDataToAssociate = allImages.filter((img) => selectedSet.has(img._id));
    // --- End get image data ---

    log.info('Bulk Associate Action', { count: imageCount, skuId: targetSkuId });

    const toastId = toast.loading(`Associating ${imageCount} image(s) with SKU...`);
    setSelectedImages([]); // Clear selection immediately

    let associationSuccessCount = 0;
    let associationFailCount = 0;

    try {
      // --- Step 1: Associate images with SKU in Convex ---
      const associationResults = await Promise.allSettled(
        imageIdsToAssociate.map((imageId) =>
          associateImageMutation({
            imageId,
            skuId: targetSkuId,
          }),
        ),
      );

      // --- Step 2: Trigger EPS upload for successfully associated images ---
      associationResults.forEach((result, index) => {
        const imageId = imageIdsToAssociate[index];
        if (result.status === 'fulfilled') {
          associationSuccessCount++;
          log.info(`Successfully associated image ${imageId} with SKU ${targetSkuId}`);

          // Trigger EPS upload using Convex action
          const imageData = imagesDataToAssociate.find((img) => img._id === imageId);
          if (imageData && imageData.bytescaleUrl && user?.id && activeEbayUserId) {
            log.info(`Triggering EPS upload via Convex action for associated image ${imageId}`);
            convex
              .action(api.epsUploadService.uploadToEPS, {
                imageIds: [imageData._id],
                ebayUserId: activeEbayUserId,
                userId: user.id,
              })
              .catch((err: unknown) => {
                // Log only if the *trigger itself* fails immediately
                log.error(`Failed to invoke EPS upload Convex action for ${imageId}`, {
                  error: err,
                });
              });

            // If recognition already exists, log it to SKU activity; otherwise trigger recognition
            if (imageData.imageRecognitionText) {
              // Log existing recognition to SKU activity since it wasn't logged when originally done
              log.info(`Logging existing image recognition to SKU activity for ${imageId}`);
              logImageRecognitionToSkuMutation({
                skuId: targetSkuId,
                imageId: imageData._id,
                imageRecognitionText: imageData.imageRecognitionText,
              }).catch((err: unknown) => {
                log.error(`Failed to log existing recognition to SKU activity for ${imageId}`, {
                  error: err,
                });
              });
            } else {
              log.info(`Triggering image recognition for newly associated image ${imageId}`);
              runImageRecognition({ imageId: imageData._id }).catch((err: unknown) => {
                log.error(`Failed to trigger image recognition for ${imageId}`, { error: err });
              });
            }
          } else {
            log.warn(
              `Could not find image data or bytescaleUrl for ${imageId}, cannot trigger EPS upload.`,
            );
          }
        } else {
          associationFailCount++;
          log.error(`Failed to associate image ${imageId} with SKU`, { error: result.reason });
        }
      });

      // --- Simple Feedback ---
      if (associationSuccessCount > 0) {
        toast.success(`${associationSuccessCount} image(s) associated. EPS uploads initiated.`, {
          id: toastId,
        });
      } else {
        toast.error('Failed to associate any images.', { id: toastId });
      }
      if (associationFailCount > 0) {
        toast.warning(`${associationFailCount} image(s) failed to associate.`);
      }
    } catch (error) {
      // Catch unexpected errors from Promise.allSettled itself (unlikely)
      log.error('Unexpected error during bulk association', {
        error,
        count: imageCount,
        skuId: targetSkuId,
      });
      toast.error('An unexpected error occurred during association.', { id: toastId });
    }
  }, [
    selectedImages,
    currentWorkingSkuId,
    associateImageMutation,
    setSelectedImages,
    allImages,
    logImageRecognitionToSkuMutation,
    user?.id,
    activeEbayUserId,
    convex,
    runImageRecognition,
  ]);

  // Handler: Opens Lightbox with ONLY the selected images
  const handlePreviewSelection = useCallback(() => {
    if (selectedImages.length === 0 || !allImages) {
      toast.error('No images selected for preview.');
      return;
    }

    // Filter allImages to get the full data for selected IDs
    const selectedSet = new Set(selectedImages);
    const imagesToPreviewData = allImages.filter((img) => selectedSet.has(img._id));

    if (imagesToPreviewData.length !== selectedImages.length) {
      log.warn('Mismatch between selected IDs and found image data for preview');
    }

    if (imagesToPreviewData.length === 0) {
      log.error('Could not find any image data for the selected IDs');
      toast.error('Could not find data for selected images.');
      return;
    }

    // Format URLs into slides for the lightbox with metadata
    const slides = createLightboxSlidesWithMetadata(imagesToPreviewData);

    log.info(`Opening lightbox for ${slides.length} selected image(s)`);
    setIsDrawerOpen(false); // Close the drawer first
    setLightboxSlides(slides); // Set slides for the lightbox
    setLightboxOpen(true); // THEN open the lightbox
  }, [selectedImages, allImages, setIsDrawerOpen]); // Add setIsDrawerOpen dependency

  // Restore the bulk delete handler
  const handleBulkDeleteImages = useCallback(async () => {
    if (selectedImages.length === 0 && imagesToDelete.length === 0) return;
    // Use imagesToDelete if set (from confirmation), otherwise use selectedImages
    const imageIdsToDelete = imagesToDelete.length > 0 ? [...imagesToDelete] : [...selectedImages];
    const imageCount = imageIdsToDelete.length;

    if (imageCount === 0) return; // Double check we have images

    log.info('Bulk Delete Action', { count: imageCount });
    const toastId = toast.loading(`Deleting ${imageCount} image(s)...`);
    setSelectedImages([]); // Clear general selection
    setImagesToDelete([]); // Clear specific delete list
    setShowDeleteConfirm(false); // Close dialog

    try {
      await Promise.all(imageIdsToDelete.map((id) => bulkDeleteImages({ id })));
      toast.success(`${imageCount} image(s) deleted successfully.`, { id: toastId });
    } catch (error) {
      log.error('Failed to delete images', { error, count: imageCount });
      toast.error('Failed to delete some images. Refresh may be needed.', { id: toastId });
    }
  }, [
    selectedImages,
    imagesToDelete,
    bulkDeleteImages,
    setSelectedImages,
    setImagesToDelete,
    setShowDeleteConfirm,
  ]);

  // Handle bulk archive
  const handleBulkArchiveImages = useCallback(async () => {
    if (selectedImages.length === 0) return;
    const imageIdsToArchive = [...selectedImages];
    const imageCount = imageIdsToArchive.length;

    log.info('Bulk Archive Action', { count: imageCount });
    const toastId = toast.loading(`Archiving ${imageCount} image(s)...`);
    setSelectedImages([]); // Clear selection immediately

    try {
      const results = await Promise.all(imageIdsToArchive.map((id) => bulkArchiveImages({ id })));

      const successCount = results.filter((r) => r.success).length;
      const failCount = results.filter((r) => !r.success).length;

      if (successCount > 0) {
        toast.success(`${successCount} image(s) archived successfully.`, { id: toastId });
      }
      if (failCount > 0) {
        toast.error(`Failed to archive ${failCount} image(s).`, { id: toastId });
      }
    } catch (error) {
      log.error('Failed to archive images', { error, count: imageCount });
      toast.error('Failed to archive some images.', { id: toastId });
    }
  }, [selectedImages, bulkArchiveImages, setSelectedImages]);

  // Modify handleEditImage to accept imageId - for metadata editing
  const handleEditImage = useCallback(
    (imageId: Id<'images'>) => {
      log.info('[WorkBenchPage] Opening edit dialog for', { imageId });
      // Find the image data using the ID
      const imageToEditData = allImages?.find((img) => img._id === imageId);
      if (imageToEditData) {
        // No need for type assertion here, rely on inferred type from allImages
        setEditImageId(imageToEditData._id);
        setEditForm({
          title: imageToEditData.title || '',
          description: imageToEditData.description || '',
          tags: imageToEditData.tags || [],
        });
        // TODO: Ensure the Edit Dialog component is rendered and controlled by editImageId
      } else {
        log.warn('Could not find image data for edit', { imageId });
        toast.error('Could not find image data to edit.');
      }
    },
    [allImages, setEditImageId, setEditForm],
  ); // Add allImages to dependencies

  // Add handler for opening the Pintura image editor
  const handleOpenImageEditor = useCallback(
    (imageId: Id<'images'>) => {
      log.info('[WorkBenchPage] Opening Pintura image editor for', { imageId });
      // Find the image data using the ID
      const imageToEditData = allImages?.find((img) => img._id === imageId);
      if (imageToEditData) {
        setImageToEdit(imageToEditData);
      } else {
        log.warn('Could not find image data for Pintura editor', { imageId });
        toast.error('Could not find image data to edit.');
      }
    },
    [allImages],
  );

  // Restore the handleSaveEditedImage callback
  const handleSaveEditedImage = useCallback(async () => {
    if (!editImageId) return;
    log.info('Saving image metadata', { editImageId, form: editForm });
    const toastId = toast.loading('Saving image metadata...');
    try {
      // Pass only the ID and the fields being updated
      await updateImageMetadata({
        id: editImageId,
        title: editForm.title,
        description: editForm.description,
        tags: editForm.tags,
      });
      toast.success('Image updated successfully', { id: toastId });
      setEditImageId(null); // Close the dialog on success
    } catch (error) {
      log.error('Failed to update image metadata', { error, imageId: editImageId });
      toast.error(
        `Failed to update image: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { id: toastId },
      );
    }
  }, [editImageId, editForm, updateImageMetadata, setEditImageId]);

  // Handler for bulk AI image analysis action
  const handleBulkImageRecognition = useCallback(async (imageIds: Id<'images'>[]) => {
    if (imageIds.length === 0) return;
    log.info('Initiating Bulk Image Recognition', { count: imageIds.length });
    const toastId = toast.loading(`Starting AI analysis for ${imageIds.length} image(s)...`);

    let successCount = 0;
    let failCount = 0;
    let alreadyProcessedCount = 0;

    // Run image recognition calls concurrently but wait for all
    const results = await Promise.allSettled(
      imageIds.map((id) => runImageRecognition({ imageId: id })),
    );

    results.forEach((result, index) => {
      const imageId = imageIds[index];
      if (result.status === 'fulfilled') {
        if (result.value.imageRecognitionText) {
          successCount++;
        } else if (result.value.alreadyProcessed) {
          alreadyProcessedCount++;
        } else {
          // Fulfilled but no text and not already processed - might count as a soft failure or different category
          log.warn('Image analysis fulfilled but no new text and not marked as already processed', {
            imageId,
            resultValue: result.value,
          });
        }
      } else {
        failCount++;
        log.error('Image analysis failed for an image in bulk operation', {
          imageId,
          reason: result.reason,
        });
      }
    });

    // Update toast based on results
    let message = `Bulk AI analysis complete. ${successCount} new, ${alreadyProcessedCount} already processed.`;
    if (failCount > 0) message += ` ${failCount} failed.`;
    toast.success(message, { id: toastId });

    // Note: Convex query will update the UI automatically eventually showing image recognition text
  }, [runImageRecognition]);

  // Handler to set active SKU ID (for SkuManager)
  const handleSetActiveSku = useCallback(
    (skuId: Id<'skus'> | null) => {
      setCurrentWorkingSkuId(skuId);
    },
    [setCurrentWorkingSkuId],
  );

  // Handler for quick SKU creation (right-click)
  const handleQuickCreateSku = useCallback(() => {
    console.log('handleQuickCreateSku called', {
      selectedImagesCount: selectedImages.length,
      isCreatingSku,
    });
    if (selectedImages.length === 0) {
      toast.error('No images selected');
      return;
    }
    if (isCreatingSku) {
      console.log('SKU creation already in progress, ignoring');
      return;
    }
    setShowQuickSkuDialog(true);
  }, [selectedImages.length, isCreatingSku]);

  const handleQuickSkuSubmit = useCallback(
    async (inputValue: string) => {
      console.log('handleQuickSkuSubmit called', {
        inputValue,
        selectedImagesCount: selectedImages.length,
      });

      if (!inputValue.trim() || selectedImages.length === 0 || isCreatingSku) {
        console.log('Early return: missing input, no images, or already creating');
        return;
      }

      // Capture the current selection before any async operations
      const imagesToAssociate = [...selectedImages];
      const trimmedInputValue = inputValue.trim();

      // Set loading state and clear UI state immediately to prevent user interaction issues
      setIsCreatingSku(true);
      setSelectedImages([]);
      setShowQuickSkuDialog(false);

      const toastId = toast.loading('Creating SKU and associating images...');
      console.log('Starting SKU creation process');

      try {
        // Create the SKU using ensureSku
        console.log('Calling ensureSkuMutation');
        const result = await ensureSkuMutation({
          whatIsIt: trimmedInputValue,
          sourceId: currentWorkingSourceId || undefined,
          blueprintId: currentWorkingBlueprintId || undefined,
          imageIds: imagesToAssociate, // Pass image IDs for barcode detection
        });
        console.log('ensureSkuMutation result:', result);

        if (!result.skuId) {
          throw new Error('Failed to create SKU');
        }

        // Associate all selected images with the new SKU
        console.log('Associating images with SKU');
        await Promise.all(
          imagesToAssociate.map((imageId) =>
            associateImageMutation({
              imageId,
              skuId: result.skuId,
            }),
          ),
        );
        console.log('Images associated successfully');

        // Log existing recognition to SKU activity or trigger new recognition
        log.info('Processing image recognition for newly associated images', {
          skuId: result.skuId,
          imageCount: imagesToAssociate.length,
        });

        // Get image data to check for existing recognition
        const imageDataList = await Promise.all(
          imagesToAssociate.map(async (imageId) => {
            try {
              return await convex.query(api.images.getImageById, { id: imageId });
            } catch (err) {
              log.error(`Failed to get image data for ${imageId}`, { error: err });
              return null;
            }
          }),
        );

        // Process each image for recognition/logging
        imagesToAssociate.forEach((imageId, index) => {
          const imageData = imageDataList[index];
          if (!imageData) return;

          if (imageData.imageRecognitionText) {
            // Log existing recognition to SKU activity
            log.info(`Logging existing image recognition to SKU activity for ${imageId}`);
            logImageRecognitionToSkuMutation({
              skuId: result.skuId,
              imageId: imageData._id,
              imageRecognitionText: imageData.imageRecognitionText,
            }).catch((err: unknown) => {
              log.error(`Failed to log existing recognition to SKU activity for ${imageId}`, {
                error: err,
              });
            });
          } else {
            // Trigger new recognition (will auto-log to SKU activity)
            log.info(`Triggering image recognition for newly associated image ${imageId}`);
            runImageRecognition({ imageId }).catch((err: unknown) => {
              log.error(`Failed to trigger image recognition for ${imageId}`, { error: err });
            });
          }
        });

        // Set the new SKU as the current working SKU
        console.log('Setting current working SKU');
        try {
          await setCurrentWorkingSkuId(result.skuId);
          console.log('Current working SKU set successfully');
        } catch (skuSetError) {
          console.error('Failed to set current working SKU:', skuSetError);
          // Continue anyway - don't let this break the whole flow
        }

        // Show success message with identifiers if detected
        let successMessage = `Created "${result.fullSku}" with ${imagesToAssociate.length} image${imagesToAssociate.length === 1 ? '' : 's'}. Images moved to SKU.`;
        if (result.detectedIdentifiers && result.detectedIdentifiers.length > 0) {
          successMessage += ` Found identifiers: ${result.detectedIdentifiers.join(', ')}`;
        }

        toast.success(successMessage, { id: toastId, duration: 5000 });

        console.log('SKU creation process completed successfully');

        // Force a small delay to ensure all state updates have propagated
        await new Promise((resolve) => setTimeout(resolve, 100));

        console.log('About to exit SKU creation function - final state check');
      } catch (error) {
        console.error('SKU creation failed:', error);
        log.error('Failed to create SKU from selection', {
          error,
          whatIsIt: trimmedInputValue,
          selectedImages: imagesToAssociate,
        });
        toast.error('Failed to create SKU and associate images', { id: toastId });

        // On error, restore the selection so user can try again
        setSelectedImages(imagesToAssociate);
      } finally {
        // Always clear the loading state
        console.log('Finally block: clearing isCreatingSku');
        try {
          setIsCreatingSku(false);
          console.log('Finally block: isCreatingSku cleared successfully');
        } catch (finalError) {
          console.error('CRITICAL ERROR in finally block:', finalError);
        }
      }
    },
    [
      selectedImages,
      isCreatingSku,
      ensureSkuMutation,
      currentWorkingSourceId,
      currentWorkingBlueprintId,
      associateImageMutation,
      setCurrentWorkingSkuId,
      convex,
      logImageRecognitionToSkuMutation,
      runImageRecognition,
    ],
  );

  // Add global error handler
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('GLOBAL ERROR CAUGHT:', event.error);
      console.error('Error message:', event.message);
      console.error('Error filename:', event.filename);
      console.error('Error line:', event.lineno);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('UNHANDLED PROMISE REJECTION:', event.reason);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // Set up event handlers for the Pintura editor
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Register callbacks on window for serializable props
      window.handleEditComplete = function () {
        log.info('[WorkBenchPage] Edit complete event received');
        // After editing is completed, refresh the images
        setImageToEdit(null);
        // Refresh images if needed
        window.dispatchEvent(new CustomEvent('workbench-image-upload'));
      };

      window.handleEditCancel = function () {
        log.info('[WorkBenchPage] Edit cancel event received');
        // Reset the image editor state when cancelled
        setImageToEdit(null);
      };

      // Clean up
      return () => {
        delete window.handleEditComplete;
        delete window.handleEditCancel;
      };
    }
  }, []);

  // Loading / Initial State Check
  if (allImages === undefined || activeEbayAccount === undefined) {
    return <div className="p-8 text-center">Loading workspace...</div>;
  }

  // Show onboarding message for new users without eBay accounts
  if (!activeEbayUserId) {
    return (
      <div className="relative pb-24">
        <div className="space-y-6">
          <EmptyState
            icon={PackageIcon}
            title="Connect your eBay account"
            description="To use the workbench, you need to connect an eBay account first. Go to Settings → eBay to connect your account."
          />
        </div>
      </div>
    );
  }

  return (
    <div className="relative pb-20">
      {/* Loading overlay during SKU creation */}
      {isCreatingSku && (
        <div
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center"
          onClick={() => console.log('Loading overlay clicked - this should not happen')}
        >
          <div className="text-center space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-sm text-muted-foreground">Creating SKU and associating images...</p>
          </div>
        </div>
      )}

      <div className="space-y-6">
        <div className="flex flex-col lg:flex-row gap-6">
          {/* --- Left Sticky Sidebar (Desktop) --- */}
          <div className="w-full lg:w-72 xl:w-80 space-y-4 lg:sticky lg:top-[calc(60px_+_1rem)] lg:border-r lg:pr-4 mb-4 lg:mb-0">
            {/* Image Uploader - Compact */}
            <div className="pb-2 border-b">
              <RepositoryUploader
                ebayUserId={activeEbayUserId}
                uploadCompleteEventName="workbench-image-upload"
                sourceId={currentWorkingSourceId || undefined}
              />
            </div>

            {/* SKU Manager - Compact */}
            <SkuManager
              ebayUserId={activeEbayUserId}
              activeSkuId={currentWorkingSkuId}
              onSkuSelect={handleSetActiveSku}
              skuImageMap={skuImageMap}
            />
          </div>
          {/* --- End Left Sidebar --- */}

          {/* --- Right Content Area (Scrolling) --- */}
          <div className="flex-1 space-y-4">
            {/* Ready to Assign Section */}
            <div>
              <h2 className="text-lg font-semibold mb-3 px-4 md:px-0">
                ({unassignedImages.length}) unassigned images in {sourceData?.name || 'this source'}
              </h2>

              {unassignedImages.length > 0 ? (
                <ImageSelectionContextMenu
                  selectedImageIds={selectedImages}
                  isCreatingSku={isCreatingSku}
                  activeSkuId={currentWorkingSkuId}
                  activeSkuName={currentSkuData?.fullSku}
                  onCreateSku={handleQuickCreateSku}
                  onAssociate={handleBulkAssociate}
                  onDelete={() => {
                    setImagesToDelete(selectedImages);
                    setShowDeleteConfirm(true);
                  }}
                  onArchive={handleBulkArchiveImages}
                  onEdit={handleEditImage}
                  onAdvancedEdit={handleOpenImageEditor}
                  onRunImageRecognition={handleBulkImageRecognition}
                  onPreviewSelection={handlePreviewSelection}
                  onClearSelection={() => setSelectedImages([])}
                >
                  <div className="flex flex-wrap gap-3 justify-start">
                    {unassignedImages.map((image) => (
                      <ImageCard
                        key={image._id}
                        image={image as ImageDocumentForCard}
                        isSelected={selectedImages.includes(image._id)}
                        onSelect={() => handleToggleImageSelection(image._id)}
                        onEdit={() => handleOpenImageEditor(image._id)}
                      />
                    ))}
                  </div>
                </ImageSelectionContextMenu>
              ) : null}
            </div>

            {/* Original Images Section - Only show if there are parent images */}
            {parentImages.length > 0 && (
              <div>
                <h2 className="text-base font-medium mb-3 px-4 md:px-0 text-muted-foreground">
                  ({parentImages.length}) original images (edited versions exist)
                </h2>

                <ImageSelectionContextMenu
                  selectedImageIds={selectedImages}
                  isCreatingSku={isCreatingSku}
                  activeSkuId={currentWorkingSkuId}
                  activeSkuName={currentSkuData?.fullSku}
                  onCreateSku={handleQuickCreateSku}
                  onAssociate={handleBulkAssociate}
                  onDelete={() => {
                    setImagesToDelete(selectedImages);
                    setShowDeleteConfirm(true);
                  }}
                  onArchive={handleBulkArchiveImages}
                  onEdit={handleEditImage}
                  onAdvancedEdit={handleOpenImageEditor}
                  onRunImageRecognition={handleBulkImageRecognition}
                  onPreviewSelection={handlePreviewSelection}
                  onClearSelection={() => setSelectedImages([])}
                >
                  <div className="flex flex-wrap gap-3 justify-start">
                    {parentImages.map((image) => (
                      <ImageCard
                        key={image._id}
                        image={image as ImageDocumentForCard}
                        isSelected={selectedImages.includes(image._id)}
                        onSelect={() => handleToggleImageSelection(image._id)}
                        onEdit={() => handleOpenImageEditor(image._id)}
                      />
                    ))}
                  </div>
                </ImageSelectionContextMenu>
              </div>
            )}

            {/* Empty states */}
            {unassignedImages.length === 0 && parentImages.length === 0 && (
              <EmptyState
                icon={allImages && allImages.length > 0 ? CheckCheck : ImageIcon}
                title={allImages && allImages.length > 0 ? 'All images assigned!' : 'No images yet'}
                context={sourceData?.name ? `in ${sourceData.name}` : undefined}
                description={
                  allImages && allImages.length > 0
                    ? 'Great work! All images have been assigned to SKUs. Upload more images or switch sources using the context bar above.'
                    : 'Upload images to start organizing them into SKUs.'
                }
                action={
                  !allImages || allImages.length === 0
                    ? {
                        label: 'Upload Images',
                        onClick: () => {
                          // Find the RepositoryUploader button - it's in the left sidebar
                          // Look for the button with dashed border (desktop) or the mobile FAB
                          const uploadButton =
                            document.querySelector('button[title="Upload Images"]') ||
                            document.querySelector('.border-dashed button') ||
                            document.querySelector('.lg\\:w-72 button');
                          if (uploadButton instanceof HTMLElement) {
                            uploadButton.click();
                          } else {
                            toast.error(
                              'Upload button not found. Please use the "Add Images" button in the sidebar.',
                            );
                          }
                        },
                        variant: 'default',
                      }
                    : undefined
                }
              />
            )}
          </div>
          {/* --- End Right Content Area --- */}
        </div>

        {/* --- Floating Action Button to Open Drawer --- */}
        {selectedImages.length > 0 && !isCreatingSku && (
          <Button
            className="fixed bottom-4 right-4 z-40 h-11 w-11 rounded-full shadow-lg"
            size="icon"
            onClick={() => setIsDrawerOpen(true)}
            title={`Actions for ${selectedImages.length} selected images`}
          >
            <CheckCheck className="h-5 w-5" />
          </Button>
        )}
        {/* --- END Floating Action Button --- */}

        {/* --- Selection Drawer --- */}
        <SelectionDrawer
          isOpen={isDrawerOpen}
          onOpenChange={setIsDrawerOpen}
          selectedImageIds={selectedImages}
          allImages={trayImages}
          activeSkuId={currentWorkingSkuId}
          isCreatingSku={isCreatingSku}
          onAssociate={handleBulkAssociate}
          onDelete={() => {
            setImagesToDelete(selectedImages);
            setShowDeleteConfirm(true);
          }}
          onArchive={handleBulkArchiveImages}
          onEdit={handleEditImage}
          onAdvancedEdit={handleOpenImageEditor}
          onRunImageRecognition={handleBulkImageRecognition}
          onPreviewSelection={handlePreviewSelection}
          onJustClear={() => setSelectedImages([])}
          onTriggerSkuSelection={() => {
            // For mobile workbench, close drawer and let user use sidebar SKU manager
            setIsDrawerOpen(false);
            toast.info('Use the SKU manager in the sidebar to select or create SKUs');
          }}
          onCreateSkuFromSelection={(whatIsIt) => {
            // Delegate to the quick create handler we already have
            handleQuickSkuSubmit(whatIsIt);
            setIsDrawerOpen(false); // Close drawer after creating
          }}
        />
        {/* --- END Selection Drawer --- */}

        {/* Render Lightbox conditionally */}
        <Lightbox
          open={lightboxOpen}
          close={() => setLightboxOpen(false)}
          slides={lightboxSlides}
          plugins={[Zoom, Captions]}
          zoom={{ scrollToZoom: true }}
        />

        {/* Re-add Edit Image Dialog */}
        <Dialog open={!!editImageId} onOpenChange={(open) => !open && setEditImageId(null)}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Edit Image Metadata</DialogTitle>
              <DialogDescription>Update title, description, and tags.</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              {/* Form content - assumes editForm state drives values */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={editForm.title}
                  onChange={(e) => setEditForm((prev) => ({ ...prev, title: e.target.value }))}
                  placeholder="Image title"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={editForm.description}
                  onChange={(e) =>
                    setEditForm((prev) => ({ ...prev, description: e.target.value }))
                  }
                  placeholder="Optional description"
                  rows={3}
                />
              </div>
              {/* Add tag management UI here if needed, similar to photos/page */}
              {/* For simplicity, just showing tags for now */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Tags</label>
                <div className="flex flex-wrap gap-1 mb-2">
                  {editForm.tags.length > 0 ? (
                    editForm.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        {tag}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-xs text-muted-foreground">No tags</span>
                  )}
                </div>
                {/* TODO: Add input/button to add/remove tags similar to image-repo page if needed */}
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditImageId(null)}>
                Cancel
              </Button>
              <Button onClick={handleSaveEditedImage}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Re-add Delete Confirmation Dialog */}
        <Dialog
          open={showDeleteConfirm}
          onOpenChange={(open) => !open && setShowDeleteConfirm(false)}
        >
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Confirm Deletion</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete {imagesToDelete.length} image(s)? This action cannot
                be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDeleteConfirm(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleBulkDeleteImages}>
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Pintura Image Editor */}
        {imageToEdit && (
          <ImageEditor
            image={imageToEdit}
            onCompleteId="handleEditComplete"
            onCancelId="handleEditCancel"
            isOpen={!!imageToEdit}
          />
        )}

        {/* Quick SKU Creation Dialog */}
        <QuickSkuDialog
          isOpen={showQuickSkuDialog}
          selectedImageCount={selectedImages.length}
          onClose={() => setShowQuickSkuDialog(false)}
          onSubmit={handleQuickSkuSubmit}
        />
      </div>
    </div>
  );
}
