'use client';

// Imports for UI components and hooks
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { EmptyState } from '@/components/ui/empty-state';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UnifiedSearchBar } from '@/components/ui/unified-search-bar';
import { useActiveRevisions } from '@/lib/ebay/hooks/trading/useActiveRevisions';
import { type UserDraftData } from '@/lib/ebay/hooks/trading/useCombinedListings';
import { useActiveEbayAccount } from '@/lib/ebay/hooks/useActiveEbayAccount';
import { logger } from '@/lib/logger';
import { FetchAllListingsResult } from '@/lib/types/ebay-listings';
import { useAuth } from '@clerk/nextjs';
import { api } from '@convex/_generated/api';
import { useAction, useMutation, useQuery } from 'convex/react';
import { FileText, Package, Plus, RefreshCw } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { lazy, Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';
import { ebayColumns, EbayListingUI, mapToEbayListingUI } from './columns';
import { DataTable } from './data-table';
import { draftColumns, DraftListing as DraftListingUI, mapToDraftListingUI } from './draftColumns';
import { EditPriceDialogProvider, useEditPriceDialog } from './edit-price-dialog';
import { ListingsSearchInput } from './ListingsSearchInput';
import { AddSkuForm, type SkuFormData } from '@/app/(auth)/stockroom/components/AddSkuForm';
import { CreateEditTemplateModal } from '@/app/(auth)/stockroom/components/CreateEditTemplateModal';
import { useRegisterPageActions, type PageAction } from '@/lib/navigation/page-actions-context';
import { useNavigation } from '@/lib/state/NavigationProvider';
import { EntityStates } from '@/lib/state/useListingStateMachine';
import {
  useCurrentFoundationBlueprintId,
  useCurrentMarketplaceId,
  useCurrentWorkingBlueprintId,
  useCurrentWorkingSourceId,
} from '@/lib/state/useWorkspaceContext';
import { Id } from '@convex/_generated/dataModel';
import { useBulkActions } from './hooks/useBulkActions';

// Lazy load the listing preview sheet for performance
const ListingPreviewSheet = lazy(() =>
  import('./ListingPreviewSheet').then((module) => ({
    default: module.ListingPreviewSheet,
  })),
);

const log = logger.create('pages:trading:listings');

// EndedTimeframe type for ended listings sync
type EndedTimeframe = '24h' | '7d' | '30d';

// Minimal SKU detail for mapping
interface BasicSkuDetail {
  _id: Id<'skus'>;
  fullSku: string;
  sourceId: Id<'sources'>;
  whatIsIt: string;
  customSkuString?: string | null;
}

function ListingsPageContent() {
  // Get search params and navigation context
  const searchParams = useSearchParams();
  const tabParam = searchParams?.get('tab');
  const { navigate } = useNavigation();

  // Keep navigation state in sync with tab param
  useEffect(() => {
    navigate('listings', { tab: tabParam || 'live' });
  }, [navigate, tabParam]);

  log.debug('[ListingsPageContent] Rendering...');

  // Auth, router, and eBay account context
  const { userId } = useAuth();
  const router = useRouter();
  const { activeAccount } = useActiveEbayAccount();

  // Convex actions for listings, drafts, and SKUs
  const fetchEbayListingsAction = useAction(api.listingsActions.fetchEbayListings);
  const createRevisionDraftAction = useAction(api.createRevisionDraft.createRevisionDraft);
  const createSkuAndDraft = useAction(api.draftCreationPublic.createSkuAndDraft);

  // Tab state: live, ended, or drafts
  const [activeTab, setActiveTab] = useState(
    tabParam === 'drafts' ? 'drafts' : tabParam === 'ended' ? 'ended' : 'live',
  );

  // Keep activeTab in sync with tabParam
  useEffect(() => {
    if (tabParam === 'drafts') {
      setActiveTab('drafts');
    } else if (tabParam === 'ended') {
      setActiveTab('ended');
    } else {
      setActiveTab('live');
    }
  }, [tabParam]);

  // UI state for modals and actions
  const [isCreatingDraft, setIsCreatingDraft] = useState(false);
  const [showSkuModal, setShowSkuModal] = useState(false);
  const [isCreatingSkuAndDraft, setIsCreatingSkuAndDraft] = useState(false);
  const [showSaveAsTemplateModal, setShowSaveAsTemplateModal] = useState(false);
  const [currentDraftForTemplate, setCurrentDraftForTemplate] = useState<UserDraftData | undefined>(
    undefined,
  );

  // Workspace context for current source, blueprint, foundation, and marketplace
  const { currentWorkingSourceId } = useCurrentWorkingSourceId();
  const { currentWorkingBlueprintId } = useCurrentWorkingBlueprintId();
  const { currentFoundationBlueprintId } = useCurrentFoundationBlueprintId();
  const { currentMarketplaceId } = useCurrentMarketplaceId();

  // Get the effective source for SKU creation
  const sourceData = useQuery(
    api.skus.getSourceById,
    currentWorkingSourceId ? { sourceId: currentWorkingSourceId } : 'skip',
  );
  const defaultSource = useQuery(
    api.skus.getSourceByCode,
    !currentWorkingSourceId ? { code: 'JDAI' } : 'skip',
  );
  const effectiveSource = sourceData || defaultSource;

  // Get the current blueprint for the workspace
  const templateForCurrentTag = useQuery(
    api.blueprints.getBlueprintById,
    currentWorkingBlueprintId ? { blueprintId: currentWorkingBlueprintId } : ('skip' as never),
  );

  // Register edit price dialog context (side effect)
  useEditPriceDialog();

  // Table selection and search state
  const [selectedRowsData, setSelectedRowsData] = useState<(EbayListingUI | DraftListingUI)[]>([]);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Use debounced search term for queries
  const searchTerm = debouncedSearchTerm || '';

  // Query for eBay listings (skip only when on drafts tab for performance)
  const ebayListingsRaw = useQuery(
    api.ebay.searchEbayListings,
    currentMarketplaceId && activeAccount && activeTab !== 'drafts'
      ? {
        searchTerm,
        marketplaceFilter: currentMarketplaceId,
        accountId: activeAccount.ebayUserId,
        limit: 100,
      }
      : 'skip',
  );
  const isLoadingEbay = ebayListingsRaw === undefined;

  const userDraftsRaw = useQuery(
    api.ebay.searchUserDrafts,
    currentMarketplaceId && activeAccount && activeTab === 'drafts'
      ? {
        searchTerm,
        marketplaceFilter: currentMarketplaceId,
        accountId: activeAccount.ebayUserId,
        limit: 100,
      }
      : 'skip',
  );

  // Mutation for deleting drafts
  const deleteDraftMutation = useMutation(api.drafts.deleteDraft);

  // Get all eBay item IDs for revision mapping
  const currentEbayItemIds = (ebayListingsRaw || []).map((item) => item.itemId);
  const { revisionsMap } = useActiveRevisions(currentEbayItemIds);

  // Transform revisionsMap to a simpler format for useBulkActions
  const transformedRevisionsMap = useMemo(() => {
    if (!revisionsMap) return null;
    const transformed: Record<string, { uuid: string }> = {};
    Object.entries(revisionsMap).forEach(([itemId, listing]) => {
      if (listing?.uuid) {
        transformed[itemId] = { uuid: listing.uuid };
      }
    });
    return transformed;
  }, [revisionsMap]);

  // Bulk actions for table selections
  const {
    isAdopting,
    isCreatingRevisionDrafts,
    isBulkDeleting,
    isBulkVerifying,
    isBulkAiEnhancing,
    unadoptedSelectedCount,
    aiEnhancableSelectedCount,
    adoptableSelectedItemIds,
    handleBulkDeleteDrafts,
    handleBulkDeleteEbayListingRecords,
    handleBulkAdopt,
    handleBulkCreateRevisionDrafts,
    handleBulkVerify,
    handleBulkAiEnhancement,
  } = useBulkActions({
    selectedRowsData,
    setSelectedRowsData,
    revisionsMap: transformedRevisionsMap,
    currentMarketplaceId,
  });

  // Gather all unique SKU IDs from listings and drafts
  const allSkuIds = useMemo(() => {
    const skuIds = new Set<Id<'skus'>>();
    (ebayListingsRaw || []).forEach((item) => item.skuId && skuIds.add(item.skuId));
    (userDraftsRaw || []).forEach((draft) => draft.skuId && skuIds.add(draft.skuId));
    return Array.from(skuIds);
  }, [ebayListingsRaw, userDraftsRaw]);

  // Query for basic SKU details for all relevant SKUs
  const skuDetailsArray = useQuery(
    api.skus.getBasicSkuDetailsForIds,
    allSkuIds.length > 0 ? { skuIds: allSkuIds } : 'skip',
  );

  // Map SKU ID to details for fast lookup
  const skuDetailsMap = useMemo(() => {
    const map = new Map<Id<'skus'>, BasicSkuDetail>();
    if (skuDetailsArray) {
      skuDetailsArray.forEach((skuDetail: BasicSkuDetail) => {
        map.set(skuDetail._id, skuDetail);
      });
    }
    return map;
  }, [skuDetailsArray]);

  // Sync state for live/ended listings
  const [isSyncingLive, setIsSyncingLive] = useState(false);
  const [isSyncingEnded, setIsSyncingEnded] = useState(false);
  const [syncProgress, setSyncProgress] = useState<{
    totalPages: number;
    pagesComplete: number;
  } | null>(null);

  // Track which ended timeframe is syncing
  const [selectedEndedTimeframe, setSelectedEndedTimeframe] = useState<EndedTimeframe | null>(null);

  // Map raw eBay listings to UI format
  const ebayListingsUI = useMemo(() => {
    if (!ebayListingsRaw) return [];
    return ebayListingsRaw.map((listing) => {
      const userListing =
        (userDraftsRaw || []).find((draft) => draft.ebayItemId === listing.itemId) || null;
      const skuDetail = listing.skuId ? skuDetailsMap.get(listing.skuId) : undefined;
      return mapToEbayListingUI(listing, userListing, revisionsMap, skuDetail);
    });
  }, [ebayListingsRaw, userDraftsRaw, revisionsMap, skuDetailsMap]);

  // Map raw user drafts to UI format
  const userDraftsUI = useMemo(() => {
    if (!userDraftsRaw) return [];
    return userDraftsRaw.map((draft) => {
      const skuDetail = draft.skuId ? skuDetailsMap.get(draft.skuId) : undefined;
      return mapToDraftListingUI(draft, skuDetail);
    });
  }, [userDraftsRaw, skuDetailsMap]);

  // Filter eBay listings by tab (live/ended)
  const filteredEbayListingsUI = useMemo(() => {
    if (activeTab === 'live') {
      return ebayListingsUI.filter((item) => item.state !== EntityStates.ENDED);
    } else if (activeTab === 'ended') {
      return ebayListingsUI.filter((item) => item.state === EntityStates.ENDED);
    }
    return ebayListingsUI;
  }, [ebayListingsUI, activeTab]);

  // Drafts are not filtered by tab (all shown in drafts tab)
  const filteredDraftsUI = userDraftsUI;

  // Handler: Sync live listings from eBay
  const handleSyncLiveListings = useCallback(async () => {
    if (!userId || isSyncingLive) return;
    setIsSyncingLive(true);
    setSyncProgress(null);
    const toastId = toast.loading('Syncing live listings from eBay...');
    try {
      const result: FetchAllListingsResult = await fetchEbayListingsAction({
        marketplaceId: currentMarketplaceId,
      });
      if (result.success) {
        toast.success(
          `Live listings sync complete! (${result.totalItemsFetched} items, ${result.totalPagesFetched} pages)`,
          { id: toastId },
        );
      } else {
        throw new Error(result.message || 'Live listings sync failed');
      }
    } catch (err) {
      log.error('Error syncing live listings', { error: err });
      toast.error(`Sync failed: ${err instanceof Error ? err.message : 'Unknown error'}`, {
        id: toastId,
      });
    } finally {
      setIsSyncingLive(false);
      setSyncProgress(null);
    }
  }, [userId, isSyncingLive, currentMarketplaceId, fetchEbayListingsAction]);

  // Handler: Sync ended listings for a given timeframe
  const handleSyncEndedListings = useCallback(
    async (timeframe: EndedTimeframe) => {
      if (!userId || isSyncingEnded) return;
      setIsSyncingEnded(true);
      setSyncProgress(null);
      setSelectedEndedTimeframe(timeframe);

      // Map timeframe to days
      const days = timeframe === '24h' ? 1 : timeframe === '7d' ? 7 : 30;
      const toastId = toast.loading(`Syncing ended listings (${timeframe})...`);

      try {
        const result: FetchAllListingsResult = await fetchEbayListingsAction({
          marketplaceId: currentMarketplaceId,
          timeFilter: { type: 'ended', days },
        });

        if (result.success) {
          toast.success(
            `Ended listings sync complete (${timeframe})! (${result.totalItemsFetched} items, ${result.totalPagesFetched} pages)`,
            { id: toastId },
          );
        } else {
          throw new Error(result.message || `Ended listings sync failed (${timeframe})`);
        }
      } catch (err) {
        log.error('Error syncing ended listings', { timeframe, error: err });
        toast.error(
          `Sync failed (${timeframe}): ${err instanceof Error ? err.message : 'Unknown error'}`,
          { id: toastId },
        );
      } finally {
        setIsSyncingEnded(false);
        setSyncProgress(null);
      }
    },
    [userId, isSyncingEnded, currentMarketplaceId, fetchEbayListingsAction],
  );

  // Handler: Open modal to create new draft (SKU modal)
  const handleCreateNewV1 = useCallback(async () => {
    log.info('Create New V1 Draft button clicked');
    setShowSkuModal(true);
  }, [setShowSkuModal]);

  // Handler: Actually create new SKU and draft from modal form
  const handleCreateNewSkuAndDraft = useCallback(
    async (data: SkuFormData) => {
      const { whatIsIt, sourceId } = data;
      setIsCreatingSkuAndDraft(true);
      const toastId = toast.loading('Creating new SKU and draft...');

      try {
        // Prefer working blueprint, else use template for current tag if not archived
        const blueprintIdToApply =
          currentWorkingBlueprintId ||
          (templateForCurrentTag && !templateForCurrentTag.archivedAt
            ? templateForCurrentTag._id
            : null);
        const foundationIdToApply = currentFoundationBlueprintId;

        log.info('Creating SKU and draft with V2 templates', {
          blueprintId: blueprintIdToApply,
          foundationId: foundationIdToApply,
          workingBlueprintId: currentWorkingBlueprintId,
          sourceId: currentWorkingSourceId,
        });

        const result = await createSkuAndDraft({
          whatIsIt,
          marketplaceId: currentMarketplaceId,
          sourceId: sourceId || currentWorkingSourceId || undefined,
          blueprintId: currentWorkingBlueprintId || undefined,
          blueprintPresetId: blueprintIdToApply || undefined,
          foundationPresetId: foundationIdToApply || undefined,
        });

        if (result.success && result.uuid) {
          setShowSkuModal(false);
          toast.success('SKU and draft created! Redirecting to editor...', { id: toastId });
          router.push(`/listings/draft/${result.uuid}/edit`);
        } else {
          toast.error(`Failed to create SKU and draft: ${result.error}`, { id: toastId });
        }
      } catch (error) {
        log.error('Error creating SKU and draft', { error });
        toast.error(`Error creating SKU and draft: ${(error as Error).message}`, {
          id: toastId,
        });
      }

      setIsCreatingSkuAndDraft(false);
    },
    [
      router,
      currentWorkingSourceId,
      currentWorkingBlueprintId,
      currentFoundationBlueprintId,
      templateForCurrentTag,
      currentMarketplaceId,
      createSkuAndDraft,
    ],
  );

  // Handler: Revise a live listing (create revision draft)
  const handleRevise = useCallback(
    async (listing: EbayListingUI) => {
      if (!listing.ebayItemId) return;

      // If a revision already exists, redirect to it
      if (revisionsMap && revisionsMap[listing.ebayItemId]) {
        toast.info('An active revision already exists. Redirecting to edit...');
        router.push(`/listings/draft/${revisionsMap[listing.ebayItemId]!.uuid}/edit`);
        return;
      }

      log.info('Revise action triggered', { itemId: listing.ebayItemId });
      setIsCreatingDraft(true);
      const toastId = toast.loading('Creating revision draft...');
      try {
        const result = await createRevisionDraftAction({
          itemId: listing.ebayItemId,
          marketplaceId: currentMarketplaceId || 'EBAY_US',
        });
        if (result.success && result.redirectUrl) {
          toast.success('Revision draft created, redirecting...', { id: toastId });
          router.push(result.redirectUrl);
        } else {
          if (result.error?.includes('SKU')) {
            toast.error(
              `SKU Required: This listing must be linked to an internal SKU before revising. Please use the "Adopt" feature or ensure it has a SKU. Message: ${result.error}`,
              { id: toastId, duration: 10000 },
            );
          } else {
            throw new Error(result.error || 'Failed to create revision draft');
          }
        }
      } catch (error) {
        log.error('Failed to create revision draft', {
          error,
          itemId: listing.ebayItemId,
        });
        toast.error(
          `Error creating revision: ${error instanceof Error ? error.message : 'Unknown error'}`,
          { id: toastId },
        );
        setIsCreatingDraft(false);
      }
    },
    [router, revisionsMap, createRevisionDraftAction, currentMarketplaceId],
  );

  // Handler: Edit a draft or revision (navigate to editor)
  const handleEditDraft = useCallback(
    (draft: DraftListingUI | EbayListingUI) => {
      log.info('Editing existing draft/revision', { uuid: draft.uuid });
      router.push(`/listings/draft/${draft.uuid}/edit`);
    },
    [router],
  );

  // Handler: Delete a draft or discard a revision
  const handleDeleteDraft = useCallback(
    async (draftOrRevision: DraftListingUI | EbayListingUI) => {
      const isRevision = !!draftOrRevision.ebayItemId;
      log.warn('Delete/Discard action triggered', {
        uuid: draftOrRevision.uuid,
        itemId: draftOrRevision.ebayItemId,
      });
      const confirmDelete = window.confirm(
        `Are you sure you want to ${isRevision ? 'discard this revision' : 'delete this draft'}?`,
      );
      if (!confirmDelete) return;

      const toastId = toast.loading(isRevision ? 'Discarding revision...' : 'Deleting draft...');
      try {
        await deleteDraftMutation({ uuid: draftOrRevision.uuid });
        toast.success(isRevision ? 'Revision discarded' : 'Draft deleted', { id: toastId });
      } catch (error) {
        log.error('Failed to delete/discard', { error, uuid: draftOrRevision.uuid });
        toast.error(`Failed: ${error instanceof Error ? error.message : 'Unknown error'}`, {
          id: toastId,
        });
      }
    },
    [deleteDraftMutation],
  );

  // Handler: Open eBay listing in new tab
  const handleViewOnEbay = useCallback((listing: EbayListingUI | DraftListingUI) => {
    let url: string | undefined | null = undefined;
    if ('ebayListingRecord' in listing && listing.ebayItemId) {
      url = `https://www.ebay.com/itm/${listing.ebayItemId}?nordt=true&orig_cvip=true`;
    } else if ('draftRecord' in listing && listing.ebayListingUrl) {
      url = listing.ebayListingUrl;
    }
    if (url) {
      window.open(url, '_blank');
    } else {
      toast.error('Cannot view on eBay - Listing ID not found.');
    }
  }, []);

  // Handler: Respond to row action events (edit, delete, revise, etc)
  const handleRowActionEvent = useCallback(
    (event: Event) => {
      const customEvent = event as CustomEvent<{ action: string; id: string }>;
      const { action, id } = customEvent.detail;
      log.debug(`Row action event received: ${action} for ID: ${id}`);

      let item: EbayListingUI | DraftListingUI | undefined;
      if (['revise', 'editRevision', 'discardRevision', 'viewOnEbay'].includes(action)) {
        item = filteredEbayListingsUI.find((l) => l.id === id);
      } else if (['edit', 'delete', 'saveAsTemplate'].includes(action)) {
        item = userDraftsUI.find((d) => d.uuid === id);
      } else {
        log.warn(`Unknown action received: ${action}`);
        return;
      }

      if (!item) {
        log.error(`Could not find item for action '${action}' with id '${id}'`);
        toast.error(`Could not find the item to perform action: ${action}`);
        return;
      }

      switch (action) {
        case 'revise':
          handleRevise(item as EbayListingUI);
          break;
        case 'editRevision':
        case 'edit':
          handleEditDraft(item);
          break;
        case 'discardRevision':
        case 'delete':
          handleDeleteDraft(item);
          break;
        case 'viewOnEbay':
          handleViewOnEbay(item);
          break;
        case 'saveAsTemplate':
          if ('draftRecord' in item) {
            setCurrentDraftForTemplate(item.draftRecord as UserDraftData);
            setShowSaveAsTemplateModal(true);
            log.info('Opening save as template modal for draft:', { uuid: item.uuid });
          } else {
            log.warn('Save as template called on non-draft item type', { item });
            toast.error('Cannot save this item type as a template.');
          }
          break;
        default:
          log.warn(`Unhandled action in event listener: ${action}`);
      }
    },
    [
      filteredEbayListingsUI,
      userDraftsUI,
      handleRevise,
      handleEditDraft,
      handleDeleteDraft,
      handleViewOnEbay,
    ],
  );

  // Preview state for listing preview sheet
  const [previewListing, setPreviewListing] = useState<EbayListingUI | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Calculate available height for table to avoid dual scrollbars
  const [tableHeight, setTableHeight] = useState(400);

  useEffect(() => {
    const calculateTableHeight = () => {
      // Get the available viewport height (subtract root header)
      const availableViewportHeight = window.innerHeight - 56; // Root header height

      // Calculate height consumed by elements within the auth layout:
      // Desktop breadcrumbs + context bar: ~80px
      // Page padding: ~24px (px-3 md:px-4 lg:px-6)
      // Space-y-4 margin: ~16px
      // Tabs header: ~40px  
      // Search bar: ~52px
      // Tab content padding: ~16px
      // Bottom margin: ~16px
      let consumedHeight = 80 + 24 + 16 + 40 + 52 + 16 + 16;

      // Add bulk actions bar height if selections exist
      if (selectedRowsData.length > 0) {
        consumedHeight += 48;
      }

      // Add sync progress bar if showing
      if ((isSyncingLive || isSyncingEnded) && syncProgress) {
        consumedHeight += 60;
      }

      // Calculate available height - table fills remaining space
      const tableHeight = Math.max(300, availableViewportHeight - consumedHeight);
      setTableHeight(tableHeight);
    };

    calculateTableHeight();
    window.addEventListener('resize', calculateTableHeight);

    return () => window.removeEventListener('resize', calculateTableHeight);
  }, [selectedRowsData.length, isSyncingLive, isSyncingEnded, syncProgress]);

  // Handler: Row click (open preview or editor)
  const handleRowClick = useCallback(
    (data: EbayListingUI | DraftListingUI) => {
      if ('draftRecord' in data) {
        log.debug('Draft row clicked, navigating to editor', { uuid: data.uuid });
        if (data.uuid) {
          router.push(`/listings/draft/${data.uuid}/edit`);
        }
      } else {
        log.debug('Row clicked, opening preview', { id: data.id || data.uuid });
        setPreviewListing(data);
        setIsPreviewOpen(true);
      }
    },
    [router],
  );

  // Register row action event listener
  useEffect(() => {
    document.addEventListener('rowAction', handleRowActionEvent);
    return () => {
      document.removeEventListener('rowAction', handleRowActionEvent);
    };
  }, [handleRowActionEvent]);

  // Page actions for the top bar (create, sync, etc)
  const pageActions = useMemo(() => {
    const actions: PageAction[] = [
      {
        id: 'create-draft',
        label: 'Create New Draft',
        icon: <Plus className="h-4 w-4" />,
        onClick: handleCreateNewV1,
        disabled: isCreatingDraft,
        variant: 'default',
      },
    ];

    if (activeTab === 'live') {
      actions.push({
        id: 'sync-live',
        label: 'Sync Live Listings',
        icon: <RefreshCw className="h-4 w-4" />,
        onClick: handleSyncLiveListings,
        disabled: isSyncingLive || isSyncingEnded,
        isLoading: isSyncingLive,
        variant: 'outline',
      });
    } else if (activeTab === 'ended') {
      actions.push(
        {
          id: 'sync-ended-24h',
          label: 'Sync Last 24h',
          icon: <RefreshCw className="h-4 w-4" />,
          onClick: () => handleSyncEndedListings('24h'),
          disabled: isSyncingLive || isSyncingEnded,
          isLoading: isSyncingEnded && selectedEndedTimeframe === '24h',
          variant: 'outline',
        },
        {
          id: 'sync-ended-7d',
          label: 'Sync Last 7 Days',
          icon: <RefreshCw className="h-4 w-4" />,
          onClick: () => handleSyncEndedListings('7d'),
          disabled: isSyncingLive || isSyncingEnded,
          isLoading: isSyncingEnded && selectedEndedTimeframe === '7d',
          variant: 'outline',
        },
        {
          id: 'sync-ended-30d',
          label: 'Sync Last 30 Days',
          icon: <RefreshCw className="h-4 w-4" />,
          onClick: () => handleSyncEndedListings('30d'),
          disabled: isSyncingLive || isSyncingEnded,
          isLoading: isSyncingEnded && selectedEndedTimeframe === '30d',
          variant: 'outline',
        },
      );
    }

    return actions;
  }, [
    activeTab,
    handleCreateNewV1,
    isCreatingDraft,
    handleSyncLiveListings,
    handleSyncEndedListings,
    isSyncingLive,
    isSyncingEnded,
    selectedEndedTimeframe,
  ]);

  // Register page actions for navigation context
  useRegisterPageActions(pageActions);


  // Main render
  return (
    <div className="space-y-4">
      {/* Show progress bar if syncing */}
      {(isSyncingLive || isSyncingEnded) && syncProgress && (
        <div className="p-2 border rounded-md bg-muted">
          <p className="text-sm font-medium mb-1">Syncing listings from eBay...</p>
          <Progress
            value={(syncProgress.pagesComplete / syncProgress.totalPages) * 100}
            className="h-2"
          />
          <p className="text-xs text-muted-foreground mt-1">
            Page {syncProgress.pagesComplete} of {syncProgress.totalPages}
          </p>
        </div>
      )}

      {/* Tabs for Ended, Live, Drafts */}
      <Tabs
        value={activeTab}
        onValueChange={(value) => {
          setActiveTab(value);
          // Update URL param for tab without navigation
          const params = new URLSearchParams(searchParams?.toString() || '');
          if (value === 'live') {
            params.delete('tab');
          } else {
            params.set('tab', value);
          }
          const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
          window.history.replaceState({}, '', newUrl);
        }}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-3 h-10 max-w-full">
          <TabsTrigger value="ended" className="text-xs sm:text-sm px-1 sm:px-4 min-w-0">
            <span className="hidden sm:inline">Ended</span>
            <span className="sm:hidden truncate">End</span>
          </TabsTrigger>
          <TabsTrigger value="live" className="text-xs sm:text-sm px-1 sm:px-4 min-w-0">
            <span className="hidden sm:inline">Live / Active</span>
            <span className="sm:hidden truncate">Live</span>
          </TabsTrigger>
          <TabsTrigger value="drafts" className="text-xs sm:text-sm px-1 sm:px-4 min-w-0">
            <span className="hidden sm:inline">Drafts / Revisions</span>
            <span className="sm:hidden truncate">Draft</span>
          </TabsTrigger>
        </TabsList>

        {/* Search bar for listings */}
        <UnifiedSearchBar>
          <ListingsSearchInput onSearchChange={setDebouncedSearchTerm} />
        </UnifiedSearchBar>

        {/* Ended Listings Tab */}
        <TabsContent value="ended" className="mt-4">
          {/* Show empty state if no ended listings */}
          {!isLoadingEbay && filteredEbayListingsUI.length === 0 && (
            <EmptyState
              icon={Package}
              title={
                debouncedSearchTerm
                  ? 'No ended listings match your search'
                  : 'No ended listings found'
              }
              description={
                debouncedSearchTerm
                  ? `No listings found matching "${debouncedSearchTerm}"`
                  : 'Try syncing ended listings from eBay using the buttons above.'
              }
              action={
                debouncedSearchTerm
                  ? {
                    label: 'Clear Search',
                    onClick: () => setDebouncedSearchTerm(''),
                    variant: 'outline',
                  }
                  : undefined
              }
            />
          )}

          {/* Loading spinner for ended listings */}
          {activeTab === 'ended' && isLoadingEbay && (
            <div className="flex items-center text-muted-foreground mb-4">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              <span>Loading ended listings…</span>
            </div>
          )}

          {/* DataTable for ended listings */}
          {!isLoadingEbay && filteredEbayListingsUI.length > 0 && (
            <DataTable
              columns={ebayColumns}
              data={filteredEbayListingsUI}
              isLoading={false}
              getRowId={(row) => row.id}
              onRowClick={handleRowClick}
              onRowAction={() => { }}
              onPaginationChange={() => { }}
              pageCount={1}
              onBulkDelete={handleBulkDeleteEbayListingRecords}
              bulkActionLabel="Dev: Delete Local Sync"
              onBulkAdopt={adoptableSelectedItemIds.length > 0 ? handleBulkAdopt : undefined}
              unadoptedSelectedCount={unadoptedSelectedCount}
              isDeleting={isBulkDeleting}
              isAdopting={isAdopting}
              onBulkCreateRevisionDrafts={
                selectedRowsData.some((row) => 'ebayListingRecord' in row)
                  ? handleBulkCreateRevisionDrafts
                  : undefined
              }
              isCreatingRevisionDrafts={isCreatingRevisionDrafts}
              onRowSelectionChange={setSelectedRowsData}
              initialSorting={[{ id: 'timeLeft', desc: true }]}
              hideStatusColumn={true}
              virtualTableHeight={tableHeight}
              estimatedRowHeight={60}
              searchTerm={debouncedSearchTerm}
              resultType="ended listings"
            />
          )}
        </TabsContent>

        {/* Live Listings Tab */}
        <TabsContent value="live" className="mt-4">
          {/* Show empty state if no live listings */}
          {!isLoadingEbay && filteredEbayListingsUI.length === 0 && (
            <EmptyState
              icon={Package}
              title={
                debouncedSearchTerm
                  ? 'No live listings match your search'
                  : 'No live listings found'
              }
              description={
                debouncedSearchTerm
                  ? `No listings found matching "${debouncedSearchTerm}"`
                  : 'Try syncing live listings from eBay.'
              }
              action={
                debouncedSearchTerm
                  ? {
                    label: 'Clear Search',
                    onClick: () => setDebouncedSearchTerm(''),
                    variant: 'outline',
                  }
                  : undefined
              }
            />
          )}
          {/* Loading spinner for live listings */}
          {activeTab === 'live' && isLoadingEbay && (
            <div className="flex items-center text-muted-foreground mb-4">
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              <span>Loading live listings…</span>
            </div>
          )}
          {/* DataTable for live listings */}
          {!isLoadingEbay && filteredEbayListingsUI.length > 0 && (
            <DataTable
              columns={ebayColumns}
              data={filteredEbayListingsUI}
              isLoading={false}
              getRowId={(row) => row.id}
              onRowClick={handleRowClick}
              onRowAction={() => { }}
              onPaginationChange={() => { }}
              pageCount={1}
              onBulkDelete={handleBulkDeleteEbayListingRecords}
              bulkActionLabel="Dev: Delete Local Sync"
              onBulkAdopt={adoptableSelectedItemIds.length > 0 ? handleBulkAdopt : undefined}
              unadoptedSelectedCount={unadoptedSelectedCount}
              isDeleting={isBulkDeleting}
              isAdopting={isAdopting}
              onBulkCreateRevisionDrafts={
                selectedRowsData.some((row) => 'ebayListingRecord' in row)
                  ? handleBulkCreateRevisionDrafts
                  : undefined
              }
              isCreatingRevisionDrafts={isCreatingRevisionDrafts}
              onRowSelectionChange={setSelectedRowsData}
              initialSorting={[{ id: 'timeActiveDays', desc: false }]}
              virtualTableHeight={tableHeight}
              estimatedRowHeight={60}
              searchTerm={debouncedSearchTerm}
              resultType="live listings"
            />
          )}
        </TabsContent>

        {/* Drafts Tab */}
        <TabsContent value="drafts" className="mt-4">
          {/* Loading state for drafts */}
          {userDraftsRaw === undefined && <p>Loading drafts...</p>}
          {/* Empty state for drafts */}
          {userDraftsRaw && filteredDraftsUI.length === 0 && (
            <EmptyState
              icon={FileText}
              title={debouncedSearchTerm ? 'No drafts match your search' : 'No drafts found'}
              description={
                debouncedSearchTerm
                  ? `No drafts found matching "${debouncedSearchTerm}"`
                  : 'You have no local drafts or active revisions. Create a new listing from your SKUs.'
              }
              action={
                debouncedSearchTerm
                  ? {
                    label: 'Clear Search',
                    onClick: () => setDebouncedSearchTerm(''),
                    variant: 'outline',
                  }
                  : undefined
              }
            />
          )}
          {/* DataTable for drafts */}
          {userDraftsRaw && filteredDraftsUI.length > 0 && (
            <DataTable
              columns={draftColumns}
              data={filteredDraftsUI}
              isLoading={false}
              getRowId={(row) => row.uuid}
              onRowClick={handleRowClick}
              onRowAction={() => { }}
              onPaginationChange={() => { }}
              pageCount={1}
              onBulkDelete={handleBulkDeleteDrafts}
              bulkActionLabel="Delete Selected Drafts"
              isDeleting={isBulkDeleting}
              onBulkVerify={
                selectedRowsData.some((row) => 'draftRecord' in row) ? handleBulkVerify : undefined
              }
              isBulkVerifying={isBulkVerifying}
              onBulkAiEnhancement={handleBulkAiEnhancement}
              isBulkAiEnhancing={isBulkAiEnhancing}
              aiEnhancableSelectedCount={aiEnhancableSelectedCount}
              onRowSelectionChange={setSelectedRowsData}
              initialSorting={[{ id: 'lastUpdated', desc: true }]}
              virtualTableHeight={tableHeight}
              estimatedRowHeight={60}
              searchTerm={debouncedSearchTerm}
              resultType="drafts"
            />
          )}
        </TabsContent>
      </Tabs>

      {/* Listing preview sheet (lazy loaded) */}
      <Suspense fallback={<div>Loading preview...</div>}>
        <ListingPreviewSheet
          listing={previewListing}
          isOpen={isPreviewOpen}
          onOpenChange={setIsPreviewOpen}
        />
      </Suspense>

      {/* Modal for creating new SKU and draft */}
      <Dialog open={showSkuModal} onOpenChange={setShowSkuModal}>
        <DialogContent className="sm:max-w-md">
          {showSkuModal && effectiveSource && (
            <>
              <DialogHeader>
                <DialogTitle>Create New Item (SKU)</DialogTitle>
                <DialogDescription>
                  Create a new internal SKU and draft listing using your current workspace settings.
                </DialogDescription>
              </DialogHeader>
              <AddSkuForm
                onSubmit={handleCreateNewSkuAndDraft}
                onCancel={() => setShowSkuModal(false)}
                isSubmitting={isCreatingSkuAndDraft}
                sourceId={effectiveSource._id}
                submitButtonText="Create SKU & Draft"
                showCancelButton={true}
                autoFocusDescription={true}
              />
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Modal for saving draft as template */}
      {showSaveAsTemplateModal && currentDraftForTemplate?.uuid && (
        <CreateEditTemplateModal
          isOpen={showSaveAsTemplateModal}
          onClose={() => {
            setShowSaveAsTemplateModal(false);
            setCurrentDraftForTemplate(undefined);
          }}
          draftUuid={currentDraftForTemplate.uuid}
        />
      )}
    </div>
  );
}

// Main page export with edit price dialog context
export default function ListingsPage() {
  return (
    <EditPriceDialogProvider>
      <ListingsPageContent />
    </EditPriceDialogProvider>
  );
}
