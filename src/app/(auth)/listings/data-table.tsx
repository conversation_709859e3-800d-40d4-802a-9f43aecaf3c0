'use client';

import React, { createContext, useState, useEffect } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  PaginationState,
  ColumnFiltersState,
  getFilteredRowModel,
  VisibilityState,
  RowSelectionState,
  Row,
} from '@tanstack/react-table';

import {
  TableCell,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Trash2, Settings2, Loader2, CopyPlus, CheckCircle, Zap } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EbayListing } from './columns';
import { VirtualizedTable } from '@/components/ui/virtualized-table';

/**
 * Centralized constants for easy reconfiguration.
 */
const DATA_TABLE_CONSTANTS = {
  SEARCH_DEBOUNCE_MS: 500,
  DEFAULT_PAGE_SIZE: 50,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  ROWS_PER_PAGE_LABEL: 'Rows per page',
  NO_RESULTS_TEXT: 'No results.',
  DELETE_SELECTED_LABEL: (count: number) => `Delete Selected (${count})`,
  ADOPT_SELECTED_LABEL: (count: number) => `Adopt Selected (${count})`,
  CREATE_REVISION_DRAFTS_LABEL: (count: number) => `Create Revision Drafts (${count})`,
  BULK_VERIFY_LABEL: (count: number) => `Verify Selected (${count})`,
  BULK_AI_ENHANCE_LABEL: (count: number) => `AI Enhance (${count})`,
  VIEW_LABEL: 'View',
  PAGE_LABEL: (current: number, total: number) => `Page ${current} of ${total}`,
  LOADING_ICON_SIZE: 6,
  BULK_DELETE_ICON_SIZE: 4,
  SETTINGS_ICON_SIZE: 4,
  BUTTON_HEIGHT: '8',
  PAGINATION_WIDTH: '100',
  ROW_CLASS_BEING_DELETED: 'opacity-50 cursor-not-allowed',
  ROW_CLASS_DEFAULT: 'cursor-pointer',
  EBAY_ITEM_URL: (itemId: string) => `https://www.ebay.com/itm/${itemId}`,
};

/**
 * Supported row-level actions for listings.
 */
export type RowAction = 'updatePrice' | 'edit' | 'archive' | 'viewOnEbay';

/**
 * Data passed to row actions.
 */
export interface ActionData {
  price?: number;
  [key: string]: unknown;
}

/**
 * Context for handling row actions in the table.
 */
const RowActionContext = createContext<{
  handleActionClick: (action: RowAction, listing: EbayListing, data?: ActionData) => void;
}>({
  handleActionClick: () => { },
});

/**
 * Props for the DataTable component.
 */
interface DataTableProps<TData extends object, TValue> {
  /** Column definitions for the table. */
  columns: ColumnDef<TData, TValue>[];
  /** Array of data objects to display. */
  data: TData[];
  /** Whether the table is currently loading data. */
  isLoading?: boolean;
  /** Total number of items (for pagination/selection display). */
  totalItems?: number;
  /** Handler for updating a listing's price. */
  onUpdatePrice?: (listing: TData, price?: number) => void;
  /** Handler for editing a listing. */
  onEdit?: (listing: TData) => void;
  /** Handler for archiving a listing. */
  onArchive?: (listing: TData) => void;
  /** Handler for viewing a listing on eBay. */
  onViewOnEbay?: (listing: TData) => void;
  /** Handler for refreshing the table data. */
  onRefresh?: () => void;
  /** Handler for clicking a row. */
  onRowClick: (data: TData) => void;
  /** Handler for pagination state changes. */
  onPaginationChange: (pagination: PaginationState) => void;
  /** Total number of pages. */
  pageCount: number;
  /** Whether to display pagination controls (default: true) */
  pagination?: boolean;
  /** Handler for bulk deleting selected items. */
  onBulkDelete: (selectedIds: string[]) => void;
  /** Optional custom label for the bulk delete button. */
  bulkActionLabel?: string;
  /** Handler for bulk adopting selected items. */
  onBulkAdopt?: (selectedIds: string[]) => void;
  /** Handler for bulk creating revision drafts for selected items. */
  onBulkCreateRevisionDrafts?: (selectedIds: string[]) => void;
  /** Handler for bulk verifying selected draft items. */
  onBulkVerify?: (selectedDraftUuids: string[]) => void;
  /** Handler for bulk AI enhancement of selected items with SKUs. */
  onBulkAiEnhancement?: (selectedSkuIds: string[]) => void;
  /** Count of selected rows that are eligible for adoption. */
  unadoptedSelectedCount?: number;
  /** Count of selected rows that are eligible for AI enhancement. */
  aiEnhancableSelectedCount?: number;
  /** Loading state for the bulk delete operation. */
  isDeleting?: boolean;
  /** Loading state for the bulk adopt operation. */
  isAdopting?: boolean;
  /** Loading state for the bulk create revision drafts operation. */
  isCreatingRevisionDrafts?: boolean;
  /** Loading state for the bulk verify operation. */
  isBulkVerifying?: boolean;
  /** Loading state for the bulk AI enhancement operation. */
  isBulkAiEnhancing?: boolean;
  /** Function to extract a unique row ID from a data object. */
  getRowId: (row: TData) => string;
  /** Generic handler for custom row actions. */
  onRowAction: (actionType: string, id: string, data?: ActionData) => void;
  /** Function to determine if an item is being deleted (for UI state). */
  isItemBeingDeleted?: (id: string) => boolean;
  /** Handler for row selection changes. */
  onRowSelectionChange?: (selectedRows: TData[]) => void;
  /** Initial sorting state for the table. */
  initialSorting?: SortingState;
  /** Whether to hide the status column (useful for ended listings where all have same status). */
  hideStatusColumn?: boolean;
  /** Height for virtualized table. */
  virtualTableHeight?: number;
  /** Estimated row height for virtual scrolling. */
  estimatedRowHeight?: number;
  /** Optional search term to display in results summary. */
  searchTerm?: string;
  /** Optional category/type label for results (e.g., "live listings", "ended listings"). */
  resultType?: string;
}

/**
 * DataTableComponent renders a paginated, filterable, and sortable table for listings.
 * Supports row actions, bulk delete, column visibility, and debounced search.
 */
function DataTableComponent<TData extends object, TValue>({
  columns,
  data,
  isLoading = false,
  onUpdatePrice,
  onEdit,
  onArchive,
  onViewOnEbay,
  onRowClick,
  onPaginationChange,
  pageCount,
  onBulkDelete,
  bulkActionLabel,
  onBulkAdopt,
  onBulkCreateRevisionDrafts,
  onBulkVerify,
  onBulkAiEnhancement,
  unadoptedSelectedCount = 0,
  aiEnhancableSelectedCount = 0,
  isDeleting = false,
  isAdopting = false,
  isCreatingRevisionDrafts = false,
  isBulkVerifying = false,
  isBulkAiEnhancing = false,
  getRowId = (row: TData) => {
    // Default getRowId, assuming TData has an 'id' property.
    // This might need to be adjusted if 'id' is not guaranteed.
    return (row as { id: string }).id;
  },
  onRowAction,
  isItemBeingDeleted,
  onRowSelectionChange,
  initialSorting,
  virtualTableHeight = 600,
  estimatedRowHeight = 60,
  searchTerm,
  resultType = 'results',
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = useState<SortingState>(initialSorting || []);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(() => {
    // Set default column visibility based on screen size
    const isMobile = typeof window !== 'undefined' && window.innerWidth < 1024;
    if (isMobile) {
      // Mobile: only show essential columns for both ebay and draft tables
      return {
        image: false,
        title: true,
        price: true,
        // Hide these on mobile - works for both ebay and draft tables
        actions: false,
        status: false,
        timeLeft: false,
        timeActive: false, // Hide timeActive on mobile
        lastUpdated: false,
        select: false,
        state: false,
        sku: false,
        type: false,
        quantity: false,
      } as VisibilityState;
    }
    // Desktop: show all columns by default, but hide status and timeActive if requested
    const visibility = {
      timeActive: false, // Hide timeActive by default on desktop
      actions: false, // Hide actions by default
      status: false,  // Hide status by default
    } as VisibilityState;
    return visibility;
  });
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [paginationState, setPaginationState] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: DATA_TABLE_CONSTANTS.DEFAULT_PAGE_SIZE,
  });

  /**
   * Handles row action clicks and dispatches to the appropriate handler.
   * Prevents row click event when an action is triggered.
   */
  const handleActionClick = (action: RowAction, listing: EbayListing, data?: ActionData) => {
    switch (action) {
      case 'updatePrice':
        onUpdatePrice?.(listing as TData, data?.price);
        break;
      case 'edit':
        onEdit?.(listing as TData);
        break;
      case 'archive':
        onArchive?.(listing as TData);
        break;
      case 'viewOnEbay':
        if (onViewOnEbay) {
          onViewOnEbay(listing as TData);
        } else {
          // Default: open the listing on eBay in a new tab
          if (listing.ebayItemId) {
            window.open(DATA_TABLE_CONSTANTS.EBAY_ITEM_URL(listing.ebayItemId), '_blank');
          }
        }
        break;
      default:
        // Fallback to generic row action handler
        onRowAction(action, getRowId(listing as unknown as TData), data);
        break;
    }
  };

  // Memoize columns and data to provide stable references
  const memoColumns = React.useMemo(() => columns, [columns]);
  const memoData = React.useMemo(() => data, [data]);

  // Create the table instance with all features enabled, using memoized refs
  const table = useReactTable({
    data: memoData,
    columns: memoColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination: paginationState,
    },
    pageCount,
    manualPagination: true,
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function' ? updater(paginationState) : updater;
      setPaginationState(newPagination);
      onPaginationChange(newPagination);
    },
    getRowId,
  });

  // Effect to call onRowSelectionChange when selection changes
  const rowSelectionState = table.getState().rowSelection;
  useEffect(() => {
    if (onRowSelectionChange) {
      const selectedData = table.getSelectedRowModel().rows.map((row) => row.original);
      onRowSelectionChange(selectedData);
    }
  }, [rowSelectionState, onRowSelectionChange, table]);

  /**
   * Handles bulk delete of selected rows.
   * Calls onBulkDelete with selected IDs and clears selection.
   */
  const handleBulkDelete = () => {
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => getRowId(row.original));
    onBulkDelete(selectedIds);
    setRowSelection({});
  };

  const handleBulkAdoptClick = () => {
    if (!onBulkAdopt) return;
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => getRowId(row.original));
    onBulkAdopt(selectedIds);
    setRowSelection({}); // Clear selection after initiating adopt
  };

  const handleBulkCreateRevisionDraftsClick = () => {
    if (!onBulkCreateRevisionDrafts) return;
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => getRowId(row.original));
    onBulkCreateRevisionDrafts(selectedIds);
    // Potentially clear selection, or let the parent component decide
    // setRowSelection({});
  };

  const handleBulkVerifyClick = () => {
    if (!onBulkVerify) return;
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => getRowId(row.original));
    onBulkVerify(selectedIds);
    // setRowSelection({}); // Let parent decide on clearing selection
  };

  const handleBulkAiEnhancementClick = () => {
    if (!onBulkAiEnhancement) return;
    const selectedRows = table.getSelectedRowModel().rows;
    const selectedIds = selectedRows.map((row) => getRowId(row.original));
    onBulkAiEnhancement(selectedIds);
    // setRowSelection({}); // Let parent decide on clearing selection
  };

  /**
   * Renders a single table row, handling click and deletion state.
   */
  type RowComponentProps = { row: Row<TData>; style?: React.CSSProperties };

  const TableRowInner = ({ row, style }: RowComponentProps) => {
    const id = getRowId(row.original);
    const beingDeleted = isItemBeingDeleted ? isItemBeingDeleted(id) : false;

    // Apply style for rows being deleted or hovered
    const rowClassName = beingDeleted
      ? DATA_TABLE_CONSTANTS.ROW_CLASS_BEING_DELETED
      : DATA_TABLE_CONSTANTS.ROW_CLASS_DEFAULT;

    return (
      <TableRow
        key={row.id}
        data-state={row.getIsSelected() && 'selected'}
        className={rowClassName}
        style={style}
        onClick={(e) => {
          // Prevent row click if:
          // 1. The row is being deleted
          // 2. The event was already handled/prevented
          // 3. The click originated from a button, menu item, or open dropdown
          const isActionClick =
            e.target instanceof Element &&
            (e.target.closest('button') ||
              e.target.closest('[role="menuitem"]') ||
              e.target.closest('[data-state="open"]'));

          if (!beingDeleted && !e.defaultPrevented && !isActionClick) {
            onRowClick(row.original);
          }
        }}
      >
        {row.getVisibleCells().map((cell) => (
          <TableCell
            key={cell.id}
            className="px-1 sm:px-2 md:px-4 py-1 sm:py-2 md:py-4 text-xs sm:text-sm"
          >
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </TableCell>
        ))}
      </TableRow>
    );
  };

  // Memoise so rows re-render only when selection or data reference changes
  const TableRowComponent = React.memo(TableRowInner, (prev, next) => {
    // Only update if row selection state or deletion visual state changed
    const prevSelected = prev.row.getIsSelected();
    const nextSelected = next.row.getIsSelected();
    if (prevSelected !== nextSelected) return false;

    const prevDeleted = isItemBeingDeleted
      ? isItemBeingDeleted(getRowId(prev.row.original))
      : false;
    const nextDeleted = isItemBeingDeleted
      ? isItemBeingDeleted(getRowId(next.row.original))
      : false;
    if (prevDeleted !== nextDeleted) return false;

    // If style transform changes (virtual list) allow re-render
    if (prev.style?.transform !== next.style?.transform) return false;

    // Otherwise skip render
    return true;
  });

  return (
    <RowActionContext.Provider value={{ handleActionClick }}>
      <div className="space-y-4">
        {/* Top bar: selection info, bulk actions, column visibility */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
          {/* Results summary and selection info combined */}
          <div className="flex items-center gap-4 text-xs sm:text-sm text-muted-foreground">
            <span>
              Showing <strong>{table.getFilteredRowModel().rows.length}</strong> {resultType}
              {searchTerm && (
                <>
                  {' '}matching &quot;<strong>{searchTerm}</strong>&quot;
                </>
              )}
            </span>
            <span>
              {Object.keys(rowSelection).length} of {table.getFilteredRowModel().rows.length} selected
            </span>
          </div>

          <div className="flex items-center space-x-2 flex-wrap gap-2">
            {/* Bulk delete button, visible if any rows are selected */}
            {Object.keys(rowSelection).length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleBulkDelete}
                className={`h-${DATA_TABLE_CONSTANTS.BUTTON_HEIGHT} text-xs sm:text-sm`}
                disabled={isDeleting || isAdopting || isCreatingRevisionDrafts || isBulkVerifying || isBulkAiEnhancing}
              >
                {isDeleting ? (
                  <Loader2
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2 animate-spin`}
                  />
                ) : (
                  <Trash2
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2`}
                  />
                )}
                <span className="hidden xs:inline">
                  {bulkActionLabel
                    ? bulkActionLabel
                    : DATA_TABLE_CONSTANTS.DELETE_SELECTED_LABEL(Object.keys(rowSelection).length)}
                </span>
                <span className="xs:hidden">Delete ({Object.keys(rowSelection).length})</span>
              </Button>
            )}

            {/* New Adopt Button - Show only if onBulkAdopt is provided */}
            {onBulkAdopt && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkAdoptClick}
                className={`h-${DATA_TABLE_CONSTANTS.BUTTON_HEIGHT} text-xs sm:text-sm`}
                disabled={
                  unadoptedSelectedCount === 0 ||
                  isAdopting ||
                  isDeleting ||
                  isCreatingRevisionDrafts ||
                  isBulkVerifying ||
                  isBulkAiEnhancing
                }
              >
                {isAdopting ? (
                  <Loader2
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2 animate-spin`}
                  />
                ) : (
                  <CopyPlus
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2`}
                  />
                )}
                <span className="hidden xs:inline">
                  {DATA_TABLE_CONSTANTS.ADOPT_SELECTED_LABEL(unadoptedSelectedCount)}
                </span>
                <span className="xs:hidden">Adopt ({unadoptedSelectedCount})</span>
              </Button>
            )}

            {/* New Create Revision Drafts Button - Show only if onBulkCreateRevisionDrafts is provided */}
            {onBulkCreateRevisionDrafts && Object.keys(rowSelection).length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkCreateRevisionDraftsClick}
                className={`h-${DATA_TABLE_CONSTANTS.BUTTON_HEIGHT} text-xs sm:text-sm`}
                disabled={
                  isCreatingRevisionDrafts ||
                  isDeleting ||
                  isAdopting ||
                  Object.keys(rowSelection).length === 0 ||
                  isBulkVerifying ||
                  isBulkAiEnhancing
                }
              >
                {isCreatingRevisionDrafts ? (
                  <Loader2
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2 animate-spin`}
                  />
                ) : (
                  <CopyPlus
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2`}
                  />
                )}
                <span className="hidden sm:inline">
                  {DATA_TABLE_CONSTANTS.CREATE_REVISION_DRAFTS_LABEL(
                    Object.keys(rowSelection).length,
                  )}
                </span>
                <span className="sm:hidden">Revise ({Object.keys(rowSelection).length})</span>
              </Button>
            )}

            {/* New Bulk Verify Button - Show only if onBulkVerify is provided and rows are selected */}
            {onBulkVerify && Object.keys(rowSelection).length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkVerifyClick}
                className={`h-${DATA_TABLE_CONSTANTS.BUTTON_HEIGHT} text-xs sm:text-sm`}
                disabled={
                  isBulkVerifying ||
                  isDeleting ||
                  isAdopting ||
                  isCreatingRevisionDrafts ||
                  isBulkAiEnhancing ||
                  Object.keys(rowSelection).length === 0
                }
              >
                {isBulkVerifying ? (
                  <Loader2
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2 animate-spin`}
                  />
                ) : (
                  <CheckCircle
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2`}
                  />
                )}
                <span className="hidden sm:inline">
                  {DATA_TABLE_CONSTANTS.BULK_VERIFY_LABEL(Object.keys(rowSelection).length)}
                </span>
                <span className="sm:hidden">Verify ({Object.keys(rowSelection).length})</span>
              </Button>
            )}

            {/* New Bulk AI Enhancement Button - Show only if onBulkAiEnhancement is provided and rows are selected */}
            {onBulkAiEnhancement && aiEnhancableSelectedCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkAiEnhancementClick}
                className={`h-${DATA_TABLE_CONSTANTS.BUTTON_HEIGHT} text-xs sm:text-sm`}
                disabled={
                  isBulkAiEnhancing ||
                  isDeleting ||
                  isAdopting ||
                  isCreatingRevisionDrafts ||
                  isBulkVerifying ||
                  aiEnhancableSelectedCount === 0
                }
              >
                {isBulkAiEnhancing ? (
                  <Loader2
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2 animate-spin`}
                  />
                ) : (
                  <Zap
                    className={`h-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.BULK_DELETE_ICON_SIZE} mr-1 sm:mr-2`}
                  />
                )}
                <span className="hidden sm:inline">
                  {DATA_TABLE_CONSTANTS.BULK_AI_ENHANCE_LABEL(aiEnhancableSelectedCount)}
                </span>
                <span className="sm:hidden">AI ({aiEnhancableSelectedCount})</span>
              </Button>
            )}

            {/* Column visibility dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={`h-${DATA_TABLE_CONSTANTS.BUTTON_HEIGHT} text-xs sm:text-sm`}
                >
                  <Settings2
                    className={`h-${DATA_TABLE_CONSTANTS.SETTINGS_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.SETTINGS_ICON_SIZE} mr-1 sm:mr-2`}
                  />
                  <span className="hidden sm:inline">{DATA_TABLE_CONSTANTS.VIEW_LABEL}</span>
                  <span className="sm:hidden">View</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Table display - always virtualized */}
        <VirtualizedTable
          table={table}
          height={virtualTableHeight}
          estimateSize={estimatedRowHeight}
          onRowClick={onRowClick}
          getRowId={getRowId}
          isLoading={isLoading}
          rowComponent={TableRowComponent}
          useParentScroll={false}
          loadingComponent={
            <div className="flex justify-center">
              <Loader2
                className={`h-${DATA_TABLE_CONSTANTS.LOADING_ICON_SIZE} w-${DATA_TABLE_CONSTANTS.LOADING_ICON_SIZE} animate-spin`}
              />
            </div>
          }
          emptyComponent={
            <div className="text-center text-xs sm:text-sm">
              {DATA_TABLE_CONSTANTS.NO_RESULTS_TEXT}
            </div>
          }
        />
      </div>
    </RowActionContext.Provider>
  );
}

/**
 * DataTable is a generic wrapper for DataTableComponent.
 * @param props DataTableProps for the table.
 */
export function DataTable<TData extends object, TValue>(props: DataTableProps<TData, TValue>) {
  return <DataTableComponent<TData, TValue> {...props} />;
}
