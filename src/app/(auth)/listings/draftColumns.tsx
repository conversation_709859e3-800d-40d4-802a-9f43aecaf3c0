'use client';

import { ColumnDef } from '@tanstack/react-table';
import { MoreHorizontal, ExternalLink, PackageIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { EntityStateIndicator } from '@/components/ebay/EntityStateIndicator';
import type { UserDraftData } from '@/lib/ebay/hooks/trading/useCombinedListings'; // Import the data type
import type { ItemType } from '@/generated/trading/trading';
import { formatDistanceToNow, formatCurrency } from '@/lib/utils';
import { AppImage } from '@/components/ui/AppImage'; // Added
import { UISize } from '@/lib/image-service'; // Added
import {
  determineEntityStateReactive,
  EntityState,
  getEntityStateLabelReactive,
} from '@/lib/state/useListingStateMachine';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Link from 'next/link'; // Added
import { Id } from '@convex/_generated/dataModel'; // Added for skuId type

// Assuming BasicSkuDetailForMapper might be defined in columns.tsx or a shared types file
// For now, let's define it here if not directly importable, or ensure columns.tsx exports it.
interface BasicSkuDetailForMapper {
  _id: Id<'skus'>;
  fullSku: string;
  sourceId: Id<'sources'>;
  customSkuString?: string | null;
}

// Define the UI type for the Drafts/Revisions table
export interface DraftListing {
  id: string; // Use uuid as primary key
  uuid: string;
  ebayItemId?: string | null;
  title: string;
  imageUrl?: string;
  price: number;
  currency?: string;
  state: EntityState; // Derived state
  statusLabel: string; // Human-readable state
  updatedAt: number;
  sku?: string | null; // <<< UNCOMMENTED/ENSURED PRESENT
  skuId?: Id<'skus'> | null; // <<< ADDED
  sourceId?: Id<'sources'> | null; // <<<< ADDED sourceId
  ebayListingUrl?: string | null; // For revisions
  // Full draft data for potential use
  draftRecord: UserDraftData;
}

// Mapper function for UserDraftData
export function mapToDraftListingUI(
  draft: UserDraftData,
  skuDetail?: BasicSkuDetailForMapper | null, // Accept enriched SKU detail
): DraftListing {
  const displayData = (draft.ebayDraft as Partial<ItemType>) || {};
  const derivedState = determineEntityStateReactive(draft); // Pass the whole draft record

  const price = (displayData.StartPrice?.Value ?? 0) as number;
  const currency = (displayData.StartPrice?.CurrencyID ?? 'USD') as string;

  const sourceIdToUse = draft.skuId && skuDetail ? skuDetail.sourceId : null;
  const skuStringToDisplay =
    draft.skuId && skuDetail
      ? skuDetail.fullSku || skuDetail.customSkuString
      : draft.sku; // SKU is stored at draft level, not in ebayDraft

  return {
    id: draft.uuid,
    uuid: draft.uuid,
    ebayItemId: draft.ebayItemId,
    title: (displayData.Title || '') as string,
    imageUrl: (displayData.PictureDetails?.PictureURL?.[0] || '') as string,
    price,
    currency,
    state: derivedState,
    statusLabel: getEntityStateLabelReactive(derivedState),
    updatedAt: draft.lastModifiedLocally ?? draft.updatedAt,
    sku: skuStringToDisplay, // Use prioritized SKU string
    skuId: draft.skuId,
    sourceId: sourceIdToUse, // Use determined sourceId
    ebayListingUrl: draft.ebayListingUrl,
    draftRecord: draft,
  };
}

// Actions Cell for Drafts Table
const createDraftActionsCell = () => {
  return {
    id: 'actions',
    cell: ({ row }: { row: { original: DraftListing } }) => {
      const draft = row.original;
      const isRevision = !!draft.ebayItemId;

      const handleAction = (action: string) => {
        const event = new CustomEvent('rowAction', { detail: { action, id: draft.uuid } });
        document.dispatchEvent(event);
      };

      return (
        <div className="flex items-center justify-end">
          {/* Link to live listing if it's a revision */}
          {isRevision && draft.ebayListingUrl && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(draft.ebayListingUrl!, '_blank');
                    }}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>View Live Listing</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction('edit');
                }}
              >
                {isRevision ? 'Edit Revision' : 'Edit Draft'}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction('saveAsTemplate');
                }}
              >
                Save as Template
              </DropdownMenuItem>
              {/* Add Publish action? Need to check state */}
              {/* {draft.state === EntityStates.DRAFT && ( ... ) } */}
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  handleAction('delete');
                }}
                className="text-red-600 focus:text-red-700 focus:bg-red-50"
              >
                {isRevision ? 'Discard Revision' : 'Delete Draft'}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  };
};

// Define columns for the Drafts/Revisions table
export const draftColumns: ColumnDef<DraftListing>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
        onClick={(e) => e.stopPropagation()}
      />
    ),
    enableSorting: false,
  },
  {
    accessorKey: 'state',
    header: 'Status',
    cell: ({ row }) => {
      const draft = row.original;
      const draftData = (draft.draftRecord.ebayDraft as Partial<ItemType>) || {};
      const type = draftData.ListingType;
      const isAuction = type === 'Chinese';
      const isOtherFormat = type && type !== 'FixedPriceItem' && type !== 'Chinese';
      
      return (
        <div className="flex flex-col gap-1">
          <EntityStateIndicator
            state={draft.state}
            lastSyncedWithEbay={draft.draftRecord.lastSyncedWithEbay}
            lastPushedToEbay={draft.draftRecord.lastPushedToEbay ?? draft.draftRecord.submittedAt}
            lastModifiedLocally={draft.draftRecord.lastModifiedLocally ?? draft.draftRecord.updatedAt}
            syncError={draft.draftRecord.ebayError?.message}
          />
          {isAuction && (
            <Badge variant="outline" className="bg-blue-50 text-blue-700 text-xs w-fit">
              Auction
            </Badge>
          )}
          {isOtherFormat && (
            <Badge variant="outline" className="bg-purple-50 text-purple-700 text-xs w-fit">
              {type}
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'image',
    header: 'Image',
    cell: ({ row }) => {
      const item = row.original;
      return (
        // Container defines max size, centers content, and MUST BE RELATIVE
        <div className="relative w-12 h-12 sm:w-16 sm:h-16 md:w-[96px] md:h-[96px] flex items-center justify-center overflow-hidden bg-muted rounded-sm">
          <AppImage
            src={item.imageUrl} // Pass original URL (service handles undefined)
            alt={item.title || 'Draft image'}
            uiSize={UISize.Thumbnail96} // Specify the desired size
            className="object-contain max-w-full max-h-full" // Ensure aspect ratio is kept within container
            loading="lazy"
          />
        </div>
      );
    },
  },
  {
    accessorKey: 'title',
    header: 'Title / Type',
    cell: ({ row }) => {
      const draft = row.original;
      const type = draft.ebayItemId ? 'Revision' : 'New Draft';
      return (
        <div className="flex flex-col">
          {draft.title ? (
            <span className="font-medium">{draft.title}</span>
          ) : (
            <span className="font-medium text-muted-foreground italic">Untitled Draft</span>
          )}
          <span className="text-xs text-muted-foreground">
            {type}
            {draft.ebayItemId ? ` (#${draft.ebayItemId})` : ''}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'sku',
    header: 'SKU',
    cell: ({ row }) => {
      const { sku, skuId, sourceId } = row.original;
      if (skuId && sourceId && sku) {
        // Fully linked SKU
        return (
          <Link
            href={`/stockroom/skus/id/${sourceId}/${skuId}`}
            className="hover:underline text-primary hover:text-primary-hover flex items-center gap-1 group"
            title={`View SKU: ${sku}`}
          >
            <PackageIcon className="h-4 w-4 text-primary group-hover:text-primary-hover transition-colors" />
            <span className="font-mono text-xs truncate" title={sku}>
              {sku}
            </span>
          </Link>
        );
      } else if (sku) {
        // SKU string exists but not fully linkable
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="font-mono text-xs text-muted-foreground flex items-center gap-1 cursor-default">
                  <PackageIcon className="h-4 w-4 text-gray-400 dark:text-gray-600" />
                  <span className="truncate" title={sku}>
                    {sku}
                  </span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>SKU from draft (not fully linked or source unknown)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      }
      return <span className="font-mono text-xs text-muted-foreground">-</span>; // No SKU info at all
    },
    sortingFn: 'alphanumeric',
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const draft = row.original;
      return (
        <div className="text-right w-full">
          <span className="font-mono font-semibold text-sm sm:text-base text-primary">
            {formatCurrency(draft.price, draft.currency || 'USD')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'lastUpdated',
    header: 'Last Updated',
    cell: ({ row }) => {
      const timestamp = row.original.updatedAt;
      return <span>{formatDistanceToNow(new Date(timestamp), { addSuffix: true })}</span>;
    },
  },
  createDraftActionsCell(),
];
