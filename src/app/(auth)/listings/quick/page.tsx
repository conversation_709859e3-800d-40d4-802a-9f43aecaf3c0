"use client";

import { useEffect } from 'react';
import { useNavigation } from '@/lib/state/NavigationProvider';
import QuickListingV3 from '@/components/ebay/QuickListingV3';

export default function QuickListingPage() {
  const { navigate } = useNavigation();

  // Set breadcrumbs on mount
  useEffect(() => {
    navigate('listingQuick');
  }, [navigate]);

  return (
    <div className="container mx-auto py-1 px-2 sm:px-4">
      <div className="max-w-sm sm:max-w-2xl lg:max-w-4xl mx-auto">
        <QuickListingV3 />
      </div>
    </div>
  );
}
