'use client';

import { useRouter, useParams } from 'next/navigation';
import { logger } from '@/lib/logger';
import { Button } from '@/components/ui/button';
import { Loader2, Pencil, ExternalLink, PackageIcon } from 'lucide-react';
import { useAction } from 'convex/react';
import { ListingDetailView } from '@/components/ebay/ListingDetailView';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useNavigation } from '@/lib/state/NavigationProvider';
import { useQuery } from 'convex/react';
import { api } from '@convex/_generated/api';
import Link from 'next/link';
import type { Doc } from '@convex/_generated/dataModel';
import type { ItemType } from '@/generated/trading/trading';
import { Badge } from '@/components/ui/badge';

const log = logger.create('pages:trading:listings:detail');

export default function ListingDetailPage() {
  const router = useRouter();
  const params = useParams<{ id: string }>();
  const itemIdFromRoute = params.id;

  const [isCreatingDraft, setIsCreatingDraft] = useState(false);
  const [fetchedEbayItemId, setFetchedEbayItemId] = useState<string | null>(null);

  const ebayListingData = useQuery(
    api.ebayListings.getEbayListingByItemId,
    itemIdFromRoute ? { itemId: itemIdFromRoute } : 'skip',
  ) as Doc<'ebayListings'> | null | undefined;

  const skuId = ebayListingData?.skuId;

  const skuData = useQuery(api.skus.getSkuDocById, skuId ? { skuId } : 'skip');
  const sourceId = skuData?.sourceId;

  const sourceData = useQuery(api.skus.getSourceById, sourceId ? { sourceId } : 'skip');

  const listingTitleFromData = (ebayListingData?.ebayData as Partial<ItemType>)?.Title;
  const _pageTitle =
    listingTitleFromData || (itemIdFromRoute ? `Item ${itemIdFromRoute}` : 'Listing Details');

  // Setup navigation breadcrumbs once on mount
  const { navigate } = useNavigation();

  useEffect(() => {
    if (itemIdFromRoute) {
      navigate('listingDetail', { listingId: itemIdFromRoute, id: itemIdFromRoute });
    }
  }, [navigate, itemIdFromRoute]);

  const createRevisionDraftAction = useAction(api.createRevisionDraft.createRevisionDraft);

  async function handleEditWithV2Editor() {
    setIsCreatingDraft(true);
    try {
      toast.info('Creating draft for revision...');
      const result = await createRevisionDraftAction({
        itemId: itemIdFromRoute,
        marketplaceId: 'EBAY_US', // Default to US marketplace
      });
      if (result.success && result.redirectUrl) {
        toast.success('Revision draft created, redirecting to editor');
        router.push(result.redirectUrl);
      } else {
        toast.error(result.error || 'Failed to create revision draft');
        setIsCreatingDraft(false);
      }
    } catch (error) {
      log.error('Error creating revision draft', { error, itemId: itemIdFromRoute });
      toast.error(error instanceof Error ? error.message : 'An unknown error occurred');
      setIsCreatingDraft(false);
    }
  }

  return (
    <div className="container py-8">
      <div className="flex items-center justify-between mb-6 gap-4">
        <div className="flex items-center gap-2">
          {itemIdFromRoute && (
            <Badge variant="outline" className="text-sm font-mono">
              eBay Listing ID: {itemIdFromRoute}
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2 flex-wrap justify-end">
          {skuData && sourceData && skuData.fullSku && sourceData._id && skuData._id && (
            <Button variant="outline" size="sm" asChild>
              <Link href={`/stockroom/skus/id/${sourceData._id}/${skuData._id}`}>
                <PackageIcon className="h-4 w-4 mr-2" />
                View SKU: {skuData.fullSku}
              </Link>
            </Button>
          )}
          {(fetchedEbayItemId || itemIdFromRoute) && (
            <Button variant="default" size="sm" asChild>
              <a
                href={`https://www.ebay.com/itm/${fetchedEbayItemId || itemIdFromRoute}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center"
              >
                <ExternalLink className="h-4 w-4 mr-2" /> View on eBay
              </a>
            </Button>
          )}
          <Button onClick={handleEditWithV2Editor} disabled={isCreatingDraft} size="sm">
            {isCreatingDraft ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating Draft...
              </>
            ) : (
              <>
                <Pencil className="h-4 w-4 mr-2" />
                Revise Listing
              </>
            )}
          </Button>
        </div>
      </div>

      <div>
        {itemIdFromRoute ? (
          <ListingDetailView
            identifier={itemIdFromRoute}
            onItemIdLoaded={(itemData) => {
              // Extract the ItemID from the loaded item data
              const itemId = itemData?.ItemID;
              if (itemId) {
                setFetchedEbayItemId(itemId);
              }
            }}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>Loading...</span>
          </div>
        )}
      </div>
    </div>
  );
}
