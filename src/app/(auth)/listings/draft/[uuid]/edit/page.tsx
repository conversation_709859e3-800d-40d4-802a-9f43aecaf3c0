'use client';

import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { AlertCircle, CheckCircle2, Loader2, <PERSON>cil, PlusCircle } from 'lucide-react';
import { api } from '@convex/_generated/api';
import { useQuery, useAction } from 'convex/react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ListingEditorV2 } from '@/components/ebay/ListingEditorV2';
import { ListingWorkflowTracker } from '@/components/ebay/ListingWorkflowTracker';
import { logger } from '@/lib/logger';
import { WorkflowProvider } from '@/lib/ebay/contexts/ListingWorkflowContext';
import { toast } from 'sonner';
import { useListingEditorStore, getListingEditorState } from '@/lib/state/useListingEditorMachine';
import { useNavigation } from '@/lib/state/NavigationProvider';
import { useCurrentWorkingSkuId } from '@/lib/state/useWorkspaceContext';

// Logger instance for this page/component
const log = logger.create('pages:draft:edit');

/**
 * Represents the result of attempting to publish or revise a listing.
 */
interface PublishActionResult {
  success: boolean;
  itemId?: string;
  error?: string;
  ebayErrors?: Array<{
    SeverityCode?: 'Error' | 'Warning';
    ShortMessage?: string;
    LongMessage?: string;
    ErrorCode?: string;
  }>;
}

/**
 * EditDraftListingPageV2
 *
 * Main page for editing an eBay draft listing or revising a published listing.
 * - Loads draft data from Convex using the UUID from the route.
 * - Displays the listing editor and workflow tracker.
 * - Handles publishing a new listing or revising an existing one, including error handling and user notifications.
 */
export default function EditDraftListingPageV2() {
  // Extract the draft UUID from the route parameters
  const params = useParams();
  const uuid = params.uuid as string;
  const router = useRouter();
  
  // Convex action for publishing drafts
  const publishDraftListing = useAction(api.ebayPublishing.publishDraftListing);

  // State management for publish/revise status and result
  const setPublishStatus = useListingEditorStore((state) => state.setPublishStatus);
  const [publishResult, setPublishResult] = useState<PublishActionResult | null>(null);

  // Session state management
  const { setCurrentWorkingSkuId } = useCurrentWorkingSkuId();

  // Log the UUID being edited on mount/UUID change
  useEffect(() => {
    log.info('Editing draft/revision with UUID', { uuid });
  }, [uuid]);

  // Fetch the draft data from Convex; undefined while loading, null if not found
  const draftData = useQuery(api.drafts.getDraftByUuid, uuid ? { uuid } : 'skip');

  // Fetch the SKU data if the draft has a skuId
  const skuData = useQuery(
    api.skus.getSkuDocById,
    draftData?.skuId ? { skuId: draftData.skuId } : 'skip',
  );

  // Set the current working SKU ID when draft data loads
  useEffect(() => {
    if (draftData?.skuId && skuData) {
      log.debug('Setting current working SKU ID from draft', {
        skuId: draftData.skuId,
        uuid: draftData.uuid,
        sourceId: skuData.sourceId,
        blueprintId: skuData.blueprintId,
      });
      // Call the async function without awaiting to avoid blocking
      setCurrentWorkingSkuId(draftData.skuId).catch((error) => {
        log.error('Failed to set current working SKU ID from draft', {
          error,
          skuId: draftData.skuId,
        });
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    draftData?.skuId,
    draftData?.uuid,
    skuData, // Remove setCurrentWorkingSkuId from dependencies to prevent infinite loop
  ]);
  const isLoading = draftData === undefined;

  // Convex action for verification (using bulk verify with quantity 1)
  const verifyDraftListingAction = useAction(api.ebayPublishing.verifyDraftListing);
  
  // Verify handler using bulk verify pattern
  const handleVerify = async () => {
    if (!uuid) {
      toast.error('Cannot verify: Draft UUID is missing.');
      return;
    }

    log.info('Triggering verification for draft', { uuid });
    
    try {
      const actionResult = await verifyDraftListingAction({ uuid });
      
      if (actionResult.success && actionResult.isValid) {
        // Check if this is a pseudo-verification case based on the message
        const isPseudoVerification = actionResult.message && (
          actionResult.message.includes('pseudo-verified (Duplicate UUID)') ||
          actionResult.message.includes('pseudo-verified (Matching Duplicate Listing)')
        );
        
        if (isPseudoVerification) {
          // Show user-friendly message for pseudo-verification (matches bulk verify)
          toast.success('Revision verified successfully', {
            description: 'Changes are valid and ready for publishing',
            duration: 4000,
          });
          log.info('Individual verify: Revision pseudo-verified successfully', { uuid });
        } else {
          // Show technical message for real eBay verification
          toast.success('Listing verified successfully by eBay.', {
            description: `Category ID: ${actionResult.categoryId || 'N/A'}`,
          });
          log.info('Verification successful', { uuid, categoryId: actionResult.categoryId });
        }
      } else if (actionResult.success && !actionResult.isValid && actionResult.errors && Array.isArray(actionResult.errors)) {
        // Handle pseudo-verification case (success: true, isValid: false with errors)
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const isDuplicateUuidError = actionResult.errors.some((err: any) => err.ErrorCode === '488');
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const duplicateListingError = actionResult.errors.find((err: any) => err.ErrorCode === '21919067');

        let isMatchingDuplicateListing = false;
        if (duplicateListingError && draftData?.ebayItemId) {
          const errorParamForItemId = duplicateListingError.ErrorParameters?.find(
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (p: any) => p.ParamID === '1'
          );
          if (errorParamForItemId?.Value === draftData.ebayItemId) {
            isMatchingDuplicateListing = true;
          }
        }

        if (isDuplicateUuidError || isMatchingDuplicateListing) {
          // Show user-friendly success message for pseudo-verification
          toast.success('Revision verified successfully', {
            description: 'Changes are valid and ready for publishing',
            duration: 4000,
          });
          log.info('Individual verify: Revision pseudo-verified due to expected duplicate UUID/listing error', { uuid });
        } else {
          // Show eBay validation errors for non-pseudo cases
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          actionResult.errors.forEach((err: any, idx: number) => {
            if (err.SeverityCode === 'Error') {
              toast.error(err.LongMessage || err.ShortMessage || `eBay Error: ${err.ErrorCode || 'Unknown'}`, {
                duration: 15000,
                id: `verify-error-${err.ErrorCode ?? 'unknown'}-${idx}`,
              });
            } else if (err.SeverityCode === 'Warning') {
              toast.warning(err.LongMessage || err.ShortMessage || `eBay Warning: ${err.ErrorCode || 'Unknown'}`, {
                duration: 10000,
                id: `verify-warn-${err.ErrorCode ?? 'unknown'}-${idx}`,
              });
            }
          });
        }
      } else if (!actionResult.success && actionResult.error && typeof actionResult.error === 'object') {
        // Handle eBay validation errors (bulk verify format)
        const errors = (actionResult.error as { Errors?: unknown[] }).Errors;
        if (Array.isArray(errors) && errors.length > 0) {
          // Check if this is a pseudo-verification case (revision with duplicate UUID)
          const isDuplicateUuidError = errors.some((errItem: unknown) => {
            const err = errItem as { ErrorCode?: string };
            return err.ErrorCode === '488';
          });
          
          const duplicateListingError = errors.find((errItem: unknown) => {
            const err = errItem as { ErrorCode?: string };
            return err.ErrorCode === '21919067';
          });

          let isMatchingDuplicateListing = false;
          if (duplicateListingError && draftData?.ebayItemId) {
            const err = duplicateListingError as { ErrorParameters?: Array<{ ParamID?: string; Value?: string }> };
            const errorParamForItemId = err.ErrorParameters?.find(
              (p) => p.ParamID === '1'
            );
            if (errorParamForItemId?.Value === draftData.ebayItemId) {
              isMatchingDuplicateListing = true;
            }
          }
          
          if (isDuplicateUuidError || isMatchingDuplicateListing) {
            // Show user-friendly success message for pseudo-verification
            toast.success('Revision verified successfully', {
              description: 'Changes are valid and ready for publishing',
              duration: 4000,
            });
            log.info('Individual verify: Revision pseudo-verified due to expected duplicate UUID/listing error', { uuid });
          } else {
            // Show actual eBay validation errors
            errors.forEach((errItem: unknown, idx: number) => {
              const err = errItem as { SeverityCode?: string; ErrorCode?: string; ShortMessage?: string; LongMessage?: string };
              if (err.SeverityCode === 'Error') {
                toast.error(err.LongMessage || err.ShortMessage || `eBay Error: ${err.ErrorCode || 'Unknown'}`, {
                  duration: 15000,
                  id: `verify-error-${err.ErrorCode ?? 'unknown'}-${idx}`,
                });
              } else if (err.SeverityCode === 'Warning') {
                toast.warning(err.LongMessage || err.ShortMessage || `eBay Warning: ${err.ErrorCode || 'Unknown'}`, {
                  duration: 10000,
                  id: `verify-warn-${err.ErrorCode ?? 'unknown'}-${idx}`,
                });
              } else {
                toast.info(err.LongMessage || err.ShortMessage || `eBay Issue: ${err.ErrorCode || 'Unknown'}`, {
                  duration: 10000,
                  id: `verify-info-${err.ErrorCode ?? 'unknown'}-${idx}`,
                });
              }
            });
          }
        } else {
          toast.error('eBay verification failed', {
            description: 'No specific error details provided by eBay.'
          });
        }
      } else {
        toast.error('Verification failed', {
          description: typeof actionResult.error === 'string' ? actionResult.error : 'Could not verify listing with eBay.'
        });
      }
    } catch (error) {
      log.error('Error during verification', { uuid, error });
      toast.error('Verification failed', {
        description: error instanceof Error ? error.message : 'An unknown error occurred.'
      });
    }
  };

  // Determine if this is a revision (i.e., the draft is linked to a published eBay item)
  const isRevision = !!draftData?.ebayItemId;
  // The eBay item ID being revised, if applicable
  const sourceItemId = draftData?.ebayItemId;

  // Setup navigation
  const { navigate } = useNavigation();
  
  useEffect(() => {
    const params: Record<string, string> = { uuid };

    // Use stable SKU info with revision/new status
    if (skuData?.whatIsIt && skuData?.fullSku) {
      const prefix = isRevision ? 'Revision draft' : 'New draft';
      const skuInfo = `${skuData.whatIsIt}`;
      const fullTitle = `${prefix} for SKU: ${skuInfo}`;
      
      // Truncate whole thing to fixed length (50 chars)
      const truncatedTitle = fullTitle.length > 50 
        ? fullTitle.substring(0, 47) + '...' 
        : fullTitle;
      
      params.title = truncatedTitle;
      navigate('listingEdit', params);
    } else if (!isLoading && draftData) {
      // Data is loaded but no SKU info - show fallback with revision status
      const fallbackTitle = isRevision ? 'Revision Draft' : 'New Draft';
      params.title = fallbackTitle;
      navigate('listingEdit', params);
    }
  }, [navigate, uuid, skuData?.whatIsIt, skuData?.fullSku, isRevision, isLoading, draftData]);

  /**
   * Attempt to publish a new listing or revise an existing one.
   * Handles UI state, error reporting, and navigation on success.
   */
  async function handlePublishOrRevise() {
    if (!draftData) return; // Should not be possible if button is disabled
    setPublishStatus('pending');
    setPublishResult(null);
    log.info(`Attempting to ${isRevision ? 'revise' : 'publish'} draft`, {
      uuid: draftData.uuid,
      itemId: sourceItemId,
    });

    // Result is declared here for access in finally block
    let result: PublishActionResult | null = null;

    try {
      // publishDraftListing handles both AddItem and ReviseItem logic
      result = await publishDraftListing({ uuid: draftData.uuid });
      setPublishResult(result);

      if (result.success && result.itemId) {
        toast.success(`Listing successfully ${isRevision ? 'revised' : 'published'}!`, {
          id: `publish-${uuid}`,
        });
        setPublishStatus('success');
        // Navigation is handled in finally to ensure all state/toasts are updated first
      } else {
        // If no detailed eBay errors, show a default error message
        const defaultErrorMessage = result.error || 'Unknown error during publish/revise.';
        setPublishStatus('error', defaultErrorMessage);

        let showedDetailedError = false;
        if (result.ebayErrors && result.ebayErrors.length > 0) {
          result.ebayErrors.forEach((err) => {
            if (err.SeverityCode === 'Error') {
              toast.error(
                `Publish Error: ${err.ShortMessage || err.ErrorCode || 'Unknown eBay Error'}`,
                {
                  description: err.LongMessage,
                  duration: 15000,
                },
              );
              showedDetailedError = true;
            } else if (err.SeverityCode === 'Warning') {
              toast.warning(
                `Publish Warning: ${err.ShortMessage || err.ErrorCode || 'Unknown eBay Warning'}`,
                {
                  description: err.LongMessage,
                  duration: 10000,
                },
              );
              // Warnings do not block publish, so do not set showedDetailedError
            }
          });
        }

        if (!showedDetailedError) {
          toast.error(`Failed to ${isRevision ? 'revise' : 'publish'}: ${defaultErrorMessage}`, {
            id: `publish-${uuid}`,
          });
        }
      }
    } catch (error) {
      log.error(`Error during ${isRevision ? 'revise' : 'publish'} action`, {
        uuid: draftData.uuid,
        error,
      });
      setPublishResult({
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred',
      });
      setPublishStatus('error', error instanceof Error ? error.message : 'Unknown error');
      toast.error(
        `Failed to ${isRevision ? 'revise' : 'publish'}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { id: `publish-${uuid}` },
      );
    } finally {
      // If the status is still pending (i.e., not set to success/error), reset to idle
      if (getListingEditorState().publishListingStatus === 'pending') {
        setPublishStatus('idle');
      }
      // Only redirect if the operation was a clear success
      if (result?.success && result?.itemId) {
        router.push(`/listings/${result.itemId}`);
      }
    }
  }

  // Render a loading spinner while fetching draft data
  if (isLoading) {
    return (
      <div className="container py-6">
        <div className="flex items-center justify-center p-12 flex-col">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-4" />
          <p className="text-muted-foreground">Loading draft: {uuid}</p>
        </div>
      </div>
    );
  }

  // Render an error alert if the draft could not be found after loading
  if (!draftData) {
    return (
      <div className="container py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Draft Not Found</AlertTitle>
          <AlertDescription>
            Could not load draft with UUID: {uuid}. It might have been deleted or does not belong to
            your account.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Main page rendering: editor, workflow tracker, and status alerts
  return (
    <div className="container py-3 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {/* Space-efficient, friendly status pill */}
          {isRevision && sourceItemId ? (
            <span className="inline-flex items-center gap-1 rounded-full bg-blue-50 text-blue-700 px-3 py-1 text-sm font-medium border border-blue-200">
              <Pencil className="w-4 h-4 text-blue-500" aria-hidden="true" />
              Revising&nbsp;
              <a
                href={`https://www.ebay.com/itm/${sourceItemId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="underline hover:text-blue-900 font-medium"
                title={`View live listing ${sourceItemId} on eBay`}
              >
                eBay #{sourceItemId}
              </a>
            </span>
          ) : (
            <span className="inline-flex items-center gap-1 rounded-full bg-green-50 text-green-700 px-3 py-1 text-sm font-medium border border-green-200">
              <PlusCircle className="w-4 h-4 text-green-500" aria-hidden="true" />
              New eBay Listing Draft
            </span>
          )}
        </div>
      </div>

      {/* Alert showing the result of the publish or revise operation */}
      {publishResult && (
        <Alert variant={publishResult.success ? 'default' : 'destructive'} className="mb-4">
          {publishResult.success ? (
            <CheckCircle2 className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertTitle>
            {publishResult.success ? 'Operation Successful' : 'Operation Failed'}
          </AlertTitle>
          <AlertDescription>
            {publishResult.success
              ? `Listing ${isRevision ? 'revised' : 'published'} with ID: ${publishResult.itemId}. Redirecting...`
              : publishResult.error || 'An error occurred during the operation.'}
          </AlertDescription>
        </Alert>
      )}

      <WorkflowProvider uuid={uuid}>
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-[1fr_300px]">
          <ListingEditorV2 uuid={uuid} useExternalWorkflowProvider={true} className="w-full" />

          <div className="space-y-6 w-full">
            <div className="sticky top-4">
              <ListingWorkflowTracker
                onPublish={handlePublishOrRevise}
                onVerify={handleVerify}
                isRevision={isRevision}
              />
            </div>
          </div>
        </div>
      </WorkflowProvider>
    </div>
  );
}
