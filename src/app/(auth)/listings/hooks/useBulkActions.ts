import { useCallback, useState, useMemo } from 'react';
import { useMutation, useAction } from 'convex/react';
import { api } from '@convex/_generated/api';
import type { Id } from '@convex/_generated/dataModel';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import type { EbayListingUI } from '../columns';
import type { DraftListing as DraftListingUI } from '../draftColumns';

const log = logger.create('hooks:useBulkActions');

interface UseBulkActionsProps {
  selectedRowsData: (EbayListingUI | DraftListingUI)[];
  setSelectedRowsData: (data: (EbayListingUI | DraftListingUI)[]) => void;
  revisionsMap?: Record<string, { uuid: string }> | null;
  currentMarketplaceId?: string;
}

export function useBulkActions({
  selectedRowsData,
  setSelectedRowsData,
  revisionsMap,
  currentMarketplaceId,
}: UseBulkActionsProps) {
  // Loading states
  const [isAdopting, setIsAdopting] = useState(false);
  const [isCreatingRevisionDrafts, setIsCreatingRevisionDrafts] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);
  const [isBulkVerifying, setIsBulkVerifying] = useState(false);
  const [isBulkAiEnhancing, setIsBulkAiEnhancing] = useState(false);

  // Convex mutations and actions
  const deleteDraftMutation = useMutation(api.drafts.deleteDraft);
  const performAdoptEbayListings = useMutation(api.adoption.adoptEbayListings);
  const deleteEbayListingRecordMutation = useMutation(api.ebayListingsActions.deleteEbayListingRecord);
  const createRevisionDraftAction = useAction(api.createRevisionDraft.createRevisionDraft);
  const verifyDraftListingAction = useAction(api.ebayPublishing.verifyDraftListing);
  const startSingleSkuAiEnhancementAction = useMutation(api.singleSkuAiEnhancementWorkflow.startSingleSkuAiEnhancementWorkflow);

  // Calculate adoption counts
  const adoptableSelectedItemIds = useMemo(() => {
    return selectedRowsData
      .filter((row): row is EbayListingUI => 'ebayListingRecord' in row && !row.skuId)
      .map((row) => row.ebayItemId as string);
  }, [selectedRowsData]);

  const unadoptedSelectedCount = useMemo(
    () => adoptableSelectedItemIds.length,
    [adoptableSelectedItemIds],
  );

  // Calculate SKU IDs that can be AI enhanced (listings and drafts with SKUs)
  const aiEnhancableSelectedSkuIds = useMemo(() => {
    return selectedRowsData
      .filter((row) => {
        // Include eBay listings with SKUs
        if ('ebayListingRecord' in row && !!row.skuId) {
          return true;
        }
        // Include drafts with SKUs
        if ('draftRecord' in row && !!row.skuId) {
          return true;
        }
        return false;
      })
      .map((row) => row.skuId as string);
  }, [selectedRowsData]);

  const aiEnhancableSelectedCount = useMemo(
    () => aiEnhancableSelectedSkuIds.length,
    [aiEnhancableSelectedSkuIds],
  );

  /**
   * Handler for bulk deletion of drafts.
   */
  const handleBulkDeleteDrafts = useCallback(
    async (uuids: string[]) => {
      if (uuids.length === 0) return;
      log.info(`Bulk delete drafts action triggered for ${uuids.length} items`);

      const confirmDelete = window.confirm(
        `Are you sure you want to delete ${uuids.length} drafts? This action cannot be undone.`,
      );
      if (!confirmDelete) return;

      const toastId = toast.loading(`Deleting ${uuids.length} drafts...`);
      let successCount = 0;
      let errorCount = 0;
      setIsBulkDeleting(true);
      try {
        for (const uuid of uuids) {
          try {
            await deleteDraftMutation({ uuid });
            successCount++;
          } catch (error) {
            errorCount++;
            log.error('Failed to delete draft in bulk operation', { error, uuid });
          }
        }
        if (errorCount === 0) {
          toast.success(`Successfully deleted ${successCount} drafts.`, { id: toastId });
        } else {
          toast.error(
            `Finished deleting drafts: ${successCount} succeeded, ${errorCount} failed.`,
            { id: toastId },
          );
        }
      } finally {
        setIsBulkDeleting(false);
      }
    },
    [deleteDraftMutation],
  );

  /**
   * Handler for bulk deletion of ebayListings records.
   */
  const handleBulkDeleteEbayListingRecords = useCallback(
    async (itemIds: string[]) => {
      if (itemIds.length === 0) return;
      log.info(
        `Bulk delete local eBay listing records action triggered for ${itemIds.length} items`,
      );

      const confirmDelete = window.confirm(
        `DEVELOPER ACTION:\nAre you sure you want to HARD DELETE ${itemIds.length} local eBay listing sync records?\n\n- This does NOT affect the actual listing on eBay.\n- These records will be re-synced from eBay if the listings are still live.\n- This is primarily a tool for resetting local cache/sync state for specific items.`,
      );
      if (!confirmDelete) return;

      const toastId = toast.loading(`Deleting ${itemIds.length} local eBay listing records...`);
      let successCount = 0;
      let errorCount = 0;
      setIsBulkDeleting(true);

      try {
        for (const ebayItemId of itemIds) {
          try {
            const result = await deleteEbayListingRecordMutation({ ebayItemId });
            if (result.success) {
              successCount++;
            } else {
              errorCount++;
              log.warn('Failed to delete local eBay listing record in bulk operation', {
                error: result.message,
                ebayItemId,
              });
            }
          } catch (error) {
            errorCount++;
            log.error('Exception during bulk deletion of local eBay listing record', {
              error,
              ebayItemId,
            });
          }
        }

        if (errorCount === 0) {
          toast.success(`Successfully deleted ${successCount} local eBay listing records.`, {
            id: toastId,
          });
        } else {
          toast.error(
            `Finished deleting local records: ${successCount} succeeded, ${errorCount} failed. Check logs for details.`,
            { id: toastId, duration: 8000 },
          );
        }
      } finally {
        setIsBulkDeleting(false);
        setSelectedRowsData([]);
      }
    },
    [deleteEbayListingRecordMutation, setSelectedRowsData],
  );

  /**
   * Handler for bulk adoption of eBay listings.
   */
  const handleBulkAdopt = useCallback(async () => {
    if (unadoptedSelectedCount === 0) {
      toast.info('No unadopted listings selected for adoption.');
      return;
    }
    log.info(`Bulk adopt action triggered for ${unadoptedSelectedCount} adoptable items`, {
      itemIds: adoptableSelectedItemIds,
    });

    const confirmAdopt = window.confirm(
      `This will create internal SKUs for ${unadoptedSelectedCount} selected eBay listings using default settings ('Imported-ITM-ITEMID'). Continue?`,
    );
    if (!confirmAdopt) return;

    setIsAdopting(true);
    const toastId = toast.loading(`Adopting ${unadoptedSelectedCount} eBay listings...`);
    try {
      const result = await performAdoptEbayListings({ itemIds: adoptableSelectedItemIds });
      if (result.success) {
        toast.success(
          `Successfully adopted ${result.adoptedCount} listings. ${result.skippedCount} already had SKUs.`,
          { id: toastId },
        );
      } else {
        toast.error(
          `Adoption finished: ${result.adoptedCount} adopted, ${result.skippedCount} skipped, ${result.errors.length} errors. Check console for details.`,
          { id: toastId, duration: 8000 },
        );
        result.errors.forEach((err) =>
          log.error('[handleBulkAdopt] Adoption Error:', {
            itemId: err.itemId,
            message: err.message,
          }),
        );
      }
      setSelectedRowsData([]);
    } catch (error) {
      log.error('Bulk adopt mutation failed', { error });
      toast.error(
        `Bulk adoption failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { id: toastId },
      );
    } finally {
      setIsAdopting(false);
    }
  }, [adoptableSelectedItemIds, unadoptedSelectedCount, performAdoptEbayListings, setSelectedRowsData]);

  /**
   * Handler for bulk creation of revision drafts.
   */
  const handleBulkCreateRevisionDrafts = useCallback(
    async (itemIdsToProcess: string[]) => {
      if (!itemIdsToProcess || itemIdsToProcess.length === 0) {
        toast.info('No items selected to create revision drafts.');
        return;
      }

      log.info(`Bulk create revision drafts action triggered for ${itemIdsToProcess.length} items`);
      setIsCreatingRevisionDrafts(true);
      const toastId = toast.loading(
        `Creating revision drafts for ${itemIdsToProcess.length} items...`,
      );

      let successCount = 0;
      let failureCount = 0;
      let alreadyExistsCount = 0;
      let skuRequiredCount = 0;

      const eligibleItems = selectedRowsData
        .filter(
          (row): row is EbayListingUI =>
            'ebayListingRecord' in row && !!row.ebayItemId && !revisionsMap?.[row.ebayItemId],
        )
        .map((row) => row.ebayItemId as string)
        .filter((id) => itemIdsToProcess.includes(id));

      const trulyAlreadyExists = itemIdsToProcess.filter((id) => revisionsMap?.[id]).length;
      alreadyExistsCount += trulyAlreadyExists;

      for (const itemId of eligibleItems) {
        if (revisionsMap && revisionsMap[itemId]) {
          alreadyExistsCount++;
          continue;
        }
        try {
          const result = await createRevisionDraftAction({
            itemId,
            marketplaceId: currentMarketplaceId || 'EBAY_US',
          });
          if (result.success) {
            successCount++;
          } else {
            if (result.error?.includes('SKU')) {
              skuRequiredCount++;
              log.warn(`SKU association required for item ${itemId}`, { error: result.error });
            } else {
              failureCount++;
              log.error(`Failed to create revision draft for item ${itemId}`, {
                error: result.error,
              });
            }
          }
        } catch (error) {
          failureCount++;
          log.error(`Exception creating revision draft for item ${itemId}`, { error });
        }
      }

      let summaryMessage = '';
      if (successCount > 0)
        summaryMessage += `Successfully created ${successCount} revision drafts. `;
      if (alreadyExistsCount > 0)
        summaryMessage += `${alreadyExistsCount} items already had revisions. `;
      if (skuRequiredCount > 0)
        summaryMessage += `${skuRequiredCount} items require SKU association. `;
      if (failureCount > 0) summaryMessage += `${failureCount} attempts failed. `;

      if (successCount === itemIdsToProcess.length) {
        toast.success(summaryMessage.trim() || 'All revision drafts created successfully.', {
          id: toastId,
        });
      } else if (successCount > 0) {
        toast.warning(summaryMessage.trim(), { id: toastId, duration: 8000 });
      } else {
        toast.error(summaryMessage.trim() || 'Failed to create any revision drafts. Check logs.', {
          id: toastId,
          duration: 8000,
        });
      }

      setIsCreatingRevisionDrafts(false);
    },
    [revisionsMap, selectedRowsData, createRevisionDraftAction, currentMarketplaceId],
  );

  /**
   * Handler for bulk verification of selected drafts with eBay.
   * Handles eBay API responses, including duplicate UUID and duplicate listing policy errors.
   */
  const handleBulkVerify = useCallback(
    async (draftUuids: string[]) => {
      if (!draftUuids || draftUuids.length === 0) {
        toast.info('No drafts selected for verification.');
        return;
      }
      log.info(`Bulk verify action triggered for ${draftUuids.length} drafts`);
      setIsBulkVerifying(true);
      const toastId = toast.loading(`Verifying ${draftUuids.length} drafts with eBay...`);

      try {
        let verifiedCount = 0;
        let warningCount = 0;
        let errorCount = 0;
        let actionFailedCount = 0;
        // Note: pseudoVerifiedCount removed since Convex handleVerificationResponse 
        // already converts 488/21919067 errors to success responses

        // Only verify actual drafts from the selection
        const draftsToVerify = selectedRowsData.filter(
          (row): row is DraftListingUI => 'draftRecord' in row && draftUuids.includes(row.uuid),
        );

        if (draftsToVerify.length !== draftUuids.length) {
          log.warn(
            'Some selected items for bulk verify were not drafts or not found in selectedRowsData',
            {
              totalSelected: draftUuids.length,
              eligibleForVerify: draftsToVerify.length,
            },
          );
        }

        if (draftsToVerify.length === 0) {
          toast.info('No eligible drafts were found in the selection to verify.', { id: toastId });
          return;
        }

        for (const draftItem of draftsToVerify) {
          const uuid = draftItem.uuid;
          const associatedEbayItemId = draftItem.ebayItemId;

          try {
            const actionResult = await verifyDraftListingAction({ uuid });

            if (actionResult.success) {
              if (actionResult.isValid) {
                verifiedCount++;
                if (actionResult.errors && actionResult.errors.length > 0) {
                  warningCount++;
                  const title = draftItem.title || 'Untitled Listing';
                  const warningMsg = actionResult.errors[0]?.ShortMessage || 'Warning';

                  // Show individual toast for this specific warning with the listing title
                  toast.warning(`${title}: ${warningMsg}`, { duration: 6000 });

                  log.warn(`Draft ${uuid} verified with warnings`, {
                    warnings: actionResult.errors,
                  });
                }
              } else {
                // This should not happen since Convex handleVerificationResponse converts
                // 488/21919067 errors to success:true, isValid:true, but handle gracefully
                errorCount++;
                const title = draftItem.title || 'Untitled Listing';
                const errorMsg = actionResult.errors?.[0]?.ShortMessage || 'Validation failed';
                
                toast.error(`${title}: ${errorMsg}`, { duration: 8000 });
                log.error(`Draft ${uuid} failed eBay validation`, {
                  errors: actionResult.errors,
                  associatedEbayItemId,
                });
              }
            } else {
              // Note: 488/21919067 errors are already handled by Convex handleVerificationResponse
              // and converted to success:true responses, so this should only be genuine failures
              actionFailedCount++;
              const title = draftItem.title || 'Untitled Listing';
              const errorMsg = actionResult.error || 'Verification action failed';
              
              toast.error(`${title}: ${errorMsg}`, { duration: 8000 });
              log.error(`Verification action failed for draft ${uuid}`, { 
                actionResult,
                associatedEbayItemId 
              });
            }
          } catch (err) {
            actionFailedCount++;
            const title = draftItem.title || 'Untitled Listing';
            toast.error(`${title}: ${err instanceof Error ? err.message : 'Verification failed'}`, {
              duration: 8000,
            });
            log.error(`Exception during verification for draft ${uuid}`, { error: err });
          }
        }

        let summaryMessage = 'Verification complete: ';
        const messages = [];
        if (verifiedCount > 0) messages.push(`${verifiedCount} verified`);
        // Note: revisions are now counted as regular 'verified' since Convex handles pseudo-verification
        if (warningCount > 0) messages.push(`${warningCount} had warnings`);
        if (errorCount > 0) messages.push(`${errorCount} had eBay validation errors`);
        if (actionFailedCount > 0) messages.push(`${actionFailedCount} failed to process`);

        summaryMessage += messages.join(', ') || 'No items processed.';

        if (actionFailedCount > 0 || errorCount > 0) {
          toast.error(summaryMessage, { id: toastId, duration: 8000 });
        } else if (warningCount > 0) {
          toast.warning(summaryMessage, { id: toastId, duration: 8000 });
        } else if (verifiedCount > 0) {
          toast.success(summaryMessage, { id: toastId });
        } else {
          toast.info(summaryMessage, { id: toastId });
        }
      } catch (error) {
        log.error('Unexpected error during bulk verification', { error, draftUuids });
        toast.error(
          `Error during bulk verification: ${error instanceof Error ? error.message : 'Unknown error'}`,
          { id: toastId },
        );
      } finally {
        // Guarantee state is always reset regardless of errors
        setIsBulkVerifying(false);
      }
    },

    [selectedRowsData, verifyDraftListingAction],
  );

  /**
   * Handler for bulk AI enhancement of selected listings with SKUs.
   * Uses individual SKU workflows in parallel for better scalability.
   */
  const handleBulkAiEnhancement = useCallback(async () => {
    if (aiEnhancableSelectedCount === 0) {
      toast.info('No listings with SKUs selected for AI enhancement.');
      return;
    }
    log.info(`Bulk AI enhancement action triggered for ${aiEnhancableSelectedCount} listings`, {
      skuIds: aiEnhancableSelectedSkuIds,
    });

    const confirmEnhancement = window.confirm(
      `This will enhance descriptions for ${aiEnhancableSelectedCount} selected listings using AI. This may take several minutes. Continue?`,
    );
    if (!confirmEnhancement) return;

    setIsBulkAiEnhancing(true);
    const toastId = toast.loading(`AI enhancing ${aiEnhancableSelectedCount} listings...`);
    try {
      // Start individual workflows in parallel for each SKU
      const workflowPromises = aiEnhancableSelectedSkuIds.map(skuId => 
        startSingleSkuAiEnhancementAction({ 
          skuId: skuId as Id<"skus">,
        })
      );
      
      const workflowIds = await Promise.all(workflowPromises);
      
      toast.success(
        `${aiEnhancableSelectedCount} AI enhancement workflows started! Processing in parallel in the background.`,
        { id: toastId, duration: 6000 },
      );
      
      // Show additional info toast about monitoring progress
      toast.info(
        'Individual workflows are now running in parallel. This process may take several minutes.',
        { duration: 8000 }
      );
      
      setSelectedRowsData([]);
      log.info('Individual SKU AI enhancement workflows started', { 
        workflowIds, 
        skuCount: aiEnhancableSelectedCount 
      });
    } catch (error) {
      log.error('Bulk AI enhancement failed', { error });
      toast.error(
        `Bulk AI enhancement failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { id: toastId },
      );
    } finally {
      setIsBulkAiEnhancing(false);
    }
  }, [aiEnhancableSelectedSkuIds, aiEnhancableSelectedCount, startSingleSkuAiEnhancementAction, setSelectedRowsData]);

  return {
    // Loading states
    isAdopting,
    isCreatingRevisionDrafts,
    isBulkDeleting,
    isBulkVerifying,
    isBulkAiEnhancing,

    // Computed values
    unadoptedSelectedCount,
    adoptableSelectedItemIds,
    aiEnhancableSelectedCount,
    aiEnhancableSelectedSkuIds,

    // Action handlers
    handleBulkDeleteDrafts,
    handleBulkDeleteEbayListingRecords,
    handleBulkAdopt,
    handleBulkCreateRevisionDrafts,
    handleBulkVerify,
    handleBulkAiEnhancement,
  };
}