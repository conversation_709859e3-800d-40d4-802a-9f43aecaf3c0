'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Id } from '@convex/_generated/dataModel';
import type { ItemType } from '@/generated/trading/trading';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '@convex/_generated/api';
import { logger } from '@/lib/logger';
import { SkuActivityView } from '@/components/ebay/SkuActivityView';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Loader2, FileText, BookOpen, Sparkles, Edit3, Link2, Camera } from 'lucide-react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { formatDistanceToNow } from 'date-fns';
import { ResearchNoteModal } from '@/components/ebay/ResearchNoteModal';
import { useListingAiEnhancer } from '@/lib/ebay/hooks/useListingAiEnhancer';
import { Input } from '@/components/ui/input';
import Image from 'next/image';
import { getImageUrl, UISize } from '@/lib/image-service';
import {
  determineEntityStateReactive,
  getEntityStateLabelReactive,
  EntityTimestamps,
} from '@/lib/state/useListingStateMachine';
import { marked } from 'marked';
import { useNavigation } from '@/lib/state/NavigationProvider';
import { ProductSelector } from '@/components/ebay/ProductSelector';
import type { ProductDetails } from '@/lib/ebay/types/product-types';
import { Textarea } from '@/components/ui/textarea';
import { useCurrentWorkingSkuId, useCurrentMarketplaceId, useCurrentFoundationBlueprintId } from '@/lib/state/useWorkspaceContext';

const log = logger.create('app:inventory:skus:detail');

// Component for Quick Note Entry
function QuickResearchNoteEntry({ skuId }: { skuId: Id<'skus'> }) {
  const [note, setNote] = useState('');
  const [isSavingNote, setIsSavingNote] = useState(false);
  const addNoteMutation = useMutation(api.skuMutations.addResearchNote);

  const handleSaveNote = async () => {
    if (!note.trim()) {
      toast.error('Note cannot be empty.');
      return;
    }
    setIsSavingNote(true);
    const toastId = toast.loading('Saving note...');
    try {
      await addNoteMutation({ skuId, note: note.trim(), source: 'SKU Detail Quick Note' });
      toast.success('Note saved to activity log.', { id: toastId });
      setNote(''); // Clear textarea after saving
    } catch (error) {
      log.error('Error saving quick research note', { error, skuId });
      toast.error('Failed to save note.', { id: toastId });
    } finally {
      setIsSavingNote(false);
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base lg:text-lg flex items-center">
          <FileText className="h-4 w-4 lg:h-5 lg:w-5 mr-2 text-blue-500" /> Quick Research / AI Note
        </CardTitle>
        <CardDescription className="text-xs">
          Add quick notes, observations, or instructions for AI. These are saved to the SKU&apos;s
          activity log.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2 pt-0">
        <Textarea
          placeholder="Jot down research findings, AI instructions, item flaws, unique attributes..."
          value={note}
          onChange={(e) => setNote(e.target.value)}
          rows={3}
          disabled={isSavingNote}
        />
        <Button onClick={handleSaveNote} disabled={isSavingNote || !note.trim()} className="w-full">
          {isSavingNote && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Note to Activity Log
        </Button>
      </CardContent>
    </Card>
  );
}

// Component for Linked Listings Display
function LinkedDraftsAndListings({ skuId }: { skuId: Id<'skus'> }) {
  const linkedUserListingsData = useQuery(api.drafts.getUserListingsBySkuId, { skuId });
  const router = useRouter(); // Ensure router is available if not already in this scope

  if (linkedUserListingsData === undefined) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Linked Drafts & Revisions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }
  if (linkedUserListingsData && linkedUserListingsData.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Linked Drafts & Revisions</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm text-center">
            No drafts or revisions are currently linked to this SKU.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base lg:text-lg flex items-center">
          <Link2 className="h-4 w-4 lg:h-5 lg:w-5 mr-2 text-green-500" /> Linked Drafts & Revisions
        </CardTitle>
        <CardDescription className="text-xs">
          Active drafts or revisions associated with this SKU. Click &quot;Edit&quot; to open in the
          listing editor.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3 pt-0">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {linkedUserListingsData.map((listing) => {
            const entityTimestamps: EntityTimestamps = {
              lastSyncedWithEbay: listing.lastSyncedWithEbay,
              lastPushedToEbay: listing.lastPushedToEbay,
              lastModifiedLocally: listing.lastModifiedLocally,
              endedAt: listing.endedAt,
              syncError: listing.ebayError?.message,
            };
            const entityState = determineEntityStateReactive(entityTimestamps);
            const statusLabel = getEntityStateLabelReactive(entityState);

            return (
              <li key={listing.uuid} className="py-3 flex items-center justify-between">
                <div>
                  <p
                    className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-xs md:max-w-md"
                    title={(listing.ebayDraft as Partial<ItemType>)?.Title || 'Untitled Draft'}
                  >
                    {(listing.ebayDraft as Partial<ItemType>)?.Title || 'Untitled Draft'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    UUID: <span className="font-mono">{listing.uuid.substring(0, 8)}...</span>
                    {listing.ebayItemId && ` | eBay ID: ${listing.ebayItemId}`}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Status: {statusLabel}| Updated:{' '}
                    {formatDistanceToNow(listing.updatedAt, { addSuffix: true })}
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push(`/listings/draft/${listing.uuid}/edit`)}
                >
                  <Edit3 className="mr-2 h-3 w-3" /> Edit
                </Button>
              </li>
            );
          })}
        </ul>
      </CardContent>
    </Card>
  );
}

export default function SkuDetailPage() {
  const params = useParams();
  const sourceId = params.sourceId as Id<'sources'>;
  const skuId = params.skuId as Id<'skus'>;
  const skuData = useQuery(api.skus.getSkuDocById, { skuId });
  const researchNotesQuery = useQuery(api.skus.getSkuResearchNotesEvents, { skuId });
  const skuImages = useQuery(api.skus.getImagesBySku, { skuId });
  const linkedUserListingsData = useQuery(api.drafts.getUserListingsBySkuId, { skuId });
  const _sourceData = useQuery(api.skus.getSourceById, sourceId ? { sourceId } : 'skip');
  const createDraftFromSku = useAction(api.draftCreationPublic.createDraftFromSku);
  const updateSkuWhatIsIt = useMutation(api.skuActionsPublic.updateSkuWhatIsIt);

  // Session state management - set current working SKU context
  const { currentWorkingSkuId, setCurrentWorkingSkuId } = useCurrentWorkingSkuId();
  const { currentMarketplaceId } = useCurrentMarketplaceId();
  const { currentFoundationBlueprintId } = useCurrentFoundationBlueprintId();

  // Set the current working SKU context when viewing this SKU detail page
  // BUT be smart about it - don't override if context was recently changed
  useEffect(() => {
    if (skuData && skuId && currentWorkingSkuId !== skuId) {
      log.debug('Considering setting current working SKU context from detail page', {
        skuId,
        currentWorkingSkuId,
        sourceId: skuData.sourceId,
        blueprintId: skuData.blueprintId,
        fullSku: skuData.fullSku,
      });

      // Only set the context if the user doesn't currently have a working SKU set,
      // or if they explicitly navigated to this page (delay to avoid interfering with workflows)
      if (!currentWorkingSkuId) {
        log.debug('No current working SKU, setting context to viewed SKU', { skuId });
        setCurrentWorkingSkuId(skuId).catch((error) => {
          log.error('Failed to set current working SKU ID', { error, skuId });
        });
      } else {
        // There's already a working SKU - use a delay to avoid interfering with workflows
        const timer = setTimeout(() => {
          log.debug('Setting working SKU context after delay (user likely navigated here)', {
            skuId,
            previousWorkingSkuId: currentWorkingSkuId,
          });
          setCurrentWorkingSkuId(skuId).catch((error) => {
            log.error('Failed to set current working SKU ID', { error, skuId });
          });
        }, 2000); // 2 second delay to avoid interfering with workflows

        return () => clearTimeout(timer);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [skuId, skuData, currentWorkingSkuId]); // Include currentWorkingSkuId to detect changes

  // Setup navigation
  const { navigate } = useNavigation();
  useEffect(() => {
    navigate('skuDetail');
  }, [navigate]);

  console.log('[SkuDetailPage] skuId:', skuId);
  console.log('[SkuDetailPage] linkedUserListingsData:', linkedUserListingsData);

  const [isCreatingDraft, setIsCreatingDraft] = useState(false);
  const [isResearchNoteModalOpen, setIsResearchNoteModalOpen] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const removeSkuFromImage = useMutation(api.images.removeSkuFromImage);

  // State for editable "What is it?"
  const [whatIsItInput, setWhatIsItInput] = useState('');
  const [isSavingWhatIsIt, setIsSavingWhatIsIt] = useState(false);
  const [unassociatingImageId, setUnassociatingImageId] = useState<Id<'images'> | null>(null);

  // Added mutations for catalog info
  const updateSkuCatalog = useMutation(api.skus.updateSkuCatalogInfo);
  const clearSkuCatalog = useMutation(api.skus.clearSkuCatalogInfo);

  const { enhance } = useListingAiEnhancer({
    skuId: skuId,
  });

  // Effect to initialize whatIsItInput when skuData loads or changes
  useEffect(() => {
    if (skuData?.whatIsIt) {
      setWhatIsItInput(skuData.whatIsIt);
    }
  }, [skuData?.whatIsIt]);

  const handleSaveWhatIsIt = async () => {
    if (!whatIsItInput.trim()) {
      toast.error("'What is it?' cannot be empty.");
      return;
    }
    if (whatIsItInput.trim() === skuData?.whatIsIt) {
      toast.info("'What is it?' hasn't changed.");
      return;
    }

    setIsSavingWhatIsIt(true);
    try {
      const result = await updateSkuWhatIsIt({
        skuId,
        newWhatIsIt: whatIsItInput.trim(),
      });
      if (result.success && result.data?.newWhatIsIt) {
        toast.success("'What is it?' updated successfully!");
      } else {
        toast.error("Failed to update 'What is it?'.");
      }
    } catch (error) {
      log.error("Error saving 'What is it?'", { error, skuId, newWhatIsIt: whatIsItInput.trim() });
      toast.error('An unexpected error occurred while saving.');
    } finally {
      setIsSavingWhatIsIt(false);
    }
  };

  const handleUnassociateImage = async (imageId: Id<'images'>) => {
    // if (!window.confirm('Are you sure you want to unassociate this image from the SKU?')) {
    //   return;
    // }
    setUnassociatingImageId(imageId);
    const toastId = toast.loading('Unassociating image...');
    try {
      const result = await removeSkuFromImage({ imageId });
      if (result.success) {
        toast.success('Image unassociated and returned to repository.', { id: toastId });
      } else {
        toast.error('Failed to unassociate image.', { id: toastId });
      }
    } catch (error) {
      log.error('Error unassociating image', { error, imageId });
      toast.error('An unexpected error occurred during unassociation.', { id: toastId });
    } finally {
      setUnassociatingImageId(null);
    }
  };

  const handleCreateDraft = async () => {
    setIsCreatingDraft(true);
    try {
      const result = await createDraftFromSku({
        skuId: skuId, 
        marketplaceId: currentMarketplaceId, 
        foundationPresetId: currentFoundationBlueprintId || undefined
      });
      if (result.success && result.uuid) {
        toast.success('Draft created! Redirecting...');
        window.location.href = `/listings/draft/${result.uuid}/edit`;
      } else {
        toast.error(result.error || 'Failed to create draft');
      }
    } catch (error) {
      log.error('Error creating draft', { error, skuId });
      toast.error('Error creating draft listing');
    } finally {
      setIsCreatingDraft(false);
    }
  };

  if (!skuData) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  const researchNotesData = researchNotesQuery?.success ? researchNotesQuery.notes : [];

  // Parse valuationEstimate if it exists
  let parsedValuationEstimate: { low: number; best: number; high: number } | null = null;
  if (skuData?.valuationEstimate && typeof skuData.valuationEstimate === 'string') {
    try {
      parsedValuationEstimate = JSON.parse(skuData.valuationEstimate);
    } catch (error) {
      log.error('Failed to parse valuationEstimate from skuData', {
        skuId,
        valuationString: skuData.valuationEstimate,
        error,
      });
      // Optionally, set to a default or show an error state in UI
    }
  }

  return (
    <div className="max-w-none space-y-3 lg:space-y-4">
      {skuData && (
        <div className="flex justify-end">
          <div className="text-right">
            <h1 className="text-xl lg:text-2xl font-bold font-mono" title={skuData.fullSku}>
              {skuData.fullSku}
            </h1>
            {skuData.customSkuString && (
              <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                Original/User SKU:{' '}
                <span className="italic font-mono">{skuData.customSkuString}</span>
              </p>
            )}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 lg:gap-4 xl:gap-6 mb-4 lg:mb-6">
        {/* Left: Editable "What is it?" and AI Title */}
        <Card className="md:col-span-2 flex flex-col justify-between h-full">
          <CardHeader className="pb-3 lg:pb-4">
            <CardTitle className="text-base lg:text-lg">Item Details</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col gap-3 lg:gap-4 pt-0">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">What is it?</label>
              <div className="flex gap-2">
                <Input
                  type="text"
                  value={whatIsItInput}
                  onChange={(e) => setWhatIsItInput(e.target.value)}
                  placeholder="Describe the item (e.g., Apple iPhone 13 Pro)"
                  className="flex-grow"
                  disabled={isSavingWhatIsIt}
                />
                <Button
                  onClick={handleSaveWhatIsIt}
                  disabled={
                    isSavingWhatIsIt ||
                    whatIsItInput.trim() === skuData?.whatIsIt ||
                    !whatIsItInput.trim()
                  }
                  size="sm"
                >
                  {isSavingWhatIsIt ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                  Save
                </Button>
              </div>
            </div>
            <div className="pt-2 border-t border-dashed border-gray-200 dark:border-gray-700">
              <p className="text-xs font-medium text-gray-700 dark:text-gray-400 mb-1">
                AI Suggested eBay Title:
              </p>
              <p
                className="text-sm text-gray-900 dark:text-gray-200 italic truncate"
                title={skuData.suggestedAiTitle || 'Not set'}
              >
                {skuData.suggestedAiTitle || <span className="text-muted-foreground">Not set</span>}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Right: Actions */}
        <Card className="flex flex-col justify-between h-full">
          <CardHeader className="pb-3 lg:pb-4">
            <CardTitle className="text-base lg:text-lg">Actions</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col gap-2 lg:gap-3 pt-0">
            <Button onClick={handleCreateDraft} disabled={isCreatingDraft}>
              {isCreatingDraft && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Draft Listing
            </Button>
            <Button
              onClick={async () => {
                if (!skuData?.whatIsIt) {
                  toast.warning('Cannot enhance without SKU title (whatIsIt field).');
                  return;
                }
                setIsEnhancing(true);
                try {
                  enhance({
                    currentTitle: skuData.whatIsIt,
                  });
                } catch (error) {
                  log.error('Enhance button onClick error', error);
                } finally {
                  setTimeout(() => setIsEnhancing(false), 500);
                }
              }}
              disabled={isEnhancing || !skuData}
              variant="outline"
            >
              {isEnhancing && <Sparkles className="mr-2 h-4 w-4 animate-spin" />}
              {!isEnhancing && <Sparkles className="mr-2 h-4 w-4" />}
              AI Enhance Description (SKU)
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Research Note Entry Card */}
      {skuData && <QuickResearchNoteEntry skuId={skuData._id} />}

      {/* --- ADDED: Linked Drafts and Listings Card --- */}
      {skuId && <LinkedDraftsAndListings skuId={skuId} />}
      {/* --- END ADDED SECTION --- */}

      {/* eBay Catalog Association Section - NEW */}
      {skuData && (
        <Card>
          <CardHeader>
            <CardTitle>eBay Catalog Association</CardTitle>
          </CardHeader>
          <CardContent>
            {/* Show detected identifiers if available */}
            {skuData.identifiers && skuData.identifiers.length > 0 && (
              <div className="mb-4 p-3 bg-muted/50 rounded-lg">
                <h4 className="text-sm font-medium mb-2">Detected Product Identifiers</h4>
                <div className="flex flex-wrap gap-2">
                  {skuData.identifiers.map((identifier, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(identifier);
                        toast.success(`Copied: ${identifier}`);
                      }}
                      className="text-xs"
                    >
                      📋 {identifier}
                    </Button>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Click to copy identifier. Search below will auto-populate with first identifier.
                </p>
              </div>
            )}

            <ProductSelector
              marketplaceId={currentMarketplaceId || 'EBAY_US'}
              initialSearchTerm={
                !skuData?.catalogInfo?.epid && skuData?.identifiers?.[0]
                  ? skuData.identifiers[0]
                  : undefined
              }
              value={skuData?.catalogInfo?.epid ? { epid: skuData.catalogInfo.epid } : undefined}
              onChange={(product: ProductDetails | null) => {
                if (product && product.epid) {
                  updateSkuCatalog({ skuId: skuData._id, productDetails: product })
                    .then(() => {
                      toast.success(`SKU associated with ePID: ${product.epid}. Details logged.`);
                    })
                    .catch((err) => {
                      log.error('Failed to associate SKU with EPID', {
                        error: err,
                        skuId: skuData._id,
                        epid: product.epid,
                        productDetails: product,
                      });
                      toast.error('Failed to associate catalog product. Please try again.');
                    });
                } else if (product === null) {
                  clearSkuCatalog({ skuId: skuData._id })
                    .then(() => {
                      toast.success('Catalog association cleared.');
                    })
                    .catch((err) => {
                      log.error('Failed to clear catalog association', {
                        error: err,
                        skuId: skuData._id,
                      });
                      toast.error('Failed to clear catalog association. Please try again.');
                    });
                }
              }}
            />
            {/* --- ADDED: Display Catalog Aspects from Snapshot --- */}
            {skuData.productDetailsSnapshot &&
              (skuData.productDetailsSnapshot as ProductDetails).aspects &&
              ((skuData.productDetailsSnapshot as ProductDetails).aspects?.length ?? 0) > 0 && (
                <div className="mt-4 pt-4 border-t">
                  <h4 className="text-sm font-medium mb-2 text-muted-foreground">
                    Item Specifics from Catalog Snapshot:
                  </h4>
                  <div className="space-y-1">
                    {(skuData.productDetailsSnapshot as ProductDetails).aspects!.map(
                      (aspect, index) => (
                        <div key={index} className="text-xs p-1 rounded bg-muted/50">
                          <span className="font-semibold">
                            {aspect.localizedName || 'Unnamed Aspect'}:{' '}
                          </span>  
                          <span>
                            {aspect.localizedValues && aspect.localizedValues.length > 0
                              ? aspect.localizedValues.join(', ')
                              : 'N/A'}
                          </span>
                        </div>
                      ),
                    )}
                  </div>
                </div>
              )}
            {/* --- END ADDED SECTION --- */}
          </CardContent>
        </Card>
      )}

      {/* Static Sections: Images */}
      <div className="space-y-3 lg:space-y-4 mb-4 lg:mb-6">
        {/* Associated Images Section */}
        <Card>
          <CardHeader className="pb-3 lg:pb-4">
            <CardTitle className="text-base lg:text-lg">Associated Images</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {skuImages === undefined && (
              <div className="flex justify-center p-4">
                <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
              </div>
            )}
            {skuImages && skuImages.length === 0 && (
              <p className="text-muted-foreground text-center">
                No images are currently associated with this SKU.
              </p>
            )}
            {skuImages && skuImages.length > 0 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-2 lg:gap-3">
                {skuImages.map((image) => {
                  // Define thumbnail size
                  const thumbnailSize = 200; // Corresponds to UISize.Preview200
                  const thumbnailUrl = getImageUrl(image.bytescaleUrl, UISize.Preview200);

                  return (
                    <div
                      key={image._id}
                      className="relative group border rounded-md overflow-hidden bg-muted aspect-square"
                    >
                      <Image
                        src={thumbnailUrl}
                        alt={image.originalFilename || 'SKU Image'}
                        width={thumbnailSize}
                        height={thumbnailSize}
                        className="object-contain w-full h-full"
                        // It's good practice to still have a fallback for getImageUrl in case it returns one
                        onError={(e) => {
                          if (e.currentTarget.src !== '/placeholder-image.svg') {
                            e.currentTarget.src = '/placeholder-image.svg';
                          }
                        }}
                      />
                      <div className="absolute bottom-0 left-0 right-0 p-2 bg-black bg-opacity-50 group-hover:bg-opacity-75 transition-opacity">
                        <p className="text-xs text-white truncate" title={image.originalFilename}>
                          {image.originalFilename}
                        </p>
                        <Button
                          size="sm"
                          variant="destructive"
                          className="w-full mt-1 text-xs py-1 px-2 h-auto"
                          onClick={() => handleUnassociateImage(image._id)}
                          disabled={unassociatingImageId === image._id}
                        >
                          {unassociatingImageId === image._id ? (
                            <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                          ) : null}
                          Un-associate
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* New AI Insights Sections */}
        {skuData && (
          <>
            {/* Suggested AI Description (New Section) */}
            {skuData.suggestedAiDescription && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base lg:text-lg">AI Suggested Description</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div
                    className="prose dark:prose-invert max-w-none prose-sm lg:prose-base"
                    dangerouslySetInnerHTML={{
                      __html: marked.parse(skuData.suggestedAiDescription) as string,
                    }}
                  />
                </CardContent>
              </Card>
            )}

            {/* Suggested Item Specifics */}
            {skuData.suggestedAiAspects && skuData.suggestedAiAspects.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base lg:text-lg">
                    AI Suggested Item Specifics
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <ul className="list-disc pl-5 space-y-1 lg:space-y-2 text-sm">
                    {skuData.suggestedAiAspects.map((aspect, index) => (
                      <li key={index}>
                        <strong className="font-medium">{aspect.name}:</strong>{' '}
                        {aspect.values.join(', ')}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Suggested Subtitle */}
            {skuData.suggestedSubtitle && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base lg:text-lg">AI Suggested Subtitle</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm italic">{skuData.suggestedSubtitle}</p>
                </CardContent>
              </Card>
            )}

            {/* Valuation Estimate */}
            {parsedValuationEstimate && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base lg:text-lg">AI Valuation Estimate</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-3 gap-2 lg:gap-4 text-center pt-0">
                  <div>
                    <p className="text-xs text-muted-foreground">LOW</p>
                    <p className="text-base lg:text-lg font-semibold">
                      ${parsedValuationEstimate.low.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs font-medium text-primary">BEST ESTIMATE</p>
                    <p className="text-lg lg:text-xl font-bold text-primary">
                      ${parsedValuationEstimate.best.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">HIGH</p>
                    <p className="text-base lg:text-lg font-semibold">
                      ${parsedValuationEstimate.high.toFixed(2)}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Buyer Persona */}
            {skuData.buyerPersona && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base lg:text-lg">AI Buyer Persona</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm">{skuData.buyerPersona}</p>
                </CardContent>
              </Card>
            )}

            {/* Potential Buyer Questions */}
            {skuData.potentialBuyerQuestions && skuData.potentialBuyerQuestions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>AI Potential Buyer Questions & Concerns</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    {skuData.potentialBuyerQuestions.map((question: string, index: number) => (
                      <li key={index}>{question}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </div>

      <Tabs defaultValue="activity">
        <TabsList className="mb-2 lg:mb-3 flex flex-wrap gap-1 lg:gap-2 h-8 lg:h-10">
          <TabsTrigger value="activity" className="text-xs lg:text-sm px-2 lg:px-3">
            <FileText className="h-3 w-3 lg:h-4 lg:w-4 lg:mr-2" />
            <span className="hidden lg:inline">Activity</span>
          </TabsTrigger>
          <TabsTrigger value="imageRecognition" className="text-xs lg:text-sm px-2 lg:px-3">
            <Camera className="h-3 w-3 lg:h-4 lg:w-4 lg:mr-2" />
            <span className="hidden lg:inline">Image Recognition</span>
          </TabsTrigger>
          <TabsTrigger value="research" className="text-xs lg:text-sm px-2 lg:px-3">
            <BookOpen className="h-3 w-3 lg:h-4 lg:w-4 lg:mr-2" />
            <span className="hidden lg:inline">Research Notes Log</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="activity" className="mt-0">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base lg:text-lg">Activity</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <SkuActivityView skuId={skuData._id} displayMode="activity_only" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="imageRecognition" className="mt-0">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base lg:text-lg">Image Recognition</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <SkuActivityView skuId={skuData._id} displayMode="image_recognition_only" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="research" className="mt-0">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-base lg:text-lg">Research Notes Log</CardTitle>
              </div>
            </CardHeader>
            <CardContent className="space-y-3 lg:space-y-4 pt-0">
              {researchNotesQuery === undefined && (
                <div className="flex justify-center p-4">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              )}
              {researchNotesQuery && !researchNotesQuery.success && <p>Error loading notes</p>}
              {researchNotesData.length === 0 && researchNotesQuery?.success && (
                <p className="text-muted-foreground text-sm">No research notes added yet.</p>
              )}
              {researchNotesData.length > 0 &&
                [...researchNotesData].reverse().map((event) => (
                  <div key={event.timestamp} className="p-3 border rounded-md bg-muted/20">
                    <p className="text-xs text-muted-foreground mb-1">
                      Added {formatDistanceToNow(event.timestamp, { addSuffix: true })}
                      {event.eventData.source && ` (Source: ${event.eventData.source})`}
                    </p>
                    <p className="text-sm whitespace-pre-wrap">{event.eventData.note}</p>
                  </div>
                ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <ResearchNoteModal
        isOpen={isResearchNoteModalOpen}
        onClose={() => setIsResearchNoteModalOpen(false)}
        skuId={skuId}
      />
    </div>
  );
}
