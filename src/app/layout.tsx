import type { <PERSON><PERSON><PERSON> } from 'next';
import { Inter, Space_Mono, Space_Grotesk } from 'next/font/google';
import './globals.css';
import { ThemeSelector } from '@/components/theme-selector';
import { siteConfig } from '@/config/site';
import { SignInButton, SignedIn, SignedOut, UserButton } from '@clerk/nextjs';
import { ConvexClientProvider } from './ConvexClientProvider';
import { ThemeProvider } from '@/components/theme-provider';
import { UserSync } from '@/components/user-sync';
import { cn } from '@/lib/utils';
//import { ResponseViewer } from '@/components/ebay/ResponseViewer';
import Link from 'next/link';
import { Tag } from 'lucide-react';
import { Analytics } from '@vercel/analytics/next';
import { RaccoonIcon } from '@/components/icons/RaccoonIcon';

// Import the server module to trigger initialization
// This ensures eBay services are initialized at application startup
import '@/lib/ebay/server';
import { ClerkThemeProvider } from '@/components/clerk-theme-provider';
import { BodyOverflowManager } from '@/components/body-overflow-manager';

const inter = Inter({ subsets: ['latin'] });
const spaceMono = Space_Mono({
  weight: ['400', '700'],
  subsets: ['latin'],
  variable: '--font-space-mono',
});
const spaceGrotesk = Space_Grotesk({
  subsets: ['latin'],
  variable: '--font-space-grotesk',
});

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  keywords: [
    'eBay search',
    'eBay seller tools',
    'eBay listings',
    'AI-powered eBay search',
    'secondhand marketplace',
    'resale platform',
    'eBay automation',
    'online marketplace tools',
    'vintage shopping',
    'thrift finds',
    'collectibles search',
    'eBay power seller',
  ],
  authors: [
    {
      name: 'junkDrawer.ai',
      url: 'https://junkdrawer.ai',
    },
  ],
  creator: 'junkDrawer.ai',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name,
    images: [
      {
        url: siteConfig.ogImage,
        width: 1200,
        height: 630,
        alt: siteConfig.name,
      },
    ],
  },

  twitter: {
    card: 'summary_large_image',
    title: siteConfig.name,
    description: siteConfig.description,
    images: [siteConfig.ogImage],
    creator: '@junkDrawer_ai',
    site: '@junkDrawer_ai',
  },
  alternates: {
    canonical: siteConfig.url,
  },
  other: {
    'twitter:domain': 'junkdrawer.ai',
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/icon.svg', type: 'image/svg+xml' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [{ url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }],
    other: [
      { url: '/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
  },
  manifest: '/site.webmanifest',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
};

// // Client component wrapper for ResponseViewer
// function ResponseViewerWrapper() {
//   return (
//     <SignedIn>
//       <ResponseViewer />
//     </SignedIn>
//   );
// }

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head></head>
      <body
        className={cn(
          'bg-background font-sans antialiased overflow-hidden',
          inter.className,
          spaceMono.variable,
          spaceGrotesk.variable,
        )}
        suppressHydrationWarning
      >
        <ThemeProvider>
          <ClerkThemeProvider>
            <ConvexClientProvider>
              <UserSync />
              <BodyOverflowManager />
              <div className="min-h-screen flex flex-col">
                <header className="sticky top-0 z-50 w-full border-b bg-background">
                  <div className="container flex h-14 items-center px-2 sm:px-4">
                    {/* Left Side: Logo & Nav */}
                    <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
                      <div className="flex items-center gap-2 sm:gap-3 min-w-0">
                        <Link href="/" className="flex items-center gap-1 sm:gap-2 min-w-0">
                          <RaccoonIcon
                            className="h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0"
                            containerClassName="p-0.5"
                          />
                          <span className="font-bold text-lg sm:text-xl tracking-tight truncate">
                            junkDrawer.ai
                          </span>
                        </Link>
                      </div>
                      {/* Search link removed */}
                      {/* <nav className="hidden md:flex gap-6 ml-6">
                          <Link
                            href="/"
                            className="text-sm font-medium hover:text-primary flex items-center gap-1"
                          >
                            <Search className="h-4 w-4" />
                            Search
                          </Link>
                        </nav> */}
                    </div>

                    {/* Right Side: Auth, Links, Theme */}
                    <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-4 flex-shrink-0">
                      {/* Auth Section - Fixed width container to prevent layout shift */}
                      <div className="min-w-[180px] sm:min-w-[240px] md:min-w-[280px] flex justify-end">
                        <SignedIn>
                          <div className="flex items-center gap-1 sm:gap-2 md:gap-4">
                            {/* Seller Dashboard Link - for signed-in users */}
                            <Link
                              href="/dashboard"
                              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm bg-gradient-to-r from-primary/10 to-secondary/10 hover:from-primary/20 hover:to-secondary/20 text-primary hover:text-primary/90 px-2 sm:px-3 py-1.5 rounded-full transition-all duration-200 border border-primary/20 hover:border-primary/30"
                            >
                              <Tag className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="font-medium hidden xs:inline">Let&apos;s List</span>
                              <span className="font-medium xs:hidden">Sell</span>
                              <span className="text-xs bg-primary/20 text-primary px-1 sm:px-1.5 py-0.5 rounded-full ml-0.5 sm:ml-1">
                                Beta
                              </span>
                            </Link>
                            {/* User Account Button with Label */}
                            <div className="flex items-center">
                              <UserButton
                                afterSignOutUrl="/"
                                appearance={{
                                  elements: {
                                    avatarBox: 'w-7 h-7 sm:w-8 sm:h-8',
                                  },
                                }}
                              />
                            </div>
                          </div>
                        </SignedIn>
                        <SignedOut>
                          <div className="flex items-center gap-1 sm:gap-2">
                            {/* Seller Waitlist Link for signed out users */}
                            <Link
                              href="/waitlist"
                              className="flex items-center gap-1 sm:gap-2 text-xs sm:text-sm bg-gradient-to-r from-primary/10 to-secondary/10 hover:from-primary/20 hover:to-secondary/20 text-primary hover:text-primary/90 px-2 sm:px-3 py-1.5 rounded-full transition-all duration-200 border border-primary/20 hover:border-primary/30"
                            >
                              <Tag className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                              <span className="font-medium hidden xs:inline">
                                eBay Sellers: Let&apos;s List!
                              </span>
                              <span className="font-medium xs:hidden">Sell</span>
                              <span className="text-xs bg-primary/20 text-primary px-1 sm:px-1.5 py-0.5 rounded-full ml-0.5 sm:ml-1">
                                Soon
                              </span>
                            </Link>

                            <SignInButton mode="modal">
                              <button className="inline-flex items-center justify-center gap-1 sm:gap-2 rounded-md text-xs sm:text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 sm:h-9 px-2 sm:px-4 py-2">
                                <span className="hidden sm:inline">Sign in</span>
                                <span className="sm:hidden">Access</span>
                              </button>
                            </SignInButton>
                          </div>
                        </SignedOut>
                      </div>

                      {/* Theme selector */}
                      <div className="flex-shrink-0">
                        <ThemeSelector />
                      </div>
                    </div>
                  </div>
                </header>

                {children}
              </div>
              <Analytics />
            </ConvexClientProvider>
          </ClerkThemeProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
