'use client';

import { usePathname } from 'next/navigation';
import { useEffect } from 'react';

/**
 * Manages body overflow based on current route.
 * Preserves the working auth layout system while allowing specific pages to scroll.
 */
export function BodyOverflowManager() {
  const pathname = usePathname();

  useEffect(() => {
    // Pages that need normal scrolling (not controlled by auth layout)
    const scrollablePages = ['/', '/search', '/classic', '/about'];
    
    const needsScrolling = scrollablePages.includes(pathname);

    if (needsScrolling) {
      // Remove overflow-hidden to allow normal page scrolling
      document.body.classList.remove('overflow-hidden');
    } else {
      // Keep overflow-hidden for auth layout system (prevents double scrollbars on /listings)
      document.body.classList.add('overflow-hidden');
    }
  }, [pathname]);

  return null; // This component doesn't render anything
}