'use client';

import { useState, useRef, forwardRef, useCallback } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@convex/_generated/api';
import { useActiveEbayAccount } from '@/lib/ebay/hooks/useActiveEbayAccount';
import { Id, Doc } from '@convex/_generated/dataModel';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import {
    ContextMenu,
    ContextMenuContent,
    ContextMenuItem,
    ContextMenuTrigger,
    ContextMenuSeparator,
    ContextMenuSub,
    ContextMenuSubContent,
    ContextMenuSubTrigger,
} from '@/components/ui/context-menu';
import {
    Warehouse,
    Layers,
    PackageIcon,
    Plus,
    X,
    Check,
    ChevronsUpDown,
    Pencil,
    RefreshCw,
    Camera,
    Zap,
    Copy,
} from 'lucide-react';
import {
    useCurrentWorkingSourceId,
    useCurrentWorkingBlueprintId,
    useCurrentWorkingSkuId,
    useCurrentFoundationBlueprintId,
} from '@/lib/state/useWorkspaceContext';
import { cn, formatCompactAge } from '@/lib/utils';
import { AppImage } from '@/components/ui/AppImage';
import { UISize } from '@/lib/image-service';

import { useMutation } from 'convex/react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { CreateEditSourceModal } from '@/app/(auth)/stockroom/components/CreateEditSourceModal';
import { usePageActions } from '@/lib/navigation/page-actions-context';
import { useNavigation } from '@/lib/state/NavigationProvider';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog';

interface SessionContextBarV2Props {
    className?: string;
    compact?: boolean;
}

interface ContextCardProps {
    icon: React.ReactNode;
    label: string;
    value: string;
    subtitle?: string;
    isEmpty?: boolean;
    onTap: () => void;
    onContextMenuChange?: () => void; // Alternative action for context menu
    onClear?: () => void;
    onAdd?: () => void;
    canClear?: boolean;
    className?: string;
    // For showing options directly in context menu
    contextMenuOptions?: Array<{
        id: string;
        label: string;
        subtitle?: string;
        isSelected?: boolean;
        onClick: () => void;
    }>;
    // For special submenu cases (like SKUs from current source)
    submenuOptions?: {
        title: string;
        subtitle?: string;
        options: Array<{
            id: string;
            label: string;
            subtitle?: string;
            isSelected?: boolean;
            onClick: () => void;
        }>;
    };
}

const ContextCard = forwardRef<HTMLDivElement, ContextCardProps>(({
    icon,
    label,
    value,
    subtitle,
    isEmpty = false,
    onTap,
    onContextMenuChange,
    onClear,
    onAdd,
    canClear = false,
    className,
    contextMenuOptions,
    submenuOptions,
}, ref) => {
    const cardRef = useRef<HTMLDivElement>(null);

    // Combine refs - use the forwarded ref if available, otherwise use local ref
    const combinedRef = ref || cardRef;

    const handleClick = (_e: React.MouseEvent) => {
        console.log('ContextCard handleClick called', { onTap: typeof onTap });
        // Always call onTap for single clicks - context menu will be handled by right-click
        if (onTap) {
            onTap();
        }
    };

    return (
        <ContextMenu modal={false}>
            <ContextMenuTrigger asChild>
                <Card
                    ref={combinedRef}
                    className={cn(
                        "p-1 lg:p-1.5 xl:p-2 cursor-pointer hover:bg-muted/50 transition-colors min-w-[80px] sm:min-w-[100px] lg:min-w-[120px] flex-shrink-0",
                        isEmpty && "border-dashed border-muted-foreground/50",
                        className
                    )}
                    onClick={handleClick}
                >
                    <div className="flex items-center gap-1 lg:gap-1.5 xl:gap-2">
                        <div className="text-muted-foreground flex-shrink-0">{icon}</div>
                        <div className="flex-1 min-w-0">
                            <div className="text-[10px] lg:text-xs text-muted-foreground leading-tight">{label}</div>
                            <div className={cn(
                                "text-[11px] lg:text-xs xl:text-sm font-medium truncate leading-tight",
                                isEmpty && "text-muted-foreground"
                            )}>
                                {value}
                            </div>
                            {subtitle && (
                                <div className="text-[10px] lg:text-xs text-muted-foreground truncate hidden xl:block leading-tight">
                                    {subtitle}
                                </div>
                            )}
                        </div>
                    </div>
                </Card>
            </ContextMenuTrigger>
            <ContextMenuContent
                className="w-64"
                onCloseAutoFocus={(event) => {
                    // Prevent the context menu from stealing focus when it closes
                    event.preventDefault();
                }}
            >
                {/* Direct options in context menu */}
                {contextMenuOptions && contextMenuOptions.length > 0 && (
                    <>
                        {/* Regular selection options */}
                        {contextMenuOptions
                            .filter(option => !option.id.startsWith('__'))
                            .slice(0, 6)
                            .map((option) => (
                                <ContextMenuItem
                                    key={option.id}
                                    onClick={option.onClick}
                                    className={option.isSelected ? "bg-muted" : ""}
                                >
                                    <div className="flex items-center gap-2 flex-1">
                                        {option.isSelected && <Check className="h-4 w-4" />}
                                        {!option.isSelected && <div className="w-4" />}
                                        <div className="flex-1 min-w-0">
                                            <div className="font-medium">{option.label}</div>
                                            {option.subtitle && (
                                                <div className="text-xs text-muted-foreground truncate">
                                                    {option.subtitle}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </ContextMenuItem>
                            ))}

                        {/* Show "More..." if there are many regular options, or custom submenu */}
                        {submenuOptions ? (
                            <ContextMenuSub>
                                <ContextMenuSubTrigger>
                                    <Warehouse className="h-4 w-4 mr-2" />
                                    {submenuOptions.title}
                                </ContextMenuSubTrigger>
                                <ContextMenuSubContent className="w-64">
                                    {submenuOptions.options.slice(0, 6).map((option) => (
                                        <ContextMenuItem
                                            key={option.id}
                                            onClick={option.onClick}
                                            className={option.isSelected ? "bg-muted" : ""}
                                        >
                                            <div className="flex items-center gap-2 flex-1">
                                                {option.isSelected && <Check className="h-4 w-4" />}
                                                {!option.isSelected && <div className="w-4" />}
                                                <div className="flex-1 min-w-0">
                                                    <div className="font-medium">{option.label}</div>
                                                    {option.subtitle && (
                                                        <div className="text-xs text-muted-foreground truncate">
                                                            {option.subtitle}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </ContextMenuItem>
                                    ))}
                                </ContextMenuSubContent>
                            </ContextMenuSub>
                        ) : contextMenuOptions && contextMenuOptions.filter(option => !option.id.startsWith('__')).length > 6 && (
                            <ContextMenuSub>
                                <ContextMenuSubTrigger>
                                    <ChevronsUpDown className="h-4 w-4 mr-2" />
                                    More {label}s ({contextMenuOptions.filter(option => !option.id.startsWith('__')).length - 6} more)
                                </ContextMenuSubTrigger>
                                <ContextMenuSubContent className="w-64">
                                    {contextMenuOptions
                                        .filter(option => !option.id.startsWith('__'))
                                        .slice(6)
                                        .map((option) => (
                                            <ContextMenuItem
                                                key={option.id}
                                                onClick={option.onClick}
                                                className={option.isSelected ? "bg-muted" : ""}
                                            >
                                                <div className="flex items-center gap-2 flex-1">
                                                    {option.isSelected && <Check className="h-4 w-4" />}
                                                    {!option.isSelected && <div className="w-4" />}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="font-medium">{option.label}</div>
                                                        {option.subtitle && (
                                                            <div className="text-xs text-muted-foreground truncate">
                                                                {option.subtitle}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            </ContextMenuItem>
                                        ))}
                                </ContextMenuSubContent>
                            </ContextMenuSub>
                        )}

                        {/* Separator */}
                        {contextMenuOptions.some(option => option.id.startsWith('__') && option.id !== '__separator__') && (
                            <ContextMenuSeparator />
                        )}

                        {/* Action options */}
                        {contextMenuOptions
                            .filter(option => option.id.startsWith('__') && option.id !== '__separator__')
                            .map((option) => (
                                <ContextMenuItem key={option.id} onClick={option.onClick}>
                                    <div className="flex items-center gap-2 flex-1">
                                        {option.id === '__edit_current__' && <Pencil className="h-4 w-4" />}
                                        {option.id === '__add_new__' && <Plus className="h-4 w-4" />}
                                        {option.id === '__copy_sku__' && <Copy className="h-4 w-4" />}
                                        <div className="flex-1 min-w-0">
                                            <div className="font-medium">{option.label}</div>
                                            {option.subtitle && (
                                                <div className="text-xs text-muted-foreground truncate">
                                                    {option.subtitle}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </ContextMenuItem>
                            ))}
                    </>
                )}

                {/* Fallback "Change" option if no direct options provided */}
                {(!contextMenuOptions || contextMenuOptions.length === 0) && (
                    <ContextMenuItem onClick={() => {
                        console.log(`Context menu: Change ${label} clicked`);
                        if (onContextMenuChange) {
                            onContextMenuChange();
                        } else {
                            // Fallback to regular onTap with delay
                            requestAnimationFrame(() => {
                                console.log('Calling onTap after requestAnimationFrame');
                                onTap();
                            });
                        }
                    }}>
                        <ChevronsUpDown className="h-4 w-4 mr-2" />
                        Change {label}
                    </ContextMenuItem>
                )}

                {canClear && onClear && (
                    <ContextMenuItem onClick={onClear}>
                        <X className="h-4 w-4 mr-2" />
                        Clear to Default
                    </ContextMenuItem>
                )}
                {onAdd && (
                    <ContextMenuItem onClick={onAdd}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add New {label}
                    </ContextMenuItem>
                )}
            </ContextMenuContent>
        </ContextMenu>
    );
});

ContextCard.displayName = 'ContextCard';

interface SelectorPopoverProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    trigger: React.ReactNode;
    children: React.ReactNode;
}

function SelectorPopover({ isOpen, onOpenChange, trigger, children }: SelectorPopoverProps) {
    // console.log('SelectorPopover render - isOpen:', isOpen);

    return (
        <Popover open={isOpen} onOpenChange={onOpenChange}>
            <PopoverTrigger asChild>
                {trigger}
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="start">
                {children}
            </PopoverContent>
        </Popover>
    );
}

export function SessionContextBarV2({ className, compact = false }: SessionContextBarV2Props) {
    // Get page actions and navigation
    const { actions: pageActions } = usePageActions();
    const { switchSku, isSwitchingSku } = useNavigation();

    // State for popover controls
    const [sourceOpen, setSourceOpen] = useState(false);
    const [blueprintOpen, setBlueprintOpen] = useState(false);
    const [foundationOpen, setFoundationOpen] = useState(false);
    const [skuOpen, setSkuOpen] = useState(false);

    // State for "Add New" modals
    const [showCreateSourceModal, setShowCreateSourceModal] = useState(false);
    const [showCreateBlueprintModal, setShowCreateBlueprintModal] = useState(false);
    const [editingSource, setEditingSource] = useState<Doc<'sources'> | null>(null);

    // State for "Change" modals (alternative to popovers for context menu)
    const [showChangeSourceModal, setShowChangeSourceModal] = useState(false);

    // Current selections
    const { currentWorkingSourceId, setCurrentWorkingSourceId } = useCurrentWorkingSourceId();
    const { currentWorkingBlueprintId, setCurrentWorkingBlueprintId } = useCurrentWorkingBlueprintId();
    const { currentFoundationBlueprintId, setCurrentFoundationBlueprintId } = useCurrentFoundationBlueprintId();
    const { currentWorkingSkuId, setCurrentWorkingSkuId } = useCurrentWorkingSkuId();

    // Auth check
    const { activeAccount } = useActiveEbayAccount();

    // Data queries
    const sourceData = useQuery(
        api.skus.getSourceById,
        currentWorkingSourceId && activeAccount ? { sourceId: currentWorkingSourceId } : "skip"
    );
    const defaultSource = useQuery(
        api.skus.getSourceByCode,
        !currentWorkingSourceId && activeAccount ? { code: "JDAI" } : "skip"
    );

    const allSources = useQuery(api.skus.getSources, activeAccount ? {} : "skip");
    const allBlueprints = useQuery(api.blueprints.getAllActiveBlueprints, activeAccount ? {} : "skip");

    // Check if user has any data
    const hasAnySources = allSources && allSources.length > 0;
    const hasAnyBlueprints = allBlueprints && allBlueprints.length > 0;
    const recentSkus = useQuery(api.skus.getRecentlyActiveSkus);

    // Add query for SKUs by current source
    const skusByCurrentSource = useQuery(
        api.skus.getSkusBySource,
        currentWorkingSourceId ? { sourceId: currentWorkingSourceId } : "skip"
    );

    const currentBlueprint = allBlueprints?.find(bp => bp._id === currentWorkingBlueprintId);

    const skuData = useQuery(
        api.skus.getSkuDocById,
        currentWorkingSkuId ? { skuId: currentWorkingSkuId } : "skip"
    );

    // Get SKU thumbnails for display
    const skuImageMap = useQuery(api.skus.getSkuThumbnails) ?? {};

    // Filter blueprints by type
    const blueprints = allBlueprints?.filter(bp => bp.type === 'blueprint') || [];
    const foundations = allBlueprints?.filter(bp => bp.type === 'foundation') || [];
    const currentFoundation = foundations.find(f => f._id === currentFoundationBlueprintId);
    const hasAnyFoundations = foundations.length > 0;

    // Smart clear functions
    const clearSource = () => {
        setCurrentWorkingSourceId(defaultSource?._id || null);
    };

    const clearBlueprint = () => {
        setCurrentWorkingBlueprintId(null);
    };

    const clearFoundation = () => {
        setCurrentFoundationBlueprintId(null);
    };

    const clearSku = () => {
        setCurrentWorkingSkuId(null);
    };

    // Add New handlers
    const handleAddNewSource = () => {
        setShowCreateSourceModal(true);
        setEditingSource(null); // Ensure we're in create mode
    };

    const handleEditCurrentSource = () => {
        if (effectiveSource && currentWorkingSourceId) {
            setEditingSource(effectiveSource);
            setShowCreateSourceModal(true);
        }
    };

    // SKU CRUD handlers
    const handleEditCurrentSku = () => {
        if (currentWorkingSkuId && skuData) {
            router.push(`/stockroom/skus/id/${skuData.sourceId}/${currentWorkingSkuId}`);
        }
    };

    const handleCreateNewSku = () => {
        // Navigate to the SKU management page where creation happens via inline forms
        router.push('/stockroom/skus');
    };

    const handleCopySku = async () => {
        if (skuData?.fullSku) {
            try {
                await navigator.clipboard.writeText(skuData.fullSku);
                toast.success(`Copied SKU: ${skuData.fullSku}`);
            } catch (error) {
                console.error('Failed to copy SKU:', error);
                toast.error('Failed to copy SKU to clipboard');
            }
        }
    };

    const handleCloseCreateSourceModal = (newSourceId?: Id<'sources'>) => {
        setShowCreateSourceModal(false);
        setEditingSource(null); // Clear editing state
        // If a new source was created, make it the active source
        if (newSourceId) {
            setCurrentWorkingSourceId(newSourceId);
        }
        // The sources query will automatically refresh due to Convex reactivity
    };

    const handleAddNewBlueprint = () => {
        setShowCreateBlueprintModal(true);
    };

    const handleEditCurrentBlueprint = () => {
        // Navigate to the blueprint management page where editing happens via modals
        router.push('/blueprints');
    };

    const handleAddNewFoundation = () => {
        // Navigate to the foundations page where foundations can be created from drafts
        router.push('/foundations');
    };

    const handleEditCurrentFoundation = () => {
        // Navigate to the foundations page where foundations can be managed
        router.push('/foundations');
    };


    // Mutations
    const ensureSku = useMutation(api.skus.ensureSku);
    const router = useRouter();

    // Helper function to handle SKU switching with navigation coordination
    const handleSkuSwitch = useCallback((skuId: Id<'skus'>) => {
        // Find the SKU to get its sourceId
        const skuDoc = recentSkus?.find(sku => sku._id === skuId) ||
            skusByCurrentSource?.find(sku => sku._id === skuId);

        if (skuDoc) {
            // Use the navigation machine to coordinate SKU switching
            switchSku(skuId, skuDoc.sourceId);
        } else {
            // Fallback: set context directly if SKU not found in recent/current lists
            setCurrentWorkingSkuId(skuId);
        }
    }, [recentSkus, skusByCurrentSource, switchSku, setCurrentWorkingSkuId]);

    // Quick SKU creation
    const handleQuickAddSku = async () => {
        try {
            const effectiveSourceId = currentWorkingSourceId || defaultSource?._id;

            if (!effectiveSourceId) {
                toast.error('No source available for SKU creation');
                return;
            }

            const result = await ensureSku({
                whatIsIt: "Quick SKU",
                sourceId: effectiveSourceId,
                blueprintId: currentWorkingBlueprintId || undefined,
            });

            // Set the new SKU as current
            setCurrentWorkingSkuId(result.skuId);

            // Navigate to the SKU edit page
            router.push(`/stockroom/skus/id/${effectiveSourceId}/${result.skuId}`);

            toast.success(`Quick SKU created: ${result.fullSku}`);
        } catch (error) {
            console.error('Failed to create quick SKU:', error);
            toast.error('Failed to create SKU');
        }
    };

    // Get effective source (current or default)
    const effectiveSource = sourceData || defaultSource;

    return (
        <div className={cn("space-y-2", className)}>
            {/* Context Cards */}
            <div className="flex gap-1 overflow-x-auto pb-1 xl:grid xl:grid-cols-4 xl:overflow-visible 2xl:gap-2">
                {/* Source Card */}
                <SelectorPopover
                    isOpen={sourceOpen}
                    onOpenChange={(open) => {
                        console.log('Source popover onOpenChange:', open);
                        setSourceOpen(open);
                    }}
                    trigger={
                        <ContextCard
                            icon={<Warehouse className="h-4 w-4" />}
                            label="Working with Source"
                            value={
                                !hasAnySources ? "Create Source" :
                                    effectiveSource?.code || "None"
                            }
                            subtitle={
                                !hasAnySources ? "Add your first source" :
                                    effectiveSource ? `${effectiveSource.name} • ${formatCompactAge(effectiveSource.dateAcquired)}` : undefined
                            }
                            isEmpty={!hasAnySources || !effectiveSource}
                            onTap={() => {
                                if (!hasAnySources) {
                                    handleAddNewSource();
                                } else {
                                    // Single click: Open source selection dropdown
                                    setSourceOpen(true);
                                }
                            }}
                            onContextMenuChange={() => {
                                console.log('Source card onContextMenuChange called, opening modal');
                                setShowChangeSourceModal(true);
                            }}
                            onClear={clearSource}
                            canClear={!!currentWorkingSourceId && hasAnySources}
                            contextMenuOptions={hasAnySources ? [
                                // Simplified right-click menu: only actions, no selection
                                {
                                    id: '__add_new__',
                                    label: 'Create New Source',
                                    subtitle: 'Add a new inventory source',
                                    onClick: handleAddNewSource,
                                },
                                ...(currentWorkingSourceId && effectiveSource ? [{
                                    id: '__edit_current__',
                                    label: `Edit "${effectiveSource.code}"`,
                                    subtitle: 'Modify current source details',
                                    onClick: handleEditCurrentSource,
                                }] : []),
                            ] : []}
                        />
                    }
                >
                    <Command>
                        <CommandInput placeholder="Search sources..." />
                        <CommandList>
                            <CommandEmpty>No sources found.</CommandEmpty>
                            <CommandGroup>
                                <CommandItem
                                    onSelect={() => {
                                        setCurrentWorkingSourceId(null);
                                        setSourceOpen(false);
                                    }}
                                >
                                    <Check className={cn(
                                        "mr-2 h-4 w-4",
                                        !currentWorkingSourceId ? "opacity-100" : "opacity-0"
                                    )} />
                                    {defaultSource ? `Default (${defaultSource.code})` : 'Default'}
                                </CommandItem>
                                {allSources?.map((source) => (
                                    <CommandItem
                                        key={source._id}
                                        onSelect={() => {
                                            setCurrentWorkingSourceId(source._id);
                                            setSourceOpen(false);
                                        }}
                                    >
                                        <Check className={cn(
                                            "mr-2 h-4 w-4",
                                            currentWorkingSourceId === source._id ? "opacity-100" : "opacity-0"
                                        )} />
                                        {source.code} • {source.name}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </SelectorPopover>

                {/* Blueprint Card */}
                <SelectorPopover
                    isOpen={blueprintOpen}
                    onOpenChange={setBlueprintOpen}
                    trigger={
                        <ContextCard
                            icon={<Layers className="h-4 w-4" />}
                            label="Applying Blueprint"
                            value={
                                !hasAnyBlueprints ? "Create Blueprint" :
                                    currentBlueprint?.code || "None"
                            }
                            subtitle={
                                !hasAnyBlueprints ? "Add your first blueprint" :
                                    currentBlueprint?.name
                            }
                            isEmpty={!hasAnyBlueprints || !currentBlueprint}
                            onTap={() => {
                                if (!hasAnyBlueprints) {
                                    handleAddNewBlueprint();
                                } else {
                                    setBlueprintOpen(true);
                                }
                            }}
                            onClear={clearBlueprint}
                            onAdd={handleAddNewBlueprint}
                            canClear={!!currentWorkingBlueprintId && hasAnyBlueprints}
                            contextMenuOptions={hasAnyBlueprints ? [
                                // Simplified right-click menu: only actions, no selection
                                {
                                    id: '__add_new__',
                                    label: 'Create New Blueprint',
                                    subtitle: 'Add new blueprint template',
                                    onClick: handleAddNewBlueprint,
                                },
                                ...(currentWorkingBlueprintId && currentBlueprint ? [{
                                    id: '__manage_current__',
                                    label: `Manage "${currentBlueprint.code}"`,
                                    subtitle: 'Go to blueprints page',
                                    onClick: handleEditCurrentBlueprint,
                                }] : []),
                            ] : []}
                        />
                    }
                >
                    <Command>
                        <CommandInput placeholder="Search blueprints..." />
                        <CommandList>
                            <CommandEmpty>No blueprints found.</CommandEmpty>
                            <CommandGroup>
                                <CommandItem
                                    onSelect={() => {
                                        setCurrentWorkingBlueprintId(null);
                                        setBlueprintOpen(false);
                                    }}
                                >
                                    <Check className={cn(
                                        "mr-2 h-4 w-4",
                                        !currentWorkingBlueprintId ? "opacity-100" : "opacity-0"
                                    )} />
                                    None
                                </CommandItem>
                                {blueprints.map((blueprint) => (
                                    <CommandItem
                                        key={blueprint._id}
                                        onSelect={() => {
                                            setCurrentWorkingBlueprintId(blueprint._id);
                                            setBlueprintOpen(false);
                                        }}
                                    >
                                        <Check className={cn(
                                            "mr-2 h-4 w-4",
                                            currentWorkingBlueprintId === blueprint._id ? "opacity-100" : "opacity-0"
                                        )} />
                                        {blueprint.code} • {blueprint.name}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </SelectorPopover>

                {/* Foundation Card - Hidden on mobile */}
                <SelectorPopover
                    isOpen={foundationOpen}
                    onOpenChange={setFoundationOpen}
                    trigger={
                        <ContextCard
                            className="hidden md:flex"
                            icon={<Layers className="h-4 w-4" />}
                            label="Foundation Preset"
                            value={
                                !hasAnyFoundations ? "Create Foundation" :
                                    currentFoundation?.name || "None"
                            }
                            subtitle={
                                !hasAnyFoundations ? "Add foundation preset" :
                                    currentFoundation?.description
                            }
                            isEmpty={!hasAnyFoundations || !currentFoundation}
                            onTap={() => {
                                if (!hasAnyFoundations) {
                                    handleAddNewFoundation();
                                } else {
                                    setFoundationOpen(true);
                                }
                            }}
                            onClear={clearFoundation}
                            onAdd={handleAddNewFoundation}
                            canClear={!!currentFoundationBlueprintId && hasAnyFoundations}
                            contextMenuOptions={hasAnyFoundations ? [
                                // Simplified right-click menu: only actions, no selection
                                {
                                    id: '__add_new__',
                                    label: 'Create New Foundation',
                                    subtitle: 'Add new foundation preset',
                                    onClick: handleAddNewFoundation,
                                },
                                ...(currentFoundationBlueprintId && currentFoundation ? [{
                                    id: '__manage_current__',
                                    label: `Manage "${currentFoundation.name}"`,
                                    subtitle: 'Go to foundations page',
                                    onClick: handleEditCurrentFoundation,
                                }] : []),
                            ] : []}
                        />
                    }
                >
                    <Command>
                        <CommandInput placeholder="Search foundations..." />
                        <CommandList>
                            <CommandEmpty>No foundations found.</CommandEmpty>
                            <CommandGroup>
                                <CommandItem
                                    onSelect={() => {
                                        setCurrentFoundationBlueprintId(null);
                                        setFoundationOpen(false);
                                    }}
                                >
                                    <Check className={cn(
                                        "mr-2 h-4 w-4",
                                        !currentFoundationBlueprintId ? "opacity-100" : "opacity-0"
                                    )} />
                                    None
                                </CommandItem>
                                {foundations.map((foundation) => (
                                    <CommandItem
                                        key={foundation._id}
                                        onSelect={() => {
                                            setCurrentFoundationBlueprintId(foundation._id);
                                            setFoundationOpen(false);
                                        }}
                                    >
                                        <Check className={cn(
                                            "mr-2 h-4 w-4",
                                            currentFoundationBlueprintId === foundation._id ? "opacity-100" : "opacity-0"
                                        )} />
                                        <div className="flex flex-col">
                                            <span>{foundation.name}</span>
                                            {foundation.description && (
                                                <span className="text-xs text-muted-foreground">
                                                    {foundation.description}
                                                </span>
                                            )}
                                        </div>
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </SelectorPopover>

                {/* SKU Card */}
                <SelectorPopover
                    isOpen={skuOpen}
                    onOpenChange={setSkuOpen}
                    trigger={
                        <ContextCard
                            icon={
                                <div className="w-6 h-6 rounded-sm border overflow-hidden flex-shrink-0 bg-muted flex items-center justify-center">
                                    {skuData && skuImageMap[skuData.fullSku] ? (
                                        <AppImage
                                            src={skuImageMap[skuData.fullSku]}
                                            alt={skuData.whatIsIt || skuData.fullSku}
                                            uiSize={UISize.Thumbnail96}
                                            className="object-contain w-full h-full"
                                        />
                                    ) : (
                                        <PackageIcon className="h-3 w-3 text-muted-foreground" />
                                    )}
                                </div>
                            }
                            label="Current SKU"
                            value={skuData?.fullSku || "None"}
                            subtitle={skuData?.whatIsIt || "Select SKU"}
                            isEmpty={!skuData}
                            onTap={() => setSkuOpen(true)}
                            onClear={clearSku}
                            canClear={!!currentWorkingSkuId}
                            className={cn(
                                "md:col-span-1",
                                isSwitchingSku && "opacity-50 pointer-events-none"
                            )}
                            contextMenuOptions={[
                                // Simplified right-click menu: only actions, no selection
                                {
                                    id: '__add_new__',
                                    label: 'Go to SKU Management',
                                    subtitle: 'Create and manage SKUs',
                                    onClick: handleCreateNewSku,
                                },
                                ...(currentWorkingSkuId && skuData ? [
                                    {
                                        id: '__copy_sku__',
                                        label: `Copy "${skuData.fullSku}"`,
                                        subtitle: 'Copy SKU to clipboard',
                                        onClick: handleCopySku,
                                    },
                                    {
                                        id: '__edit_current__',
                                        label: `Edit "${skuData.fullSku}"`,
                                        subtitle: 'Open SKU detail page',
                                        onClick: handleEditCurrentSku,
                                    }
                                ] : []),
                            ]}
                            submenuOptions={currentWorkingSourceId && skusByCurrentSource && skusByCurrentSource.length > 0 ? {
                                title: `From ${effectiveSource?.name || 'Current Source'}`,
                                subtitle: `${skusByCurrentSource.length} SKUs from current source`,
                                options: skusByCurrentSource.slice(0, 6).map(sku => ({
                                    id: sku._id,
                                    label: sku.fullSku,
                                    subtitle: sku.whatIsIt.substring(0, 40) + (sku.whatIsIt.length > 40 ? '...' : ''),
                                    isSelected: currentWorkingSkuId === sku._id,
                                    onClick: () => {
                                        if (!isSwitchingSku) {
                                            handleSkuSwitch(sku._id);
                                        }
                                    },
                                })),
                            } : undefined}
                        />
                    }
                >
                    <Command>
                        <CommandInput placeholder="Search SKUs..." />
                        <CommandList>
                            <CommandEmpty>No SKUs found.</CommandEmpty>
                            <CommandGroup>
                                <CommandItem
                                    onSelect={() => {
                                        setCurrentWorkingSkuId(null);
                                        setSkuOpen(false);
                                    }}
                                >
                                    <Check className={cn(
                                        "mr-2 h-4 w-4",
                                        !currentWorkingSkuId ? "opacity-100" : "opacity-0"
                                    )} />
                                    None
                                </CommandItem>
                                {recentSkus?.map((sku) => (
                                    <CommandItem
                                        key={sku._id}
                                        onSelect={() => {
                                            if (!isSwitchingSku) {
                                                handleSkuSwitch(sku._id);
                                                setSkuOpen(false);
                                            }
                                        }}
                                    >
                                        <Check className={cn(
                                            "mr-2 h-4 w-4",
                                            currentWorkingSkuId === sku._id ? "opacity-100" : "opacity-0"
                                        )} />
                                        <div className="flex items-center gap-2 flex-1 min-w-0">
                                            <div className="w-6 h-6 rounded-sm border overflow-hidden flex-shrink-0 bg-muted flex items-center justify-center">
                                                {skuImageMap[sku.fullSku] ? (
                                                    <AppImage
                                                        src={skuImageMap[sku.fullSku]}
                                                        alt={sku.whatIsIt}
                                                        uiSize={UISize.Thumbnail96}
                                                        className="object-contain w-full h-full"
                                                    />
                                                ) : (
                                                    <PackageIcon className="h-3 w-3 text-muted-foreground" />
                                                )}
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <div className="font-medium">{sku.fullSku}</div>
                                                <div className="text-sm text-muted-foreground truncate">
                                                    {sku.whatIsIt.substring(0, 40)}{sku.whatIsIt.length > 40 ? '...' : ''}
                                                </div>
                                            </div>
                                        </div>
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </SelectorPopover>
            </div>

            {/* Mobile Priority Actions - Icon Only */}
            <div className="flex gap-1 items-center md:hidden">
                <Button 
                    size="sm" 
                    variant="default" 
                    onClick={() => router.push('/listings/quick')}
                    className="h-8 w-8 p-0"
                    title="QuickList"
                >
                    <Zap className="h-4 w-4" />
                </Button>
                <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => router.push('/workbench')}
                    className="h-8 w-8 p-0"
                    title="Photo Workbench"
                >
                    <Camera className="h-4 w-4" />
                </Button>
                <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={handleQuickAddSku}
                    className="h-8 w-8 p-0"
                    title="Quick Add SKU"
                >
                    <Plus className="h-4 w-4" />
                </Button>
            </div>

            {/* Desktop Quick Actions */}
            {!compact && (
                <div className="hidden md:flex gap-1 lg:gap-2 items-center">
                    <Button size="sm" variant="outline" onClick={handleQuickAddSku} className="h-7 lg:h-8 px-2 lg:px-3">
                        <Plus className="h-3 w-3 lg:mr-1" />
                        <span className="hidden lg:inline ml-1">
                            Quick SKU{sourceData && ` in ${sourceData.code}`}
                        </span>
                    </Button>

                    {/* Page-specific actions */}
                    {pageActions.length > 0 && (
                        <>
                            <div className="h-4 w-px bg-border mx-1" />
                            {pageActions.map((action) => (
                                <Button
                                    key={action.id}
                                    size="sm"
                                    variant={action.variant || 'outline'}
                                    onClick={action.onClick}
                                    disabled={action.disabled || action.isLoading}
                                    className={cn("h-7 lg:h-8 px-2 lg:px-3", action.className)}
                                >
                                    {action.isLoading ? (
                                        <RefreshCw className="h-3 w-3 animate-spin" />
                                    ) : action.icon ? (
                                        <span className="flex items-center">{action.icon}</span>
                                    ) : null}
                                    <span className="hidden lg:inline ml-1">{action.label}</span>
                                </Button>
                            ))}
                        </>
                    )}
                </div>
            )}

            {/* Modals */}
            <CreateEditSourceModal
                isOpen={showCreateSourceModal}
                onClose={() => handleCloseCreateSourceModal()}
                onSourceCreated={(sourceId) => handleCloseCreateSourceModal(sourceId)}
                initialData={editingSource}
            />

            <Dialog open={showCreateBlueprintModal} onOpenChange={setShowCreateBlueprintModal}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Create Blueprint</DialogTitle>
                        <DialogDescription>
                            Blueprint creation functionality coming soon.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowCreateBlueprintModal(false)}>
                            Close
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>


            {/* Change Source Modal */}
            <Dialog open={showChangeSourceModal} onOpenChange={setShowChangeSourceModal}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Change Source</DialogTitle>
                        <DialogDescription>
                            Select a different source for your current context.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <div className="space-y-2">
                            <div
                                className={cn(
                                    "p-3 rounded-lg border cursor-pointer hover:bg-muted/50 transition-colors",
                                    !currentWorkingSourceId && "bg-muted border-primary"
                                )}
                                onClick={() => {
                                    setCurrentWorkingSourceId(null);
                                    setShowChangeSourceModal(false);
                                }}
                            >
                                <div className="font-medium">{defaultSource ? `Default (${defaultSource.code})` : 'Default'}</div>
                                <div className="text-sm text-muted-foreground">Default source</div>
                            </div>
                            {allSources?.map((source) => (
                                <div
                                    key={source._id}
                                    className={cn(
                                        "p-3 rounded-lg border cursor-pointer hover:bg-muted/50 transition-colors",
                                        currentWorkingSourceId === source._id && "bg-muted border-primary"
                                    )}
                                    onClick={() => {
                                        setCurrentWorkingSourceId(source._id);
                                        setShowChangeSourceModal(false);
                                    }}
                                >
                                    <div className="font-medium">{source.code} • {source.name}</div>
                                    {source.location && (
                                        <div className="text-sm text-muted-foreground">{source.location}</div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowChangeSourceModal(false)}>
                            Cancel
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
} 