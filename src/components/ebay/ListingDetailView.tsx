'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { useAuth } from '@clerk/nextjs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AppImage } from '@/components/ui/AppImage';
import { UISize } from '@/lib/image-service';
import { Badge } from '@/components/ui/badge';
import { Award, Star, Loader2 } from 'lucide-react';
import type { ItemType, NameValueListType, ConditionDescriptorType } from '@/generated/trading/trading';
import { toast } from 'sonner';
import { logger } from '@/lib/logger';
import { getConditionDetails } from '@/lib/ebay/types/condition-enum';
import { DateTime } from 'luxon';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { api } from '@convex/_generated/api';
import {
  isTradingCardCategory,
  getConditionDescriptors,
  formatConditionDescriptorNameWithCache,
  formatConditionDescriptorValuesWithCache,
  generateConditionDescriptionWithCache,
  type CachedMetadata
} from '@/lib/ebay/trading-cards/constants';

// Get a logger instance
const log = logger.create('components:ListingDetailView');

/**
 * IMPORTANT: COMPLEX DATA FLOW ARCHITECTURE NOTES
 * 
 * This component handles multiple data sources and transformation chains that can be confusing:
 * 
 * 1. PUBLISHED LISTINGS (Trading API):
 *    - Direct eBay ItemID → Trading API GetItem call → ItemType
 *    - Clean, straightforward data flow
 * 
 * 2. SEARCH RESULTS (Browse API → Trading API):
 *    - Browse API search → creates fake EbayListingUI object → passed as initialListingData
 *    - Component then uses ItemID to fetch real Trading API data
 *    - ISSUE: Browse API and Trading API have different data structures, leading to mismatched fields
 * 
 * 3. CONVEX STORED LISTINGS:
 *    - Stored as EbayListingData with ebayData field containing ItemType
 *    - Needs extraction: listing.ebayListingRecord.ebayData
 * 
 * 4. DRAFT LISTINGS (TODO):
 *    - Completely different data structure from Convex drafts table
 *    - Needs separate handling and preview logic
 * 
 * CURRENT ISSUES:
 * - Multiple type casts and assumptions about data structure
 * - initialListingData can be partial, undefined, or completely different shape
 * - Search results create "fake" listing objects that may have inconsistent data
 * - No clear contract for what data is available when
 * 
 * FUTURE IMPROVEMENTS NEEDED:
 * - Cleaner data source abstraction
 * - Type-safe handling of different data sources
 * - Draft preview support with proper data mapping
 * - Better error handling for data mismatches
 * 
 * DRAFT PREVIEW EXTENSION PLAN:
 * 
 * To support draft preview, we need to:
 * 1. Add draftData prop and mode prop to distinguish preview types
 * 2. Create draft-to-ItemType transformation utility 
 * 3. Handle draft-specific fields (validation errors, incomplete data)
 * 4. Add draft-specific UI elements (validation warnings, "Preview" badges)
 * 5. Prevent Trading API calls for pure draft previews
 * 6. Support draft timeline info (created, last modified, etc.)
 * 
 * DRAFT DATA STRUCTURE DIFFERENCES:
 * - Draft stores data in ebayDraft field (different from ItemType)
 * - May have incomplete required fields
 * - Includes validation state and error information
 * - Uses UUID instead of ItemID for identification
 * - May reference SKU data that needs to be merged
 */

interface ListingDetailViewProps {
  identifier: string | null | undefined;

  /**
   * Optional initial listing data - can come from various sources:
   * - Search results (Browse API transformed to EbayListingUI)
   * - Convex stored listings (extracted from ebayData field)
   * - Direct ItemType data
   * 
   * WARNING: This data may be incomplete, inconsistent, or from different API endpoints
   * The component will attempt to use this as fallback while fetching authoritative data
   */
  initialListingData?: Partial<ItemType>;

  onItemIdLoaded?: (itemData: Partial<ItemType> | null) => void;

  /**
   * Indicates if this listing is from search results (should not auto-store)
   * This affects caching behavior to prevent search result contamination
   */
  isFromSearchResults?: boolean;

  // TODO: Add support for draft data
  // draftData?: Doc<'drafts'>;
  // mode?: 'published' | 'draft' | 'preview';
}

// Define the type expected from the successful query function
type FetchedListingData = ItemType & { _isDraftState?: boolean };

// Define the return type for the date formatting helper
type FormattedDateInfo =
  | {
    originalIso: string;
    luxonDateTime: DateTime;
    absolute: string;
    relative: string;
    relativeCountdown: string;
  }
  | 'N/A'
  | 'Invalid Date';

// Helper function for date formatting (with explicit return type)
const formatPreviewDateLuxon = (dateString?: string | number): FormattedDateInfo => {
  if (!dateString) return 'N/A';
  try {
    const dt = DateTime.fromISO(dateString as string);
    if (!dt.isValid) return 'Invalid Date';

    const absolute = dt.toFormat('ccc, LLL d, yyyy, h:mm a');
    const relative = dt.toRelative() ?? 'N/A';
    const now = DateTime.now();
    const diff = dt.diff(now, ['days', 'hours', 'minutes']).normalize();
    let relativeCountdown = 'Ended';
    if (diff.valueOf() > 0) {
      if (diff.days >= 1) relativeCountdown = `in ${Math.ceil(diff.days)} days`;
      else if (diff.hours >= 1) relativeCountdown = `in ${Math.ceil(diff.hours)} hours`;
      else if (diff.minutes >= 1) relativeCountdown = `in ${Math.ceil(diff.minutes)} min`;
      else relativeCountdown = 'Ending soon';
    }
    return {
      originalIso: dateString as string,
      luxonDateTime: dt,
      absolute,
      relative,
      relativeCountdown,
    };
  } catch (e) {
    log.error('Error formatting date with Luxon', { dateString, error: e });
    return 'Invalid Date';
  }
};


// Define type for item specifics used in the map
interface MappedItemSpecific {
  Name: string;
  Value: string;
}

// A helper component for displaying sections that might not be loaded yet
interface ProgressiveContentProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

const ProgressiveContent: React.FC<ProgressiveContentProps> = ({
  isLoading,
  children,
  fallback,
  className,
}) => {
  return (
    <div className={className}>
      {isLoading ? (
        fallback || (
          <div className="animate-pulse">
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        )
      ) : (
        children
      )}
    </div>
  );
};

// Trading Card Details Component
interface TradingCardDetailsProps {
  itemData: Partial<ItemType> | undefined;
  categoryPath: string | null;
  loadingCategoryPath: boolean;
  cachedMetadata: CachedMetadata;
}

const TradingCardDetails: React.FC<TradingCardDetailsProps> = ({
  itemData,
  categoryPath,
  loadingCategoryPath,
  cachedMetadata
}) => {
  if (!itemData) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        Trading card condition data is not available.
      </div>
    );
  }

  // Get condition descriptors using centralized utility
  const conditionDescriptors = getConditionDescriptors(itemData);
  const condition = itemData?.ConditionDisplayName || (itemData?.ConditionID ? String(itemData.ConditionID) : undefined);
  const categoryId = itemData?.PrimaryCategory?.CategoryID;

  console.log('[TradingCardDetails] Extracted data:', {
    categoryId,
    condition,
    hasConditionDescriptors: !!conditionDescriptors,
    descriptorCount: conditionDescriptors?.length || 0
  });

  return (
    <div className="space-y-6">
      {/* Trading Card Header */}
      <div className="flex items-center gap-2 pb-4 border-b">
        <Award className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Trading Card Details</h3>
        <div className="ml-auto flex items-center gap-2">
          {categoryId && (
            <code className="text-xs text-muted-foreground font-mono">ID: {categoryId}</code>
          )}
          <Badge variant="secondary">
            {loadingCategoryPath ? (
              <div className="flex items-center gap-1">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>Loading...</span>
              </div>
            ) : categoryPath ? (
              <span className="text-xs max-w-[200px] truncate" title={categoryPath}>
                {categoryPath.split(' > ').pop() || categoryPath}
              </span>
            ) : (
              'Trading Card'
            )}
          </Badge>
        </div>
      </div>

      {/* Basic Condition Info */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
            Basic Condition
          </h4>
          <div className="flex items-center gap-2">
            <Star className="h-4 w-4 text-amber-500" />
            <span className="font-medium">{condition || 'Not specified'}</span>
          </div>
        </div>

        <div className="space-y-2">
          <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
            Category
          </h4>
          <div className="flex flex-col gap-1">
            {loadingCategoryPath ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span className="text-sm text-muted-foreground">Loading category...</span>
              </div>
            ) : categoryPath ? (
              <div className="text-sm">
                <div className="font-medium">{categoryPath}</div>
                <div className="text-xs text-muted-foreground mt-1">Full category path</div>
              </div>
            ) : (
              <span className="text-sm text-muted-foreground">Unknown Category</span>
            )}
            {categoryId && (
              <code className="text-xs text-muted-foreground">ID: {categoryId}</code>
            )}
          </div>
        </div>
      </div>

      {/* Condition Descriptors */}
      {conditionDescriptors && conditionDescriptors.length > 0 ? (
        <div className="space-y-4">
          {/* Smart Summary Section */}
          <div className="bg-gradient-to-r from-primary/5 to-primary/10 border border-primary/20 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Award className="h-4 w-4 text-primary" />
              <h4 className="font-semibold text-primary">Condition Summary</h4>
            </div>
            <p className="text-sm font-medium text-foreground">
              {generateConditionDescriptionWithCache(conditionDescriptors, condition, cachedMetadata)}
            </p>
          </div>

          {/* Detailed Breakdown */}
          <div>
            <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide mb-3">
              Detailed Breakdown
            </h4>
            <div className="grid gap-3">
              {conditionDescriptors.map((descriptor: ConditionDescriptorType, index: number) => {
                const displayName = formatConditionDescriptorNameWithCache(descriptor, cachedMetadata);
                const displayValues = formatConditionDescriptorValuesWithCache(descriptor, cachedMetadata);

                // Special handling for Certification Number to make it more prominent
                const isCertificationNumber = descriptor.Name === '27503' || displayName.toLowerCase().includes('certification');

                return (
                  <div key={index} className="border rounded-lg p-3 bg-card">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h5 className="font-medium text-sm">{displayName}</h5>
                        {descriptor.Name && descriptor.Name !== displayName && (
                          <code className="text-xs text-muted-foreground">ID: {descriptor.Name}</code>
                        )}
                      </div>

                      <div className="flex flex-col items-end gap-1">
                        {displayValues.map((value, valueIndex) => (
                          <Badge
                            key={valueIndex}
                            variant={isCertificationNumber ? "default" : "secondary"}
                            className={`text-xs ${isCertificationNumber ? 'font-mono bg-primary text-primary-foreground' : ''}`}
                          >
                            {value}
                          </Badge>
                        ))}
                        {descriptor.Value && descriptor.Value.length > 0 && displayValues.length > 0 &&
                          descriptor.Value.some((v, i) => v !== displayValues[i]?.replace(/^.+?: /, '')) && (
                            <code className="text-xs text-muted-foreground mt-1">
                              Raw: {descriptor.Value.join(', ')}
                            </code>
                          )}
                      </div>
                    </div>

                    {/* Show AdditionalInfo with better styling for cert numbers */}
                    {descriptor.AdditionalInfo && !isCertificationNumber && (
                      <p className="text-xs text-muted-foreground mt-2 italic">
                        {descriptor.AdditionalInfo}
                      </p>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          <Award className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>No condition descriptors found for this trading card.</p>
          <p className="text-xs mt-1">This may be a basic condition without grading details.</p>
        </div>
      )}

      {/* Debug Info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-6">
          <summary className="cursor-pointer text-xs text-muted-foreground hover:text-foreground">
            Debug: Condition Data & Cache Status
          </summary>
          <div className="mt-2 space-y-2">
            <div className="text-xs">
              <strong>Cache Status:</strong> {cachedMetadata ? 'Using cached eBay metadata ✅' : 'Using static fallback ⚠️'}
            </div>
            {cachedMetadata && (
              <div className="text-xs">
                <strong>Cached Grading Companies:</strong> {cachedMetadata.conditionMetadata?.conditionDescriptors?.find(d => d.name === 'Professional Grader')?.values?.length || 0}
              </div>
            )}
            <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(conditionDescriptors, null, 2)}
            </pre>
          </div>
        </details>
      )}
    </div>
  );
};

export function ListingDetailView({
  identifier,
  initialListingData,
  onItemIdLoaded,
}: ListingDetailViewProps) {
  const [selectedPreviewUrl, setSelectedPreviewUrl] = useState<string | undefined>(undefined);
  useAuth();

  // Try to get draft data first if identifier looks like an eBay item ID
  const draftQuery = useQuery(
    api.drafts.getDraftByItemId,
    identifier && typeof identifier === 'string' && identifier.length > 0
      ? { ebayItemId: identifier }
      : 'skip'
  );

  // Try to get stored eBay listing data
  const ebayListingQuery = useQuery(
    api.ebayListings.getEbayListingByItemId,
    identifier && typeof identifier === 'string' && identifier.length > 0
      ? { itemId: identifier }
      : 'skip'
  );

  // Determine the data source and loading state
  const { fetchedDetails, isLoading, error } = useMemo(() => {
    // Check if we're still loading either query
    const isLoadingQueries = draftQuery === undefined || ebayListingQuery === undefined;
    
    // Prefer draft data if available
    if (draftQuery && draftQuery.ebayDraft) {
      log.info('Using draft data from Convex', { identifier, uuid: draftQuery.uuid });
      const typedEbayDraft = draftQuery.ebayDraft as Partial<ItemType>;
      return {
        fetchedDetails: {
          ...typedEbayDraft,
          ItemID: identifier,
          _isDraftState: true
        } as FetchedListingData,
        isLoading: false,
        error: null
      };
    }

    // Fall back to stored eBay listing data
    if (ebayListingQuery && ebayListingQuery.ebayData) {
      log.info('Using stored eBay listing data from Convex', { identifier });
      const typedEbayData = ebayListingQuery.ebayData as Partial<ItemType>;
      return {
        fetchedDetails: typedEbayData as FetchedListingData,
        isLoading: false,
        error: null
      };
    }

    // If no data found and not loading, show error
    if (!isLoadingQueries && !draftQuery && !ebayListingQuery) {
      return {
        fetchedDetails: undefined,
        isLoading: false,
        error: new Error('Listing not found in cache. Please try refreshing the page.')
      };
    }

    // Still loading
    return {
      fetchedDetails: undefined,
      isLoading: isLoadingQueries,
      error: null
    };
  }, [draftQuery, ebayListingQuery, identifier]);

  const status = error ? 'error' : isLoading ? 'pending' : 'success';
  const isFetching = isLoading;

  const displayDataSource = useMemo(() => {
    /**
     * COMPLEX DATA SOURCE PRIORITY CHAIN:
     * 
     * This logic determines which data to display based on availability and reliability:
     * 
     * 1. FIRST PRIORITY: fetchedDetails (Trading API)
     *    - Most authoritative and complete data
     *    - Fetched directly from eBay Trading API using GetItem
     *    - Only available if fetch was successful
     * 
     * 2. FALLBACK: initialListingData 
     *    - Used while waiting for Trading API or if fetch fails
     *    - Can come from multiple sources with different reliability:
     *      a) Convex stored data (ebayData field) - RELIABLE
     *      b) Search results (Browse API) - PARTIAL/UNRELIABLE
     *      c) Direct ItemType - RELIABLE but may be stale
     * 
     * MATCHING LOGIC ISSUES:
     * - UUID matching: Used for drafts (initialListingData.UUID === identifier)
     * - ItemID matching: Used for published listings (initialListingData.ItemID === identifier)
     * - This creates complex conditional logic that's error-prone
     * 
     * SEARCH RESULTS PROBLEMS:
     * - Browse API data gets transformed into fake EbayListingUI
     * - Then gets partially converted to ItemType shape
     * - Many fields may be undefined or have wrong types
     * - Price formats, date formats, field names may not match Trading API
     */

    // Prefer fetchedDetails if available and successful
    if (status === 'success' && fetchedDetails) {
      log.debug('Using fetchedDetails from Trading API', { itemId: fetchedDetails.ItemID });
      return fetchedDetails;
    }

    // Fallback to initialListingData - handle UUID matching (drafts)
    if (
      initialListingData &&
      initialListingData.ItemID === undefined &&
      identifier === initialListingData.UUID
    ) {
      log.debug('Using initialListingData with UUID matching', { uuid: identifier });
      return initialListingData as FetchedListingData;
    }

    // Fallback to initialListingData - handle ItemID matching (published listings)
    if (
      initialListingData &&
      initialListingData.ItemID &&
      identifier === initialListingData.ItemID
    ) {
      log.debug('Using initialListingData with ItemID matching', { itemId: identifier });
      return initialListingData as FetchedListingData;
    }

    log.debug('No displayDataSource available', {
      status,
      hasFetchedDetails: !!fetchedDetails,
      hasInitialData: !!initialListingData,
      identifier
    });
    return undefined;
  }, [status, fetchedDetails, initialListingData, identifier]);

  // Category and Trading Card Logic (moved from ListingPreviewSheet)
  const categoryId = displayDataSource?.PrimaryCategory?.CategoryID || null;

  // Check if this is a trading card listing
  const isTradingCard = useMemo(() => {
    if (!displayDataSource) return false;
    const result = isTradingCardCategory(categoryId);
    if (result) {
      console.log('[ListingDetailView] Detected trading card listing:', categoryId);
    }
    return result;
  }, [displayDataSource, categoryId]);

  // Convex queries for category and metadata
  const cachedCategoryPathQuery = useQuery(api.metadata.getCategoryPath,
    categoryId ? {
      marketplaceId: 'EBAY_US',
      categoryId: categoryId
    } : 'skip'
  );

  const cachedMetadataQuery = useQuery(api.metadata.getMetadataForCategory,
    categoryId && isTradingCard ? {
      marketplaceId: 'EBAY_US',
      categoryId: categoryId
    } : 'skip'
  );

  const { categoryPath, loadingCategoryPath } = useMemo(() => {
    if (!categoryId) {
      return { categoryPath: null, loadingCategoryPath: false };
    }

    if (cachedCategoryPathQuery === undefined) {
      return { categoryPath: null, loadingCategoryPath: true };
    }

    if (cachedCategoryPathQuery) {
      return { categoryPath: cachedCategoryPathQuery, loadingCategoryPath: false };
    }

    // Not in cache - just show the category ID
    return { categoryPath: `Category #${categoryId}`, loadingCategoryPath: false };
  }, [categoryId, cachedCategoryPathQuery]);

  const ebayItemIdFromData = displayDataSource?.ItemID;

  useEffect(() => {
    if (onItemIdLoaded) {
      if (status === 'success' && displayDataSource) {
        onItemIdLoaded(displayDataSource);
      } else if (status === 'error') {
        onItemIdLoaded(null);
      }
    }
  }, [status, displayDataSource, onItemIdLoaded]);

  // Determine primary title from available data
  const title = useMemo(() => {
    return displayDataSource?.Title || initialListingData?.Title || 'Listing Details';
  }, [displayDataSource?.Title, initialListingData?.Title]);

  const allImageUrls = useMemo(() => {
    return displayDataSource?.PictureDetails?.PictureURL ?? [];
  }, [displayDataSource]);

  const startTime = displayDataSource?.ListingDetails?.StartTime;
  const endTime = displayDataSource?.ListingDetails?.EndTime;

  const itemSpecifics = useMemo(() => {
    return (
      displayDataSource?.ItemSpecifics?.NameValueList?.map(
        (nvl: NameValueListType): MappedItemSpecific => ({
          Name: nvl.Name || '',
          Value: Array.isArray(nvl.Value) ? nvl.Value.join(', ') : nvl.Value || '',
        }),
      ) ?? []
    );
  }, [displayDataSource]);

  const startInfo: FormattedDateInfo = useMemo(
    () => formatPreviewDateLuxon(startTime),
    [startTime],
  );
  const endInfo: FormattedDateInfo = useMemo(() => formatPreviewDateLuxon(endTime), [endTime]);

  // Logging for debugging timeline
  useEffect(() => {
    log.debug('Timeline Debug Info:', {
      identifier,
      startTimeRaw: startTime,
      endTimeRaw: endTime,
      startInfoProcessed: startInfo,
      endInfoProcessed: endInfo,
      displayDataSourceExists: !!displayDataSource,
      listingDetailsExists: !!displayDataSource?.ListingDetails,
    });
  }, [identifier, startTime, endTime, startInfo, endInfo, displayDataSource]);

  const timelineData = useMemo(() => {
    if (typeof startInfo !== 'object') return null;
    const startDate = startInfo.luxonDateTime;
    const now = DateTime.now(); // Define 'now' once, usable by both branches

    let timeActiveString = '';
    const hasStarted = now >= startDate;
    if (hasStarted) {
      const activeDuration = now.diff(startDate, ['years', 'days', 'hours']).normalize();
      const activeYears = Math.floor(activeDuration.years);
      const activeDays = Math.floor(activeDuration.days);
      const activeHours = Math.floor(activeDuration.hours);
      if (activeYears > 0) {
        timeActiveString = `${activeYears} year${activeYears > 1 ? 's' : ''}${activeDays > 0 ? `, ${activeDays} day${activeDays > 1 ? 's' : ''}` : ''}`;
      } else if (activeDays > 0) {
        timeActiveString = `${activeDays} day${activeDays > 1 ? 's' : ''}${activeHours > 0 ? `, ${activeHours} hr${activeHours > 1 ? 's' : ''}` : ''}`;
      } else if (activeHours > 0) {
        timeActiveString = `${activeHours} hr${activeHours > 1 ? 's' : ''}`;
      } else {
        timeActiveString = 'Less than an hour';
      }
    }

    if (typeof endInfo === 'object') {
      // Fixed-duration logic
      const endDate = endInfo.luxonDateTime;
      const totalDurationMs = endDate.diff(startDate).as('milliseconds');
      const elapsedMs = now.diff(startDate).as('milliseconds');
      let progressPercent = 0;
      if (totalDurationMs > 0) {
        progressPercent = Math.max(0, Math.min(100, (elapsedMs / totalDurationMs) * 100));
      }
      const isEnded = now >= endDate;
      if (isEnded) progressPercent = 100;

      // Calculate total runtime for ended listings
      let totalRunTimeString = '';
      if (isEnded) {
        const runTimeDuration = endDate.diff(startDate, ['years', 'days', 'hours']).normalize();
        const runTimeYears = Math.floor(runTimeDuration.years);
        const runTimeDays = Math.floor(runTimeDuration.days);
        const runTimeHours = Math.floor(runTimeDuration.hours);

        if (runTimeYears > 0) {
          totalRunTimeString = `${runTimeYears} year${runTimeYears > 1 ? 's' : ''}${runTimeDays > 0 ? `, ${runTimeDays} day${runTimeDays > 1 ? 's' : ''}` : ''}`;
        } else if (runTimeDays > 0) {
          totalRunTimeString = `${runTimeDays} day${runTimeDays > 1 ? 's' : ''}${runTimeHours > 0 ? `, ${runTimeHours} hr${runTimeHours > 1 ? 's' : ''}` : ''}`;
        } else if (runTimeHours > 0) {
          totalRunTimeString = `${runTimeHours} hr${runTimeHours > 1 ? 's' : ''}`;
        } else {
          totalRunTimeString = 'Less than an hour';
        }
      }

      let statusText = isEnded
        ? 'Ended'
        : hasStarted
          ? progressPercent < 100
            ? 'Active'
            : 'Ending very soon'
          : 'Scheduled';
      let verbalTimeRemaining = '';
      if (hasStarted && !isEnded) {
        const remaining = endDate.diff(now, ['years', 'days', 'hours', 'minutes']).normalize();
        const years = Math.floor(remaining.years);
        const days = Math.floor(remaining.days);
        const hours = Math.floor(remaining.hours);
        const minutes = Math.floor(remaining.minutes);
        if (years > 0) {
          verbalTimeRemaining = `${years} year${years > 1 ? 's' : ''}${days > 0 ? `, ${days} day${days > 1 ? 's' : ''}` : ''} remaining`;
        } else if (days > 0) {
          verbalTimeRemaining = `${days} day${days > 1 ? 's' : ''}${hours > 0 ? `, ${hours} hr${hours > 1 ? 's' : ''}` : ''} remaining`;
        } else if (hours > 0) {
          verbalTimeRemaining = `${hours} hr${hours > 1 ? 's' : ''}${minutes > 0 ? `, ${minutes} min` : ''} remaining`;
        } else if (minutes > 0) {
          verbalTimeRemaining = 'Less than a minute remaining';
          statusText = 'Ending very soon';
        }
      }
      if (isEnded) verbalTimeRemaining = totalRunTimeString ? `Ran for ${totalRunTimeString}` : 'Auction has ended';

      return {
        type: 'fixed',
        startDateString: startDate.toFormat('LLL d, yyyy h:mm a'),
        endDateString: endDate.toFormat('LLL d, yyyy h:mm a'),
        progressPercent,
        isEnded,
        hasStarted,
        statusText,
        verbalTimeRemaining,
        timeActiveString: hasStarted ? timeActiveString : 'Not yet started', // Add timeActiveString
        totalRunTimeString,
      };
    }

    // GTC logic
    const statusTextGTC = hasStarted
      ? "Active (Good 'Til Cancelled)"
      : "Scheduled (Good 'Til Cancelled)";
    return {
      type: 'gtc',
      startDateString: startDate.toFormat('LLL d, yyyy h:mm a'),
      timeActiveString: hasStarted ? timeActiveString : 'Not yet started', // Use the common timeActiveString
      hasStarted,
      statusText: statusTextGTC,
    };
  }, [startInfo, endInfo]);

  useEffect(() => {
    if (allImageUrls.length > 0 && selectedPreviewUrl === undefined) {
      setSelectedPreviewUrl(allImageUrls[0]);
    }
    // Reset selected image if identifier changes (e.g. new page load) or no images
    if (!identifier || allImageUrls.length === 0) {
      setSelectedPreviewUrl(undefined);
    }
  }, [allImageUrls, selectedPreviewUrl, identifier]);

  useEffect(() => {
    if (status === 'error' && error) {
      toast.error('Could not load details.', { description: error.message });
    }
  }, [status, error]);

  if (isLoading) {
    // Show loader only during initial data fetch - eBay-style layout
    return (
      <div className="p-2 sm:p-4">
        {/* eBay-style Layout Skeleton */}
        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
          {/* Image Section - Full Width on Mobile, Left Side on Desktop */}
          <div className="w-full max-w-lg mx-auto lg:mx-0 lg:flex-1 lg:max-w-[400px]">
            {/* Main Image Area */}
            <div className="relative border rounded bg-muted aspect-[4/3] mb-3">
              <Skeleton className="absolute inset-0 rounded" />
            </div>
            {/* Horizontal Thumbnails Row - eBay Style */}
            <div className="w-full max-w-lg mx-auto lg:mx-0 lg:max-w-[400px]">
              <div className="flex gap-2 pb-1 overflow-x-auto">
                {[1, 2, 3, 4, 5].map((i) => (
                  <Skeleton key={i} className="w-16 h-16 rounded flex-shrink-0" />
                ))}
              </div>
            </div>
          </div>

          {/* Details Section - Full Width on Mobile, Right Side on Desktop */}
          <div className="w-full lg:flex-1 lg:min-w-0">
            {/* Title and Item ID */}
            <div className="mb-4">
              <Skeleton className="h-6 w-full mb-2" />
              <Skeleton className="h-4 w-32" />
            </div>

            {/* Price */}
            <div className="mb-4">
              <Skeleton className="h-8 w-40" />
            </div>

            {/* Condition */}
            <div className="mb-4">
              <div className="flex items-center gap-2 mb-1">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-24" />
              </div>
              <Skeleton className="h-3 w-full" />
            </div>

            {/* Timeline Card */}
            <div className="p-3 border rounded-lg bg-card mb-4">
              <div className="flex justify-between items-center mb-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
              <Skeleton className="h-2 w-full mb-1" />
              <Skeleton className="h-3 w-32 mx-auto" />
            </div>

            {/* Quick Details List */}
            <div className="space-y-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex justify-between">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-24" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tabs Section Skeleton */}
        <div className="mt-6">
          <Skeleton className="h-8 w-full mb-2" />
          <div className="p-3 border rounded-lg">
            <div className="space-y-2">
              {[1, 2, 3, 4].map((i) => (
                <Skeleton key={i} className="h-4 w-full" />
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'error' && !displayDataSource) {
    // Show error if fetch failed and no fallback
    return (
      <div className="text-destructive text-center p-4 border border-destructive bg-destructive/10 rounded-md">
        <p className="font-semibold">Failed to load listing details.</p>
        {error && <p className="text-sm text-destructive/80">{error.message}</p>}
        <p className="text-sm mt-2">Item ID: {identifier || 'N/A'}</p>
      </div>
    );
  }

  if (!displayDataSource && !isLoading) {
    // If not loading, no error, but still no data
    return (
      <div className="text-muted-foreground text-center p-4 border border-dashed rounded-md">
        <p>No listing details available for Item ID: {identifier || 'N/A'}.</p>
        <p className="text-sm">This item might have been deleted or the ID is incorrect.</p>
      </div>
    );
  }

  // Extract other display variables from displayDataSource
  const price = displayDataSource?.StartPrice?.Value;
  const currency = displayDataSource?.StartPrice?.CurrencyID;
  // state needs to be derived based on ItemType fields, not directly from a 'state' field
  // This is a placeholder; actual state derivation will be more complex
  const sellingStatus = displayDataSource?.SellingStatus?.ListingStatus || 'Unknown';
  // hasActiveRevision logic would need to be determined, perhaps from a draft linked to this ItemID if applicable
  // const hasActiveRevision = false; // Placeholder
  // const directEbayUrl = ebayItemId ? `https://www.ebay.com/itm/${ebayItemId}` : undefined; // Removed unused variable

  const totalQuantity = displayDataSource?.Quantity;
  const listingType = displayDataSource?.ListingType;
  const sku = displayDataSource?.SKU;
  const quantitySold = displayDataSource?.SellingStatus?.QuantitySold;
  const conditionId = displayDataSource?.ConditionID
    ? Number(displayDataSource.ConditionID)
    : undefined;
  const conditionDetails = conditionId ? getConditionDetails(conditionId) : undefined;
  const conditionLabel = conditionDetails?.label || 'Unknown';
  const conditionDescription = displayDataSource?.ConditionDescription;
  const htmlDescription = displayDataSource?.Description as string | undefined;
  const watchCount = displayDataSource?.WatchCount;
  const location = displayDataSource?.Location;
  const postalCode = displayDataSource?.PostalCode;
  const returnPolicy = displayDataSource?.ReturnPolicy?.ReturnsAcceptedOption;

  return (
    <ScrollArea className="flex-1 h-full">
      <div className="p-2 sm:p-4">
        {isFetching && !isLoading && (
          <div className="fixed top-0 left-0 right-0 h-1 bg-primary/20 animate-pulse z-50"></div>
        )}

        {/* eBay-style Layout: Stacked on Mobile, Side-by-side on Desktop */}
        <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
          {/* Image Section - Full Width on Mobile, Left Side on Desktop */}
          <div className="w-full max-w-lg mx-auto lg:mx-0 lg:flex-1 lg:max-w-[400px]">
            {/* Main Image */}
            <div className="relative border rounded bg-muted overflow-hidden aspect-[4/3] flex items-center justify-center mb-3">
              {allImageUrls.length > 0 && selectedPreviewUrl ? (
                <AppImage
                  src={selectedPreviewUrl}
                  alt={title || 'Listing image preview'}
                  uiSize={UISize.Fixed_Ebay_4}
                  className="object-contain w-full h-full"
                  priority={selectedPreviewUrl === allImageUrls[0]}
                />
              ) : isLoading ? (
                <Skeleton className="absolute inset-0" />
              ) : (
                <div className="text-muted-foreground text-sm">No images available for this listing.</div>
              )}
            </div>

            {/* Thumbnails - Bottom Horizontal */}
            {allImageUrls.length > 0 && (
              <div className="w-full max-w-lg mx-auto lg:mx-0 lg:max-w-[400px] overflow-x-auto scrollbar-hide hover:scrollbar-show transition-all duration-200">
                <div className="flex gap-2 pb-1">
                  {allImageUrls.map((url: string, index: number) => (
                    <button
                      key={index}
                      onClick={() => setSelectedPreviewUrl(url)}
                      className={`relative w-16 h-16 border rounded bg-muted overflow-hidden flex items-center justify-center transition-all flex-shrink-0 ${selectedPreviewUrl === url ? 'border-primary ring-2 ring-primary ring-offset-2' : 'border hover:border-primary/50'}`}
                    >
                      <AppImage
                        src={url}
                        alt={`${title || 'Listing image'} thumbnail ${index + 1}`}
                        uiSize={UISize.Thumbnail96}
                        className="object-contain"
                        loading="lazy"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Details Section - Full Width on Mobile, Right Side on Desktop */}
          <div className="w-full lg:flex-1 lg:min-w-0">
            {/* Title and Item ID */}
            <div className="mb-4">
              <h1 className="text-xl font-semibold mb-2 leading-tight">{title}</h1>
              {ebayItemIdFromData && (
                <p className="text-sm text-muted-foreground">
                  Item #{ebayItemIdFromData}
                </p>
              )}
            </div>

            {/* Price */}
            <ProgressiveContent
              isLoading={isLoading}
              fallback={<Skeleton className="h-8 w-32 mb-4" />}
            >
              <div className="mb-4">
                <span className="text-2xl font-bold">
                  {currency === 'USD' ? '$' : currency} {price?.toFixed(2)}
                </span>
                {listingType === 'FixedPriceItem' && (
                  <span className="text-sm text-muted-foreground ml-2">or Best Offer</span>
                )}
              </div>
            </ProgressiveContent>

            {/* Category */}
            <ProgressiveContent
              isLoading={isLoading}
              fallback={<Skeleton className="h-6 w-40 mb-4" />}
            >
              <div className="mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Category:</span>
                  {loadingCategoryPath ? (
                    <div className="inline-flex items-center gap-1">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      <span className="text-sm text-muted-foreground">Loading...</span>
                    </div>
                  ) : categoryPath ? (
                    <span className="text-sm text-foreground">{categoryPath}</span>
                  ) : (
                    <span className="text-sm text-muted-foreground">No category available</span>
                  )}
                  {isTradingCard && <Badge variant="secondary" className="ml-2 text-xs">Trading Card</Badge>}
                </div>
              </div>
            </ProgressiveContent>

            {/* Condition */}
            <ProgressiveContent
              isLoading={isLoading}
              fallback={<Skeleton className="h-6 w-40 mb-4" />}
            >
              <div className="mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Condition:</span>
                  <span className="text-sm">{conditionLabel}</span>
                </div>
                {conditionDescription && (
                  <p className="text-xs text-muted-foreground mt-1 italic">
                    &quot;{conditionDescription}&quot;
                  </p>
                )}
              </div>
            </ProgressiveContent>

            {/* Timeline Display */}
            <ProgressiveContent
              isLoading={isLoading || !timelineData}
              fallback={
                <div className="p-3 border rounded-lg mb-4">
                  <Skeleton className="h-5 w-32 mb-2" />
                  <div className="space-y-2 mb-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-4/5" />
                  </div>
                  <Skeleton className="h-2 w-full mb-2" />
                  <Skeleton className="h-4 w-32 mx-auto" />
                </div>
              }
            >
              {timelineData && timelineData.type === 'fixed' ? (
                <div className="p-3 border rounded-lg bg-card mb-4">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="text-sm font-medium">Listing Timeline</h3>
                    <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${timelineData.isEnded
                      ? 'bg-gray-100 text-gray-800'
                      : timelineData.hasStarted
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                      }`}>
                      {timelineData.statusText}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground mb-2">
                    {timelineData.hasStarted && (
                      <p>
                        {timelineData.isEnded
                          ? `Ran for: ${timelineData.totalRunTimeString || 'N/A'}`
                          : `Active for: ${timelineData.timeActiveString}`}
                      </p>
                    )}
                  </div>
                  <Progress value={timelineData.progressPercent} className="h-2 mb-1" />
                  <div className="text-xs text-center font-medium text-primary">
                    {timelineData.verbalTimeRemaining || timelineData.statusText}
                  </div>
                </div>
              ) : timelineData && timelineData.type === 'gtc' ? (
                <div className="p-3 border rounded-lg bg-card mb-4">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="text-sm font-medium">Listing Information</h3>
                    <span className="text-xs font-medium px-2 py-0.5 rounded-full bg-green-100 text-green-800">
                      {timelineData.statusText}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {timelineData.hasStarted ? (
                      <p>Active for: <span className="font-medium text-foreground">{timelineData.timeActiveString}</span></p>
                    ) : (
                      <p>Scheduled to start soon</p>
                    )}
                  </div>
                </div>
              ) : (
                <div className="p-3 border rounded-lg bg-card mb-4">
                  <h3 className="text-sm font-medium mb-1">Listing Information</h3>
                  <p className="text-xs text-muted-foreground">Timeline data unavailable</p>
                </div>
              )}
            </ProgressiveContent>

            {/* Quick Details */}
            <ProgressiveContent
              isLoading={isLoading}
              fallback={
                <div className="space-y-2 mb-4">
                  {[1, 2, 3, 4].map((i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              }
            >
              <div className="space-y-2 text-sm mb-4">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Format:</span>
                  <span>{listingType === 'FixedPriceItem' ? 'Fixed Price' : listingType || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Quantity:</span>
                  <span>
                    {totalQuantity ?? 'N/A'}
                    {quantitySold !== undefined && quantitySold > 0 && (
                      <span className="text-muted-foreground ml-1">({quantitySold} sold)</span>
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <span>{sellingStatus}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Watchers:</span>
                  <span>{watchCount ?? 0}</span>
                </div>
                {sku && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">SKU:</span>
                    <span className="text-right break-all" title={sku}>{sku}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Location:</span>
                  <span>
                    {location || '-'}
                    {postalCode ? `, ${postalCode}` : ''}
                  </span>
                </div>
              </div>
            </ProgressiveContent>
          </div>
        </div>

        {/* Tabs Section - Full Width Below */}
        <div className="mt-6">
          <ProgressiveContent
            isLoading={isLoading}
            fallback={
              <div className="space-y-3">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-64 w-full" />
              </div>
            }
          >
            <div>
              <Tabs defaultValue="description">
                <TabsList className="w-full justify-start">
                  <TabsTrigger value="description">Description</TabsTrigger>
                  <TabsTrigger value="specifics">Specifics</TabsTrigger>
                  <TabsTrigger value="shipping">Shipping</TabsTrigger>
                  {isTradingCard && (
                    <TabsTrigger value="trading-card" className="flex items-center gap-2">
                      <Award className="h-3 w-3" />
                      Trading Card
                    </TabsTrigger>
                  )}
                </TabsList>

                <TabsContent value="description" className="p-3 border rounded-lg mt-2">
                  {htmlDescription ? (
                    <div className="prose prose-sm dark:prose-invert">
                      <div dangerouslySetInnerHTML={{ __html: htmlDescription ?? '' }} />
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground italic">
                      {isFetching ? 'Loading description...' : 'No description available.'}
                    </p>
                  )}
                </TabsContent>

                <TabsContent value="specifics" className="p-3 border rounded-lg mt-2">
                  {itemSpecifics.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-1 text-sm">
                      {itemSpecifics.map((spec: MappedItemSpecific, index: number) => (
                        <React.Fragment key={`${spec.Name}-${index}`}>
                          <p className="font-medium truncate text-xs" title={spec.Name}>
                            {spec.Name}:
                          </p>
                          <p className="text-muted-foreground truncate text-xs md:col-span-1 lg:col-span-2" title={spec.Value}>
                            {spec.Value}
                          </p>
                        </React.Fragment>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground italic">
                      No item specifics available.
                    </p>
                  )}
                </TabsContent>

                <TabsContent value="shipping" className="p-3 border rounded-lg mt-2">
                  <div className="space-y-4 text-sm">
                    <div>
                      <h4 className="text-xs font-medium">Location</h4>
                      <p className="text-xs text-muted-foreground">
                        {location || '-'}
                        {postalCode ? `, ${postalCode}` : ''}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-xs font-medium">Returns</h4>
                      <p className="text-xs text-muted-foreground">{returnPolicy || 'Not specified'}</p>
                    </div>
                  </div>
                </TabsContent>

                {isTradingCard && (
                  <TabsContent value="trading-card" className="p-3 border rounded-lg mt-2">
                    <TradingCardDetails
                      itemData={displayDataSource}
                      categoryPath={categoryPath}
                      loadingCategoryPath={loadingCategoryPath}
                      cachedMetadata={cachedMetadataQuery}
                    />
                  </TabsContent>
                )}
              </Tabs>
            </div>
          </ProgressiveContent>
        </div>
      </div>
    </ScrollArea>
  );
}
