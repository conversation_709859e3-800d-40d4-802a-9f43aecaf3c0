// src/components/ebay/trading/ListingEditorV2.tsx
'use client';

import React, { useCallback, useMemo, useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '@convex/_generated/api';
import { Doc, Id } from '@convex/_generated/dataModel';
import { useDebouncedCallback } from 'use-debounce';

// UI Components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PriceInput } from '@/components/ui/price-input';
import {
  Card,
  CardContent,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, AlertCircle as AlertCircleIconLucide, Pencil, Code, StarsIcon, InfoIcon, StoreIcon, Gavel } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { TooltipProvider } from '@/components/ui/tooltip';
import Link from 'next/link';

import { logger } from '@/lib/logger';

// Form State Management
import { useForm, useWatch } from 'react-hook-form';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import type { ListingEditorFormValues } from '@/lib/ebay/trading/schemas';

// Context Providers
import { WorkflowProvider, useWorkflow } from '@/lib/ebay/contexts/ListingWorkflowContext';

// eBay Types & Components
import { useCurrentMarketplaceId } from '@/lib/state/useWorkspaceContext';
import { ProductSelector } from '@/components/ebay/ProductSelector';
import { CategorySelectorField } from './fields/CategorySelectorField';
import { ConditionSelectorField } from './fields/ConditionSelectorField';

// Item Aspects & Image Management
import { ItemAspectsProvider } from '@/components/ebay/ItemAspectsContext';
import { ItemAspects } from '@/components/ebay/ItemAspects';
import { useAutosave } from '@/lib/ebay/hooks/useAutosave';
import { useProductSelection } from '@/lib/ebay/hooks/useProductSelection';
import { ListingPoliciesTab } from './tabs/ListingPoliciesTab';
import { ManualFulfillmentTab } from './tabs/ManualFulfillmentTab';
import { InListingImageSelector } from '@/components/ebay/InListingImageSelector';
import { useActiveEbayAccount } from '@/lib/ebay/hooks/useActiveEbayAccount';

// --- Import Refactored Hooks ---
import { useListingForm } from '@/lib/ebay/hooks/useListingForm';
import { useListingAiEnhancer } from '@/lib/ebay/hooks/useListingAiEnhancer';
import { useBytescaleFilenames } from '@/lib/ebay/hooks/useBytescaleFilenames';
import { useListingTypeManager } from '@/lib/ebay/hooks/useListingTypeManager';
import { useManageItemAspectsV3 } from '@/lib/ebay/hooks/useManageItemAspects';
import type { ProductDetails } from '@/lib/ebay/types/product-types';
import type { NameValueListType, ProductListingDetailsType, ItemType } from '@/generated/trading/trading';
import { useEbayCategoryValidation } from '@/lib/ebay/hooks/useEbayCategoryValidation';


import type { MarketplaceId } from '@/lib/ebay/types/marketplaces';
// Note: State management now uses XState - see useListingEditorMachine
import { DEFAULT_ENHANCE_DESCRIPTION_USER_PROMPT } from '@/lib/ai/seller-prompts';

// Import the specific action needed for server-side verify

// Define ListingData type to match Convex action response
type ListingData = {
  suggestedTitle: string;
  suggestedSubtitle?: string;
  description: string;
  valuationEstimate?: { low: number; best: number; high: number };
  buyerPersona?: string;
  potentialBuyerQuestions?: string[];
  suggestedAspects?: Array<{ name: string; values: string[] }>;
};

// --- Lexical Editor Imports ---
import dynamic from 'next/dynamic';
import type { LexicalEditor } from 'lexical';
import { $generateHtmlFromNodes } from '@lexical/html';
import type { EditorProps as LexicalEditorPropsType } from '@/components/editor';

// --- Import Lexical Utils ---
import { convertHtmlToLexicalJson, EMPTY_LEXICAL_JSON_STATE } from '@/lib/lexical/utils';
import { convertLexicalJsonToHtml } from '@/lib/lexical/utils';
import { marked } from 'marked';
import { getConditionDetails } from '@/lib/ebay/types/condition-enum';

// --- Lexical Editor Component & Constants ---
const LexicalRichTextEditor = dynamic<LexicalEditorPropsType>(() => import('@/components/editor'), {
  ssr: false,
  loading: () => <div className="p-4">Loading Editor...</div>,
});

/**
 * Props for the ListingEditorV2 component.
 * @property uuid - Unique identifier for the draft listing.
 * @property className - Optional CSS class for styling.
 * @property useExternalWorkflowProvider - If true, uses a parent-provided WorkflowProvider.
 */
interface ListingEditorV2Props {
  uuid: string;
  className?: string;
  useExternalWorkflowProvider?: boolean;
}

const log = logger.create('components:ebay:trading:ListingEditorV2');

// Helper component for Title Character Count
const TitleCharacterCounter = React.memo((
  { control }: { control: ReturnType<typeof useForm<ListingEditorFormValues>>['control'] }
) => {
  const titleValue = useWatch({ control, name: 'Title' }) ?? '';
  const currentLength = titleValue.length;
  const maxLength = 80;
  let lengthColor = 'text-muted-foreground';

  if (currentLength > maxLength) {
    lengthColor = 'text-destructive';
  } else if (currentLength > maxLength - 10) {
    lengthColor = 'text-amber-600';
  }

  return (
    <div className="flex justify-between items-center pt-1">
      <p className={`text-xs ${lengthColor}`}>
        {currentLength}/{maxLength}
      </p>
      {currentLength > maxLength && (
        <p className="text-xs text-destructive">
          Exceeds {maxLength} characters
        </p>
      )}
    </div>
  );
});
TitleCharacterCounter.displayName = 'TitleCharacterCounter';

/**
 * ListingEditorV2
 *
 * Main entry point for the eBay listing editor. Handles loading the draft, error states,
 * and wraps the content in the appropriate workflow context provider.
 */
export function ListingEditorV2({
  uuid,
  className,
  useExternalWorkflowProvider = false,
}: ListingEditorV2Props) {
  // Fetch the draft listing data from Convex using the provided UUID.
  const draftDataResult = useQuery(api.drafts.getDraftByUuid, { uuid });
  const isLoadingDraft = draftDataResult === undefined;
  const draftData = isLoadingDraft ? null : draftDataResult;
  const { form } = useListingForm({ draftData });



  // Show loading spinner while fetching draft data.
  if (isLoadingDraft) {
    return (
      <div className="flex justify-center items-center p-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading draft...</span>
      </div>
    );
  }

  // Show error UI if draft is not found.
  if (!isLoadingDraft && !draftData) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-xl font-semibold mb-2">Draft Not Found</h2>
        <p className="text-muted-foreground mb-4">
          The requested draft listing could not be found.
        </p>
        <Button variant="outline" asChild>
          <a href="/sell/inventory">Return to Inventory</a>
        </Button>
      </div>
    );
  }

  // Render the main editor content, optionally wrapped in a WorkflowProvider.
  return (
    <Form {...form}>
      {useExternalWorkflowProvider ? (
        <ListingEditorV2Content
          uuid={uuid}
          className={className}
          draftData={draftData}
          form={form}
        />
      ) : (
        <WorkflowProvider uuid={uuid}>
          <ListingEditorV2Content
            uuid={uuid}
            className={className}
            draftData={draftData}
            form={form}
          />
        </WorkflowProvider>
      )}
    </Form>
  );
}

/**
 * Props for the ListingEditorV2Content component.
 * @property uuid - Unique identifier for the draft listing.
 * @property className - Optional CSS class for styling.
 * @property draftData - The draft data document from Convex.
 * @property form - The react-hook-form instance for the listing.
 */
interface ContentProps {
  uuid: string;
  className?: string;
  draftData: Doc<'userListings'> | null;
  form: ReturnType<typeof useForm<ListingEditorFormValues>>;
}

/**
 * ListingEditorV2Content
 *
 * Renders the main content of the listing editor, including all form fields, tabs, image management,
 * item specifics, AI description generation, and publishing controls.
 * This component expects to be rendered within a WorkflowProvider context.
 */
function ListingEditorV2Content({
  uuid,
  className,
  draftData: initialDraftData,
  form,
}: ContentProps) {
  const {
    draftData,
    isLoading: isLoadingContext,
    isEbayAccountOptedIn,
    isLoadingOptInStatus,
  } = useWorkflow();
  const { currentMarketplaceId: currentMarketplace } = useCurrentMarketplaceId();
  const { activeAccount } = useActiveEbayAccount();
  const updateDraft = useMutation(api.drafts.updateDraft);
  const syncPictureDetailsMutation = useMutation(api.drafts.syncImageIdsWithPictureDetails);
  const processSkuAi = useAction(api.skuActionsPublic.processSkuAi);

  // Use the most up-to-date draft data from context, fallback to initial prop.
  const currentDraftData = !isLoadingContext ? draftData : initialDraftData;

  // Use marketplace from draft data if available, otherwise fall back to context (default to EBAY_US)
  const marketplace = (currentDraftData?.marketplaceId || currentMarketplace || 'EBAY_US') as MarketplaceId;

  // Debug logging for marketplace resolution
  useEffect(() => {
    if (currentDraftData) {
      log.debug('Marketplace resolution debug', {
        uuid: currentDraftData.uuid,
        draftMarketplaceId: currentDraftData.marketplaceId,
        contextMarketplace: currentMarketplace,
        resolvedMarketplace: marketplace,
        isRevision: !!currentDraftData.ebayItemId,
        site: (currentDraftData.ebayDraft as Partial<ItemType>)?.Site,
      });
    }
  }, [currentDraftData?.marketplaceId, currentMarketplace, marketplace, currentDraftData?.uuid, currentDraftData?.ebayItemId, currentDraftData?.ebayDraft, currentDraftData]);

  // Watch key form fields for changes.
  const watchedCategoryId = useWatch({
    control: form.control,
    name: 'PrimaryCategory.CategoryID',
  });
  const watchedProductReferenceID = useWatch({
    control: form.control,
    name: 'ProductListingDetails.ProductReferenceID',
  });

  // Internal state for SKU Convex ID and AI generation.
  const [internalSkuId, setInternalSkuId] = useState<Id<'skus'> | null>(null);

  // State for description editor dialog (general).
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [modalEditorKey, setModalEditorKey] = useState(0); // Used to force editor remount

  // --- Lexical Editor State for Dialog ---
  const lexicalEditorInstanceRef = useRef<LexicalEditor | null>(null);
  const [isLexicalEditorReady, setIsLexicalEditorReady] = useState(false);
  // Stores the Lexical state as a JSON string for the dialog editor.
  const [dialogLexicalJson, setDialogLexicalJson] = useState<string>(EMPTY_LEXICAL_JSON_STATE);
  // --- End Lexical Editor State ---

  // State for AI enhancement within the dialog.
  const [isEnhancingInDialog, setIsEnhancingInDialog] = useState(false);

  // Fetch the SKU document from Convex when SKU string changes.
  const linkedSkuDoc = useQuery(
    api.skus.getSkuDocById,
    internalSkuId ? { skuId: internalSkuId } : 'skip'
  );

  // Fetch research notes for the SKU (used in dialog AI enhance).
  const researchNotesForDialog = useQuery(
    api.skus.getSkuResearchNotesEvents,
    internalSkuId ? { skuId: internalSkuId } : 'skip'
  );

  // Update internal SKU ID state based on SKU document fetch result.
  useEffect(() => {
    if (currentDraftData?.skuId) {
      setInternalSkuId(currentDraftData.skuId);
      log.debug('[ListingEditorV2Content] Set internalSkuId from currentDraftData', {
        skuId: currentDraftData.skuId,
        uuid: currentDraftData.uuid,
      });
    } else if (!isLoadingContext && currentDraftData && !currentDraftData.skuId) {
      log.error(
        '[ListingEditorV2Content] currentDraftData loaded but internal SKU ID (draftData.skuId) is missing!',
        { uuid: currentDraftData.uuid },
      );
      toast.error(
        'Critical error: Draft is not properly linked to an internal SKU. AI features may be limited or unavailable.',
      );
      setInternalSkuId(null);
    } else if (isLoadingContext) {
      log.debug('[ListingEditorV2Content] Waiting for currentDraftData to load...');
    } else if (!currentDraftData) {
      log.warn(
        '[ListingEditorV2Content] currentDraftData is null or undefined, cannot set internalSkuId.',
      );
      setInternalSkuId(null);
    }
  }, [currentDraftData, isLoadingContext, linkedSkuDoc, form]);

  // Fetch category validation metadata for the selected category and marketplace.
  const { metadataLoading, metadataQuery } = useEbayCategoryValidation(
    watchedCategoryId,
    marketplace,
  );

  // AI Enhancer Hook for description field.
  const { enhance: enhanceFormDescription } = useListingAiEnhancer({
    skuId: internalSkuId,
  });
  const [isEnhancingForm, setIsEnhancingForm] = useState(false);

  /**
   * Immediately update the draft in Convex with the provided updates.
   * Provides logging and error handling.
   */
  const updateDraftImmediately = useCallback(
    async (updates: Partial<Doc<'userListings'>>) => {
      const timestamp = Date.now();
      log.debug(`[${timestamp}] updateDraftImmediately called`, {
        uuid,
        keys: Object.keys(updates),
      });
      try {
        await updateDraft({ uuid, updates });
      } catch (error) {
        log.error(`[${timestamp}] updateDraftImmediately failed`, { uuid, error });
        toast.error('Failed to save changes.');
      }
    },
    [updateDraft, uuid],
  );

  /**
   * Debounced update function for item aspects.
   * Waits 750ms after the last change before saving.
   */
  const debouncedUpdateAspects = useDebouncedCallback(
    useCallback(
      (aspectsUpdate: { itemAspects: NameValueListType[] }) => {
        const timestamp = Date.now();
        log.debug(`[${timestamp}] Debounced updateAspects called`, {
          uuid,
          aspectCount: aspectsUpdate.itemAspects.length,
        });
        updateDraftImmediately(aspectsUpdate);
      },
      [updateDraftImmediately, uuid],
    ),
    750,
  );

  /**
   * Handler for when the category changes.
   * Triggers an immediate update to the draft.
   */
  const handleCategoryChange = useCallback(
    (newCategoryId: string | null) => {
      const timestamp = Date.now();
      log.debug(`[${timestamp}] Category changed, updating immediately`, {
        uuid,
        newCategoryId,
        currentCategory: currentDraftData?.primaryCategoryId,
        isRevision: !!currentDraftData?.ebayItemId,
        currentConditionId: currentDraftData?.conditionId,
        currentEbayDraftConditionID: (currentDraftData?.ebayDraft as Partial<ItemType>)?.ConditionID,
      });
      updateDraftImmediately({ primaryCategoryId: newCategoryId });
    },
    [updateDraftImmediately, uuid, currentDraftData],
  );

  /**
   * Handler for when the condition changes.
   * Updates the draft immediately.
   */
  const handleConditionChange = useCallback(
    (newConditionId: number | null) => {
      const timestamp = Date.now();
      log.debug(`[${timestamp}] Condition changed, updating immediately`, {
        uuid,
        newConditionId,
        currentConditionId: currentDraftData?.conditionId,
        currentEbayDraftConditionID: (currentDraftData?.ebayDraft as Partial<ItemType>)?.ConditionID,
        currentRHFConditionID: form.getValues('ConditionID'),
        isRevision: !!currentDraftData?.ebayItemId,
        formIsDirty: form.formState.isDirty,
      });
      const updates: Partial<Doc<'userListings'>> = {
        conditionId: newConditionId,
        ebayDraft: {
          ConditionID: newConditionId,
        },
      };
      updateDraftImmediately(updates);
    },
    [updateDraftImmediately, uuid, currentDraftData, form],
  );

  // Fetch item aspects metadata for the selected category and marketplace.
  const {
    aspectsMetadata: aspectMetadata,
    isLoading: isLoadingMetadata,
    error: errorMetadata,
  } = useManageItemAspectsV3({
    categoryId: watchedCategoryId,
    marketplaceId: marketplace,
  });

  /**
   * Handler for when a product is selected or deselected.
   * Updates the draft immediately.
   */
  const handleProductSelect = useCallback(
    (product: ProductDetails | null) => {
      log.debug('Product selected/deselected, updating immediately', { uuid, product });
      const productDetailsUpdate: ProductListingDetailsType | undefined = product
        ? { ProductReferenceID: product.epid, IncludeStockPhotoURL: true }
        : undefined;

      const updates: Partial<Doc<'userListings'>> = {
        productListingDetails: productDetailsUpdate,
      };

      // Populate item specifics from catalog product aspects when selecting a product
      if (product && product.aspects && product.aspects.length > 0) {
        // Get the list of aspect names that are defined in the taxonomy metadata for this category
        const taxonomyAspectNames = new Set(
          aspectMetadata?.aspects?.map(aspect => aspect.localizedAspectName).filter(Boolean) || []
        );

        const itemAspectsFromProduct: NameValueListType[] = product.aspects
          .filter(aspect => {
            // Only include aspects that are defined in the taxonomy metadata
            const aspectName = aspect.localizedName;
            return aspectName && taxonomyAspectNames.has(aspectName);
          })
          .map(aspect => ({
            Name: aspect.localizedName || "Unknown Aspect",
            Value: aspect.localizedValues?.filter(v => v !== "N/A") || [],
            Source: "Product" as const
          }))
          .filter(a => a.Name !== "Unknown Aspect" && a.Value.length > 0);

        updates.itemAspects = itemAspectsFromProduct;
        log.debug('Populated item specifics from catalog product (filtered to taxonomy aspects)', {
          uuid,
          epid: product.epid,
          totalCatalogAspects: product.aspects.length,
          filteredAspectCount: itemAspectsFromProduct.length,
          taxonomyAspectCount: taxonomyAspectNames.size
        });
      } else if (!product) {
        // Clear item specifics when deselecting a product
        updates.itemAspects = undefined;
        // Also explicitly clear the form field to prevent re-loading
        form.setValue('ProductListingDetails.ProductReferenceID', undefined);
        log.debug('Cleared item specifics and form field after product deselection', { uuid });
      }

      updateDraftImmediately(updates);
    },
    [updateDraftImmediately, uuid, aspectMetadata, form],
  );

  /**
   * Handler for when item aspects change.
   * Calls the debounced aspect updater.
   */
  const handleAspectsChange = useCallback(
    (update: { values: Record<string, string[]>; isValid: boolean }) => {
      log.debug('Aspects changed, queueing debounced update', {
        uuid,
        changedAspectsCount: Object.keys(update.values).length,
        frontendIsValid: update.isValid,
      });
      const nameValueList: NameValueListType[] = Object.entries(update.values).map(
        ([name, values]) => ({ Name: name, Value: values }),
      );
      debouncedUpdateAspects({ itemAspects: nameValueList });
    },
    [debouncedUpdateAspects, uuid],
  );

  // Used for logging product selection changes (for hook).
  const handleProductSelectionForHook = useCallback((isSelected: boolean) => {
    log.debug('Product selection changed (for hook)', { isSelected });
  }, []);
  const { selectedProduct } = useProductSelection(handleProductSelectionForHook);

  // Log image management props for debugging.
  log.debug('[ListingEditorV2Content] Props for useImageManagement (Now direct draft data)', {
    initialImageIdsFromDraft: currentDraftData?.imageStatus?.imageIds,
    currentDraftDataExists: !!currentDraftData,
    currentDraftDataImageStatusExists: !!currentDraftData?.imageStatus,
  });

  // Image IDs directly from Convex draft data.
  const listingImageIdsFromDraft = useMemo(
    () => currentDraftData?.imageStatus?.imageIds || [],
    [currentDraftData?.imageStatus?.imageIds]
  );

  /**
   * Central handler to update listing image IDs in Convex and form state.
   * Also syncs image IDs to eBay draft PictureDetails.
   */
  const updateListingImageIds = useCallback(async (newImageIds: Id<'images'>[]) => {
    log.debug(
      'ListingEditorV2Content: updateListingImageIds called.',
      { uuid, newImageIdCount: newImageIds.length }
    );

    // 1. Update imageStatus.imageIds in the Convex draft.
    await updateDraftImmediately({
      imageStatus: {
        ...(currentDraftData?.imageStatus ?? { imageIds: [] }),
        imageIds: newImageIds,
      },
    });

    // 2. Update React Hook Form state.
    form.setValue('imageStatus.imageIds', newImageIds, {
      shouldDirty: true,
      shouldValidate: true,
    });

    // 3. Sync these newImageIds to ebayDraft.PictureDetails.PictureURL.
    if (uuid && currentDraftData?._id) {
      try {
        log.debug('Calling syncImageIdsWithPictureDetails mutation', { draftId: currentDraftData._id, imageIdsToSync: newImageIds });
        await syncPictureDetailsMutation({ draftId: currentDraftData._id, imageIds: newImageIds });
        log.info('Successfully synced PictureDetails.');
      } catch (error) {
        log.error('Failed to sync PictureDetails', { uuid, draftId: currentDraftData._id, error });
        toast.error('Failed to update image URLs for eBay.');
      }
    }
  }, [
    updateDraftImmediately,
    uuid,
    currentDraftData?._id,
    currentDraftData?.imageStatus,
    form,
    syncPictureDetailsMutation
  ]);

  /**
   * Memoized list of available conditions for the selected category.
   */
  const categoryConditions = useMemo(() => {
    log.debug('[useMemo categoryConditions] Recalculating', {
      categoryId: watchedCategoryId,
      metadataLoading,
      hasData: !!metadataQuery.data?.conditionMetadata,
      dataTimestamp: metadataQuery.data?._creationTime || Date.now(),
    });
    if (!metadataQuery.data?.conditionMetadata) {
      return [];
    }
    const conditionMetadata = metadataQuery.data.conditionMetadata;
    const conditions = conditionMetadata.conditions;
    const conditionDescriptors = conditionMetadata.conditionDescriptors;

    return Array.isArray(conditions)
      ? conditions.map((c) => ({
        id: Number(c.id),
        name: c.name,
        conditionDescriptors: conditionDescriptors
      }))
      : [];
  }, [
    metadataQuery.data?._creationTime,
    metadataQuery.data?.conditionMetadata,
    watchedCategoryId,
    metadataLoading,
  ]);

  /**
   * Memoized mapping of item aspects for UI consumption.
   */
  const aspectValuesForUI = useMemo(() => {
    const aspectsMap: Record<string, string[]> = {};
    const aspectsList = currentDraftData?.itemAspects;
    if (aspectsList && Array.isArray(aspectsList)) {
      aspectsList.forEach((aspect) => {
        if (aspect.Name && aspect.Value) {
          const values = Array.isArray(aspect.Value) ? aspect.Value : [aspect.Value];
          aspectsMap[aspect.Name] = values.filter(
            (v: string): v is string => typeof v === 'string',
          );
        }
      });
    }
    return aspectsMap;
  }, [currentDraftData?.itemAspects]);

  // Update Bytescale filenames for image management (side effect).
  useBytescaleFilenames({ draftData: currentDraftData, imageIds: listingImageIdsFromDraft });

  /**
   * Type for the updateDraft mutation function.
   */
  type EditorUpdateDraftFn = (args: {
    uuid: string;
    updates: Partial<Doc<'userListings'>>;
  }) => Promise<unknown>;

  // Autosave logic for the listing form.
  useAutosave({
    uuid,
    form,
    updateDraftMutation: updateDraft as EditorUpdateDraftFn,
    isLoadingDraft: isLoadingContext,
    selectedProduct,
    itemAspects: aspectValuesForUI,
    draftData: currentDraftData,
  });


  // Listing type management (e.g., FixedPriceItem vs. Auction).
  const { listingType, handleListingTypeChange } = useListingTypeManager({
    uuid,
    form,
    updateDraftMutation: updateDraft as EditorUpdateDraftFn,
  });




  /**
   * Handler for AI enhancement directly within the Edit Description dialog.
   * Uses the current Lexical editor state and updates the dialog content with AI-generated text.
   */
  const handleEnhanceDialogLexicalContent = useCallback(
    async (userPrompt?: string) => {
      if (!lexicalEditorInstanceRef.current) {
        toast.error('Lexical editor instance not available for dialog AI enhancement.');
        return;
      }
      if (!internalSkuId) {
        toast.error(
          'SKU ID is missing from draft. Cannot enhance description from dialog without SKU context.',
        );
        log.warn(
          '[handleEnhanceDialogLexicalContent] Aborted: internalSkuId is null. currentDraftData?.skuId was:',
          currentDraftData?.skuId,
        );
        setIsEnhancingInDialog(false);
        return;
      }

      const notesForAi = researchNotesForDialog?.success && researchNotesForDialog.notes
        ? researchNotesForDialog.notes.map(entry => entry.eventData.note)
        : [];

      setIsEnhancingInDialog(true);
      log.info('Starting AI description enhancement from dialog', { skuId: internalSkuId });

      try {
        const currentDialogEditorState = lexicalEditorInstanceRef.current.getEditorState();
        const currentDialogLexicalJson = JSON.stringify(currentDialogEditorState.toJSON());
        log.debug('[AI Enhance Dialog] currentDialogLexicalJson from editor:', {
          skuId: internalSkuId,
          currentDialogLexicalJson,
        });
        const htmlContentForAi = convertLexicalJsonToHtml(currentDialogLexicalJson);
        log.debug('[AI Enhance Dialog] htmlContentForAi after conversion:', {
          skuId: internalSkuId,
          htmlContentForAi,
        });

        const title = form.getValues('Title') || undefined;
        const conditionId = form.getValues('ConditionID') || undefined;

        const listingContextForAi: Partial<
          Pick<ListingData, 'suggestedTitle'> & { conditionLabel?: string }
        > = {};
        if (title) listingContextForAi.suggestedTitle = title;

        if (conditionId) {
          const conditionInfo = getConditionDetails(conditionId);
          if (conditionInfo) {
            listingContextForAi.conditionLabel = conditionInfo.label;
          }
        }

        const aiResponse = await processSkuAi({
          skuId: internalSkuId,
          existingDescription: htmlContentForAi,
          enhancementPrompt: userPrompt || DEFAULT_ENHANCE_DESCRIPTION_USER_PROMPT,
          listingContext:
            Object.keys(listingContextForAi).length > 0 ? listingContextForAi : undefined,
          researchNotes: notesForAi.length > 0 ? notesForAi : undefined,
        });

        if (!aiResponse.success) {
          const errorMsg = aiResponse.error;
          toast.error('AI Enhancement Error', { description: errorMsg });
          log.error('AI enhancement (dialog) failed', { skuId: internalSkuId, error: errorMsg });
        } else if (!aiResponse.data?.description) {
          const errorMsg = 'AI enhancement succeeded but produced no description.';
          toast.error('AI Enhancement Error', { description: errorMsg });
          log.error('AI enhancement (dialog) produced no description', {
            skuId: internalSkuId,
            response: aiResponse,
          });
        } else {
          const aiHtmlOutput = marked.parse(aiResponse.data.description) as string;
          if (!aiHtmlOutput || typeof aiHtmlOutput !== 'string') {
            throw new Error(
              'Markdown to HTML conversion resulted in invalid output for dialog AI.',
            );
          }

          const newLexicalJsonForDialog = convertHtmlToLexicalJson(aiHtmlOutput);

          if (lexicalEditorInstanceRef.current) {
            const newEditorState =
              lexicalEditorInstanceRef.current.parseEditorState(newLexicalJsonForDialog);
            lexicalEditorInstanceRef.current.setEditorState(newEditorState);
            setDialogLexicalJson(newLexicalJsonForDialog);
            toast.success('Content updated by AI in editor.');
          }
        }
      } catch (e) {
        const errorMsg =
          e instanceof Error
            ? e.message
            : 'An unknown error occurred during dialog AI enhancement.';
        toast.error('Dialog AI Error', { description: errorMsg });
        log.error('Error in handleEnhanceDialogLexicalContent function', {
          error: e,
          skuId: internalSkuId,
        });
      } finally {
        setIsEnhancingInDialog(false);
      }
    },
    [currentDraftData?.skuId, form, internalSkuId, researchNotesForDialog, processSkuAi],
  );

  /**
   * Handler for saving the description from the dialog editor.
   * Updates both the form state and the Convex draft with Lexical JSON and HTML.
   */
  const handleSaveDescription = useCallback(async () => {
    if (!lexicalEditorInstanceRef.current) {
      toast.error('Lexical editor instance not available.');
      log.error('Cannot save description, Lexical editor instance not ready.');
      return;
    }
    try {
      const editorState = lexicalEditorInstanceRef.current.getEditorState();
      const lexicalJsonString = JSON.stringify(editorState.toJSON());

      // 1. Update the RHF state with the Lexical JSON string for the itemDescriptionLexical field.
      form.setValue('itemDescriptionLexical', lexicalJsonString, {
        shouldValidate: true,
        shouldDirty: true,
      });

      // 2. Explicitly save ONLY the itemDescriptionLexical to Convex.
      log.debug('Saving itemDescriptionLexical (Lexical JSON) from modal', {
        uuid,
        length: lexicalJsonString.length,
      });
      await updateDraftImmediately({
        itemDescriptionLexical: lexicalJsonString,
      });

      // 3. Generate HTML from the saved Lexical state and update the HTML Description field in the form.
      let generatedHtmlString =
        '<p class="text-muted-foreground italic">Error generating HTML preview.</p>';
      try {
        editorState.read(() => {
          generatedHtmlString = $generateHtmlFromNodes(lexicalEditorInstanceRef.current!, null);
        });
        form.setValue('Description', generatedHtmlString, {
          shouldValidate: true,
          shouldDirty: true,
        });
      } catch (err: unknown) {
        log.error(
          'Failed to convert new Lexical JSON to HTML for form update',
          err,
        );
      }

      // 4. Update Convex with both itemDescriptionLexical AND the new HTML in ebayDraft.Description.
      log.debug('Saving itemDescriptionLexical and updating ebayDraft.Description in Convex', {
        uuid,
        lexicalLength: lexicalJsonString.length,
        htmlLength: generatedHtmlString.startsWith(
          '<p class="text-muted-foreground italic">Error generating HTML preview.</p>',
        )
          ? -1
          : generatedHtmlString.length,
      });
      await updateDraftImmediately({
        itemDescriptionLexical: lexicalJsonString,
        ebayDraft: {
          Description: generatedHtmlString,
        },
      });

      // 5. Close dialog and show success.
      setIsEditingDescription(false);
      toast.info('Description updated.');
    } catch (error) {
      log.error('Error saving description from modal (Lexical)', { uuid, error });
      toast.error('Failed to save description.');
    }
  }, [form, uuid, updateDraftImmediately]);

  // Debugging logs to trace skuId propagation.
  useEffect(() => {
    log.debug(
      `[ListingEditorV2Content] internalSkuId state: ${internalSkuId}, currentDraftData.skuId: ${currentDraftData?.skuId}`,
      { uuid: currentDraftData?.uuid },
    );
  }, [internalSkuId, currentDraftData]);

  // --- Render the main editor card and form ---
  return (
    <Card className={cn('w-full max-w-6xl mx-auto relative', className)}>
      {/* <CardHeader>
        <div className="flex justify-between items-center">
        </div>
        {errorMetadata && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircleIconLucide className="h-4 w-4" />
            <AlertTitle>Error Loading Category Metadata</AlertTitle>
            <AlertDescription>{errorMetadata}</AlertDescription>
          </Alert>
        )}
      </CardHeader> */}

      <form onSubmit={(e) => e.preventDefault()} className="space-y-6">
        <CardContent className="space-y-6 pt-6">
          {/* Essential Information Section */}
          <div className="border rounded-lg p-4">
            <div className="grid grid-cols-1 gap-4 mb-4">
              <div className="space-y-4">
                {/* Title Field */}
                <FormField
                  control={form.control}
                  name="Title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input placeholder="Listing Title" {...field} value={field.value ?? ''} />
                      </FormControl>
                      <TitleCharacterCounter control={form.control} />
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div>
                {/* Category Selector */}
                <CategorySelectorField
                  control={form.control}
                  name="PrimaryCategory.CategoryID"
                  currentMarketplace={marketplace}
                  onCategoryChange={handleCategoryChange}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                {/* Condition Selector */}
                <ConditionSelectorField
                  control={form.control}
                  categoryConditions={categoryConditions}
                  categoryId={watchedCategoryId}
                  onConditionChange={handleConditionChange}
                />
              </div>
              <div>
                {/* SKU Display (Read-Only) */}
                <FormLabel>Linked SKU</FormLabel>
                <div className="mt-1">
                  {!currentDraftData?.skuId && (
                    <Alert variant="destructive">
                      <AlertCircleIconLucide className="h-4 w-4" />
                      <AlertTitle>Error</AlertTitle>
                      <AlertDescription>
                        This draft is not linked to an internal SKU. Please resolve this issue.
                      </AlertDescription>
                    </Alert>
                  )}
                  {currentDraftData?.skuId && linkedSkuDoc === undefined && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Loading SKU details...
                    </div>
                  )}
                  {currentDraftData?.skuId && linkedSkuDoc && (
                    <div className="mt-1 p-3 border rounded-md bg-muted/40 hover:bg-muted/60 transition-colors duration-150 group">
                      <Link href={`/stockroom/skus/id/${linkedSkuDoc.sourceId}/${linkedSkuDoc._id}`} passHref legacyBehavior>
                        <a className="font-medium text-primary group-hover:underline" target="_blank">
                          {linkedSkuDoc.fullSku}
                        </a>
                      </Link>
                      <p className="text-xs text-muted-foreground truncate" title={linkedSkuDoc.whatIsIt}>
                        {linkedSkuDoc.whatIsIt}
                      </p>
                      {linkedSkuDoc.customSkuString && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 pt-1 border-t border-gray-300 dark:border-gray-600 font-mono">
                          Original/User SKU: <span className="italic">{linkedSkuDoc.customSkuString}</span>
                        </p>
                      )}
                    </div>
                  )}
                  {currentDraftData?.skuId && linkedSkuDoc === null && internalSkuId && (
                    <Alert variant="default">
                      <InfoIcon className="h-4 w-4" />
                      <AlertTitle>SKU Not Found</AlertTitle>
                      <AlertDescription>
                        The linked SKU (ID: {internalSkuId}) could not be loaded. It may have been deleted.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
            </div>

            <div className="mb-4">
              <FormLabel>Product Catalog Association</FormLabel>
              <ProductSelector
                marketplaceId={marketplace}
                value={{
                  epid: watchedProductReferenceID ?? undefined,
                }}
                onChange={handleProductSelect}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Linking to an eBay catalog product can auto-fill details and improve visibility.
              </p>
            </div>

            <div className="mt-6">
              <h3 className="font-medium mb-2">Listing Images</h3>
              <InListingImageSelector
                skuId={internalSkuId || undefined}
                skuFullName={internalSkuId && linkedSkuDoc ? linkedSkuDoc.fullSku : undefined}
                skuWhatIsIt={internalSkuId && linkedSkuDoc ? linkedSkuDoc.whatIsIt : undefined}
                selectedImageIds={listingImageIdsFromDraft}
                ebayUserId={activeAccount?.ebayUserId || currentDraftData?.ebayUserId || ''}
                draftId={currentDraftData?._id}
                onImageAdded={useCallback((addedImageId: Id<'images'>) => {
                  const currentIds = currentDraftData?.imageStatus?.imageIds || [];
                  const newIds = [...currentIds, addedImageId].slice(0, 24);
                  updateListingImageIds(newIds);
                }, [currentDraftData?.imageStatus?.imageIds, updateListingImageIds])}
                onImageRemoved={useCallback((removedImageId: Id<'images'>) => {
                  const currentIds = currentDraftData?.imageStatus?.imageIds || [];
                  const newIds = currentIds.filter(id => id !== removedImageId);
                  updateListingImageIds(newIds);
                }, [currentDraftData?.imageStatus?.imageIds, updateListingImageIds])}
                onImageOrderChanged={useCallback((reorderedImageIds: Id<'images'>[]) => {
                  updateListingImageIds(reorderedImageIds);
                }, [updateListingImageIds])}
                onClearAllImages={useCallback(() => {
                  updateListingImageIds([]);
                }, [updateListingImageIds])}
                onMultipleImagesAdded={useCallback((addedIds: Id<'images'>[]) => {
                  const currentIds = currentDraftData?.imageStatus?.imageIds || [];
                  const newUniqueIds = addedIds.filter(id => !currentIds.includes(id));
                  const combinedIds = [...currentIds, ...newUniqueIds].slice(0, 24);
                  updateListingImageIds(combinedIds);
                }, [currentDraftData?.imageStatus?.imageIds, updateListingImageIds])}
                imagesUploadedEventName={currentDraftData?.uuid ? `listing-images-uploaded-${currentDraftData.uuid}` : `listing-images-uploaded-default-${uuid}`}
              />
            </div>
          </div>

          {/* Main Tabs for Listing Details */}
          <Tabs defaultValue="basics" className="w-full">
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="basics">Basics</TabsTrigger>
              <TabsTrigger value="pricing">Pricing & Format</TabsTrigger>
              <TabsTrigger value="location">Location</TabsTrigger>        
              <TabsTrigger value="aspects">Item Specifics</TabsTrigger>
              <TabsTrigger value="shipping">Handling</TabsTrigger>
              {isLoadingOptInStatus && (
                <TabsTrigger value="policies" disabled>
                  Policies <Loader2 className="ml-1 -mr-1 h-3 w-3 animate-spin" />
                </TabsTrigger>
              )}
              {!isLoadingOptInStatus && isEbayAccountOptedIn && (
                <TabsTrigger value="policies">Policies</TabsTrigger>
              )}
              {!isLoadingOptInStatus && !isEbayAccountOptedIn && (
                <TabsTrigger value="manualFulfillment">Fulfillment Setup</TabsTrigger>
              )}
            </TabsList>

            {/* Basics Tab: Description, Subtitle */}
            <TabsContent value="basics" className="mt-4">
              <div className="grid grid-cols-1 gap-8">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-4">Description</h3>
                  <div className="space-y-4">
                    {/* Subtitle Field */}
                    <FormField
                      control={form.control}
                      name="SubTitle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            Subtitle{' '}
                            <span className="text-xs text-amber-500 font-normal">
                              (eBay fee applies)
                            </span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Optional subtitle"
                              maxLength={55}
                              {...field}
                              value={field.value ?? ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    {/* Description Field with AI and HTML Dialogs */}
                    <FormField
                      control={form.control}
                      name="Description"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center justify-between mb-2">
                            <FormLabel>Description</FormLabel>
                            <div className="flex items-center gap-2 flex-wrap">
                              {/* "AI Enhance Full Description" Button */}
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={async () => {
                                  setIsEnhancingForm(true);
                                  try {
                                    const currentTitle = form.getValues('Title');
                                    const currentLexicalDesc =
                                      form.getValues('itemDescriptionLexical');
                                    log.debug('Calling enhanceFormDescription', {
                                      currentTitle,
                                      currentLexicalDescExists: !!currentLexicalDesc,
                                    });

                                    enhanceFormDescription({
                                      currentTitle: currentTitle || undefined,
                                      currentDescription: currentLexicalDesc || undefined,
                                      onSuccess: async (newLexicalJson) => {
                                        log.info(
                                          'AI Enhancement successful, updating form description.',
                                        );
                                        form.setValue(
                                          'itemDescriptionLexical',
                                          newLexicalJson,
                                          { shouldDirty: true, shouldValidate: true },
                                        );

                                        // Convert Lexical to HTML and update form + Convex
                                        try {
                                          const html = convertLexicalJsonToHtml(newLexicalJson);
                                          form.setValue('Description', html, {
                                            shouldDirty: true,
                                          });

                                          // Save Lexical JSON and generated HTML to Convex
                                          log.debug('Saving AI-enhanced description (Lexical & HTML) to Convex', { uuid });
                                          await updateDraftImmediately({
                                            itemDescriptionLexical: newLexicalJson,
                                            ebayDraft: { Description: html },
                                          });
                                          log.info('Successfully saved AI-enhanced description to Convex', { uuid });

                                        } catch (err: unknown) {
                                          log.error(
                                            'Failed to convert new Lexical JSON to HTML for form update or save to Convex',
                                            err,
                                          );
                                          form.setValue('Description', '', {
                                            shouldDirty: true,
                                          });
                                          toast.error('Save Error', { description: 'Could not save updated description.' });
                                        }
                                      },
                                    });
                                  } catch (error) {
                                    log.error('Error in enhance button onClick', error);
                                  } finally {
                                    setTimeout(() => setIsEnhancingForm(false), 500);
                                  }
                                }}
                                disabled={isEnhancingForm || !internalSkuId}
                                className="flex items-center gap-1.5"
                              >
                                {isEnhancingForm ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <StarsIcon className="h-4 w-4 text-blue-500" />
                                )}
                                AI Enhance Description
                              </Button>

                              {/* HTML Diagnostics Dialog */}
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-1.5"
                                  >
                                    <Code className="h-4 w-4" />
                                    View HTML
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-[700px]">
                                  <DialogHeader>
                                    <DialogTitle>Description HTML</DialogTitle>
                                  </DialogHeader>
                                  <Tabs defaultValue="raw">
                                    <TabsList>
                                      <TabsTrigger value="raw">Raw HTML</TabsTrigger>
                                      <TabsTrigger value="formatted">Formatted</TabsTrigger>
                                    </TabsList>
                                    <TabsContent value="raw" className="mt-4">
                                      <Textarea
                                        readOnly
                                        className="font-mono text-xs h-[300px] whitespace-pre overflow-auto"
                                        value={field.value || ''}
                                      />
                                    </TabsContent>
                                    <TabsContent
                                      value="formatted"
                                      className="mt-4 border p-4 rounded h-[300px] overflow-auto"
                                    >
                                      <pre className="text-xs">
                                        {field.value
                                          ? field.value.replace(/</g, '&lt;').replace(/>/g, '&gt;')
                                          : ''}
                                      </pre>
                                    </TabsContent>
                                  </Tabs>
                                </DialogContent>
                              </Dialog>

                              {/* Edit Description Dialog */}
                              <Dialog
                                open={isEditingDescription}
                                onOpenChange={setIsEditingDescription}
                              >
                                <DialogTrigger asChild>
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      const formLexicalValue =
                                        form.getValues('itemDescriptionLexical');
                                      log.debug(
                                        "Attempting to load Lexical editor. Value from form.getValues('itemDescriptionLexical'):",
                                        formLexicalValue,
                                      );
                                      log.debug(
                                        'Current draftData from useWorkflow context:',
                                        draftData,
                                      );

                                      let lexicalJsonToLoad = EMPTY_LEXICAL_JSON_STATE;

                                      const authoritativeLexicalJson =
                                        draftData?.itemDescriptionLexical;

                                      if (authoritativeLexicalJson) {
                                        log.debug(
                                          'Using authoritativeLexicalJson from draftData:',
                                          authoritativeLexicalJson,
                                        );
                                        try {
                                          const parsed = JSON.parse(authoritativeLexicalJson);
                                          if (
                                            parsed &&
                                            parsed.root &&
                                            parsed.root.type === 'root'
                                          ) {
                                            lexicalJsonToLoad = authoritativeLexicalJson;
                                          } else {
                                            log.warn(
                                              'Authoritative itemDescriptionLexical is valid JSON but not in expected Lexical root format. Falling back to empty.',
                                              { authoritativeLexicalJson },
                                            );
                                          }
                                        } catch (e) {
                                          log.warn(
                                            'Failed to parse authoritative itemDescriptionLexical as JSON. Falling back to empty editor.',
                                            {
                                              originalDescription: authoritativeLexicalJson,
                                              error: e,
                                            },
                                          );
                                        }
                                      } else if (formLexicalValue) {
                                        log.debug(
                                          'Authoritative Lexical JSON missing, falling back to formLexicalValue:',
                                          formLexicalValue,
                                        );
                                        try {
                                          const valueToParse =
                                            typeof formLexicalValue === 'string'
                                              ? formLexicalValue
                                              : JSON.stringify(formLexicalValue);
                                          const parsed = JSON.parse(valueToParse);
                                          if (
                                            parsed &&
                                            parsed.root &&
                                            parsed.root.type === 'root'
                                          ) {
                                            lexicalJsonToLoad = valueToParse;
                                          } else {
                                            log.warn(
                                              'itemDescriptionLexical from form (fallback) is valid JSON but not in expected Lexical root format. Falling back to empty.',
                                              { formLexicalValue },
                                            );
                                          }
                                        } catch (e) {
                                          log.warn(
                                            'Failed to parse itemDescriptionLexical from form (fallback) as JSON. Falling back to empty editor.',
                                            { originalDescription: formLexicalValue, error: e },
                                          );
                                        }
                                      } else {
                                        const currentHtmlDescription =
                                          form.getValues('Description');
                                        if (
                                          currentHtmlDescription &&
                                          typeof currentHtmlDescription === 'string' &&
                                          currentHtmlDescription.trim() !== ''
                                        ) {
                                          log.info(
                                            'itemDescriptionLexical is empty, attempting to convert existing HTML description to Lexical JSON.',
                                            { htmlLength: currentHtmlDescription.length },
                                          );
                                          lexicalJsonToLoad =
                                            convertHtmlToLexicalJson(currentHtmlDescription);
                                        } else {
                                          log.debug(
                                            'itemDescriptionLexical is empty, and no valid HTML description found either. Loading empty editor.',
                                          );
                                        }
                                      }

                                      log.debug(
                                        'Final lexicalJsonToLoad for editor:',
                                        lexicalJsonToLoad,
                                      );
                                      setDialogLexicalJson(lexicalJsonToLoad);
                                      setModalEditorKey((prev) => prev + 1);
                                      setIsLexicalEditorReady(false);
                                      setIsEditingDescription(true);
                                    }}
                                    className="flex items-center gap-1.5"
                                  >
                                    <Pencil className="h-4 w-4" />
                                    Edit Description
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="sm:max-w-[80vw] md:max-w-[70vw] lg:max-w-[60vw] max-h-[90vh] flex flex-col">
                                  <DialogHeader className="flex-row items-center justify-between pr-6 pt-6">
                                    <DialogTitle>Edit Description</DialogTitle>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleEnhanceDialogLexicalContent()}
                                      disabled={
                                        isEnhancingInDialog ||
                                        !internalSkuId ||
                                        !lexicalEditorInstanceRef.current
                                      }
                                      className="flex items-center gap-1.5"
                                    >
                                      {isEnhancingInDialog ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <StarsIcon className="h-4 w-4 text-purple-500" />
                                      )}
                                      Enhance in Editor
                                    </Button>
                                  </DialogHeader>

                                  {(dialogLexicalJson.includes('<table') ||
                                    dialogLexicalJson.includes('<font')) && (
                                      <Alert variant="destructive" className="mt-2 mb-4">
                                        <AlertCircleIconLucide className="h-4 w-4" />
                                        <AlertTitle>Editing Imported Description</AlertTitle>
                                        <AlertDescription>
                                          This description may contain complex formatting (like tables
                                          or custom layouts) from another source. Editing here focuses
                                          on the text content; some original layout may not be
                                          preserved. Consider using &quot;AI Enhance Description&quot;
                                          to create a new description in a modern format.
                                        </AlertDescription>
                                      </Alert>
                                    )}

                                  <div className="flex-grow overflow-y-auto p-1">
                                    <TooltipProvider>
                                      <LexicalRichTextEditor
                                        key={modalEditorKey}
                                        isEditable={true}
                                        initialState={dialogLexicalJson}
                                        editorRef={lexicalEditorInstanceRef}
                                        onReady={() => {
                                          setIsLexicalEditorReady(true);
                                          log.debug('Lexical editor in dialog is ready.');
                                        }}
                                      />
                                    </TooltipProvider>
                                  </div>
                                  <DialogFooter className="mt-auto pt-4 border-t">
                                    <DialogClose asChild>
                                      <Button type="button" variant="outline">
                                        Cancel
                                      </Button>
                                    </DialogClose>
                                    <Button
                                      type="button"
                                      onClick={handleSaveDescription}
                                      disabled={!isLexicalEditorReady || isEnhancingForm}
                                    >
                                      Save Changes
                                    </Button>
                                  </DialogFooter>
                                </DialogContent>
                              </Dialog>
                            </div>
                          </div>
                          <div
                            className="description-display max-w-none p-3 border rounded bg-muted/40 min-h-[100px]"
                            dangerouslySetInnerHTML={{
                              __html:
                                form.watch('Description') ||
                                '<p class="text-muted-foreground italic">No description yet.</p>',
                            }}
                          />
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Pricing & Format Tab: Unified pricing and format controls */}
            <TabsContent value="pricing" className="mt-4 space-y-4">
              {/* Format Selection */}
              <div>
                <h3 className="font-medium mb-2">Listing Format</h3>
                <FormField
                  control={form.control}
                  name="ListingType"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex gap-2">
                          <button
                            type="button"
                            className={cn(
                              'px-3 py-1.5 text-sm rounded-md border transition-all flex items-center gap-1.5',
                              field.value === 'FixedPriceItem'
                                ? 'border-primary bg-primary/5 text-primary'
                                : 'border-muted bg-background hover:border-primary/50 text-muted-foreground',
                            )}
                            onClick={() => {
                              field.onChange('FixedPriceItem');
                              handleListingTypeChange('FixedPriceItem');
                            }}
                          >
                            <StoreIcon className="h-3 w-3" />
                            Fixed Price
                          </button>
                          <button
                            type="button"
                            className={cn(
                              'px-3 py-1.5 text-sm rounded-md border transition-all flex items-center gap-1.5',
                              field.value === 'Chinese'
                                ? 'border-primary bg-primary/5 text-primary'
                                : 'border-muted bg-background hover:border-primary/50 text-muted-foreground',
                            )}
                            onClick={() => {
                              field.onChange('Chinese');
                              handleListingTypeChange('Chinese');
                            }}
                          >
                            <Gavel className="h-3 w-3" />
                            Auction
                          </button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Pricing Grid */}
              <div>
                <h3 className="font-medium mb-2">Pricing</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  <FormField
                    control={form.control}
                    name="StartPrice.Value"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">
                          {listingType === 'FixedPriceItem' ? 'Price' : 'Starting Bid'}
                        </FormLabel>
                        <FormControl>
                          <PriceInput
                            value={field.value ?? null}
                            onChange={(value) => {
                              field.onChange(value);
                              if (value !== null && !form.getValues('StartPrice.CurrencyID')) {
                                form.setValue('StartPrice.CurrencyID', 'USD');
                              }
                            }}
                            currency="USD"
                            showCurrency={true}
                            placeholder="0.00"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {listingType === 'Chinese' && (
                    <>
                      <FormField
                        control={form.control}
                        name="ReservePrice.Value"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm">
                              Reserve <span className="text-xs text-amber-500">(fee applies)</span>
                            </FormLabel>
                            <FormControl>
                              <PriceInput
                                value={field.value ?? null}
                                onChange={(value) => {
                                  field.onChange(value);
                                  if (value !== null && !form.getValues('ReservePrice.CurrencyID')) {
                                    form.setValue('ReservePrice.CurrencyID', 'USD');
                                  }
                                }}
                                currency="USD"
                                showCurrency={true}
                                placeholder="0.00"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="BuyItNowPrice.Value"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm">Buy It Now</FormLabel>
                            <FormControl>
                              <PriceInput
                                value={field.value ?? null}
                                onChange={(value) => {
                                  field.onChange(value);
                                  if (value !== null && !form.getValues('BuyItNowPrice.CurrencyID')) {
                                    form.setValue('BuyItNowPrice.CurrencyID', 'USD');
                                  }
                                  if (value !== null && listingType === 'Chinese') {
                                    form.setValue('BestOfferEnabled', false);
                                  }
                                }}
                                currency="USD"
                                showCurrency={true}
                                placeholder="0.00"
                              />
                            </FormControl>
                            {listingType === 'Chinese' && form.watch('BestOfferEnabled') === true && (
                              <p className="text-xs text-destructive">⚠️ Cannot combine with Best Offer on auctions</p>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                </div>
              </div>

              {/* Options & Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Options */}
                <div>
                  <h3 className="font-medium mb-2">Options</h3>
                  <FormField
                    control={form.control}
                    name="BestOfferEnabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value === true}
                            onCheckedChange={(checked) => {
                              field.onChange(checked === true ? true : false);
                              if (checked === true && listingType === 'Chinese') {
                                form.setValue('BuyItNowPrice.Value', null);
                                form.setValue('BuyItNowPrice.CurrencyID', undefined);
                              }
                            }}
                          />
                        </FormControl>
                        <div className="leading-none">
                          <FormLabel className="text-sm font-medium">
                            Accept Best Offers
                          </FormLabel>
                          {listingType === 'Chinese' && !!form.watch('BuyItNowPrice.Value') && (
                            <p className="text-xs text-destructive">⚠️ Cannot combine with Buy It Now on auctions</p>
                          )}
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Details */}
                <div>
                  <h3 className="font-medium mb-2">Details</h3>
                  <div className="grid grid-cols-2 gap-3">
                    <FormField
                      control={form.control}
                      name="Quantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm">Quantity</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              step="1"
                              placeholder="1"
                              {...field}
                              value={field.value ?? ''}
                              disabled={listingType === 'Chinese'}
                              onChange={(e) => {
                                const value = e.target.value ? parseInt(e.target.value, 10) : null;
                                field.onChange(value);
                              }}
                              className="w-20"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="ListingDuration"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm">Duration</FormLabel>
                          <FormControl>
                            <Select onValueChange={field.onChange} value={field.value ?? undefined}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select" />
                              </SelectTrigger>
                              <SelectContent>
                                {listingType === 'FixedPriceItem' ? (
                                  <>
                                    <SelectItem value="GTC">Good &apos;Til Cancelled</SelectItem>
                                    <SelectItem value="Days_30">30 Days</SelectItem>
                                  </>
                                ) : (
                                  <>
                                    <SelectItem value="Days_1">1 Day</SelectItem>
                                    <SelectItem value="Days_3">3 Days</SelectItem>
                                    <SelectItem value="Days_5">5 Days</SelectItem>
                                    <SelectItem value="Days_7">7 Days</SelectItem>
                                    <SelectItem value="Days_10">10 Days</SelectItem>
                                  </>
                                )}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
              
              {/* Hidden field for ListingType */}
              <input type="hidden" name="ListingType" value={listingType} />
            </TabsContent>

            {/* Location Tab: Location, Postal Code, Country */}
            <TabsContent value="location" className="mt-4 space-y-4">
              <FormField
                control={form.control}
                name="Location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Item location (city, state)"
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="PostalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Postal Code</FormLabel>
                    <FormControl>
                      <Input placeholder="Zip/Postal code" {...field} value={field.value ?? ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="Country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Country code (e.g., US)"
                        maxLength={2}
                        {...field}
                        value={field.value ?? ''}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </TabsContent>

            {/* Aspects Tab: Item Specifics */}
            <TabsContent value="aspects" className="mt-4 space-y-4">
              {Boolean(watchedCategoryId) && (
                <ItemAspectsProvider>
                  <div className="border rounded-lg p-3">
                    <ItemAspects
                      categoryId={watchedCategoryId || ''}
                      marketplaceId={marketplace}
                      value={aspectValuesForUI}
                      onChange={handleAspectsChange}
                      compact={true}
                      isCatalogProductAssociated={!!selectedProduct}
                      associatedEpid={selectedProduct?.epid}
                    />
                  </div>
                </ItemAspectsProvider>
              )}
              {isLoadingMetadata && (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Loading item specifics...
                </div>
              )}
              {!isLoadingMetadata && errorMetadata && (
                <div className="p-4 text-center text-sm text-destructive">
                  Error loading specifics: {errorMetadata}
                </div>
              )}
              {!isLoadingMetadata && !aspectMetadata && watchedCategoryId && (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  No specifics found for this category.
                </div>
              )}
            </TabsContent>


            {/* Shipping Tab: Handling Time */}
            <TabsContent value="shipping" className="mt-4 space-y-4">
              <FormField
                control={form.control}
                name="DispatchTimeMax"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Handling Time (days)</FormLabel>
                    <div className="space-y-3">
                      {/* Quick Select Buttons */}
                      <div className="flex flex-wrap gap-2">
                        <Button
                          type="button"
                          variant={field.value === 0 ? "default" : "outline"}
                          size="sm"
                          onClick={() => field.onChange(0)}
                          className="text-xs"
                        >
                          Same Day ⭐
                        </Button>
                        <Button
                          type="button"
                          variant={field.value === 1 ? "default" : "outline"}
                          size="sm"
                          onClick={() => field.onChange(1)}
                          className="text-xs"
                        >
                          1 Day ⭐
                        </Button>
                        <Button
                          type="button"
                          variant={field.value === 2 ? "default" : "outline"}
                          size="sm"
                          onClick={() => field.onChange(2)}
                          className="text-xs"
                        >
                          2 Days ⭐
                        </Button>
                        <Button
                          type="button"
                          variant={field.value === 3 ? "default" : "outline"}
                          size="sm"
                          onClick={() => field.onChange(3)}
                          className="text-xs"
                        >
                          3 Days
                        </Button>
                      </div>
                      
                      {/* Custom Input for Other Values */}
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          max="40"
                          step="1"
                          placeholder="Custom days (1-40)"
                          {...field}
                          value={field.value ?? ''}
                          onChange={(e) => {
                            const rawValue = e.target.value;
                            const numValue = parseInt(rawValue, 10);
                            // If rawValue is empty string, treat as null.
                            // If parseInt results in NaN, also treat as null.
                            // Otherwise, use the parsed numeric value.
                            if (rawValue === '') {
                              field.onChange(null);
                            } else if (isNaN(numValue)) {
                              field.onChange(null);
                            } else {
                              field.onChange(numValue);
                            }
                          }}
                          className="w-48"
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                    <div className="space-y-1 text-xs text-muted-foreground">
                      <p>
                        ⭐ 1-2 days recommended for Top Rated status and better visibility
                      </p>
                      <p>
                        Number of business days to ship after receiving payment (max 40 days)
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </TabsContent>

            {/* Policies Tab Content - Conditionally Rendered */}
            {(isLoadingOptInStatus || (!isLoadingOptInStatus && isEbayAccountOptedIn)) && (
              <ListingPoliciesTab
                control={form.control}
                isEbayAccountOptedIn={isEbayAccountOptedIn}
                isLoadingOptInStatus={isLoadingOptInStatus}
              />
            )}

            {/* Manual Fulfillment Setup Tab Content - Conditionally Rendered */}
            {!isLoadingOptInStatus && !isEbayAccountOptedIn && (
              <ManualFulfillmentTab control={form.control} />
            )}
          </Tabs>
        </CardContent>

      </form>
    </Card>
  );
}
