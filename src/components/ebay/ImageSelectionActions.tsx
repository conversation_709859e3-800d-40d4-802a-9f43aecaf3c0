'use client';

import React from 'react';
import { Id } from '@convex/_generated/dataModel';
import { Package, Trash2, ScanText, Eye, Edit, Wand2, Link, Archive } from 'lucide-react';

export interface ImageSelectionActionsProps {
    selectedImageIds: Id<'images'>[];
    isCreatingSku?: boolean;
    activeSkuId?: Id<'skus'> | null;
    activeSkuName?: string; // Optional SKU name for better UX

    // Action handlers
    onCreateSku?: () => void;
    onAssociate?: () => void;
    onDelete?: () => void;
    onArchive?: () => void;
    onEdit?: (imageId: Id<'images'>) => void;
    onAdvancedEdit?: (imageId: Id<'images'>) => void;
    onRunImageRecognition?: (imageIds: Id<'images'>[]) => void;
    onPreviewSelection?: () => void;
    onClearSelection?: () => void;
    onTriggerSkuSelection?: () => void;
}

export interface ActionItem {
    id: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
    onClick: () => void;
    disabled?: boolean;
    variant?: 'default' | 'destructive';
    separator?: boolean;
}

export function useImageSelectionActions({
    selectedImageIds,
    isCreatingSku = false,
    activeSkuId,
    activeSkuName,
    onCreateSku,
    onAssociate,
    onDelete,
    onArchive,
    onEdit,
    onAdvancedEdit,
    onRunImageRecognition,
    onPreviewSelection,
    onClearSelection,
    onTriggerSkuSelection,
}: ImageSelectionActionsProps): ActionItem[] {
    const selectedCount = selectedImageIds.length;
    const hasSelection = selectedCount > 0;
    const singleSelection = selectedCount === 1;

    if (!hasSelection || isCreatingSku) {
        return [
            {
                id: 'no-action',
                label: isCreatingSku ? 'Creating SKU...' : 'Select images first',
                icon: Package,
                onClick: () => { },
                disabled: true,
            }
        ];
    }

    const actions: ActionItem[] = [];

    // Create SKU action
    if (onCreateSku) {
        actions.push({
            id: 'create-sku',
            label: `Create SKU from ${selectedCount} image${selectedCount === 1 ? '' : 's'}`,
            icon: Package,
            onClick: onCreateSku,
            disabled: isCreatingSku,
        });
    }

    // Associate with existing SKU action
    if (onAssociate && activeSkuId) {
        const skuDisplayName = activeSkuName || 'current SKU';
        actions.push({
            id: 'associate',
            label: `Associate with ${skuDisplayName}`,
            icon: Link,
            onClick: onAssociate,
            disabled: isCreatingSku,
        });
    }

    // SKU selection trigger
    if (onTriggerSkuSelection && !activeSkuId) {
        actions.push({
            id: 'select-sku',
            label: 'Select SKU to associate',
            icon: Link,
            onClick: onTriggerSkuSelection,
            disabled: isCreatingSku,
        });
    }

    // Separator before secondary actions
    if (actions.length > 0) {
        actions.push({
            id: 'separator-1',
            label: '',
            icon: Package,
            onClick: () => { },
            separator: true,
        });
    }

    // Preview action
    if (onPreviewSelection) {
        actions.push({
            id: 'preview',
            label: `Preview ${selectedCount} image${selectedCount === 1 ? '' : 's'}`,
            icon: Eye,
            onClick: onPreviewSelection,
            disabled: isCreatingSku,
        });
    }

    // Edit metadata (single image only)
    if (onEdit && singleSelection) {
        actions.push({
            id: 'edit-metadata',
            label: 'Edit metadata',
            icon: Edit,
            onClick: () => onEdit(selectedImageIds[0]!), // Non-null assertion safe because singleSelection is true
            disabled: isCreatingSku,
        });
    }

    // Advanced edit (single image only)
    if (onAdvancedEdit && singleSelection) {
        actions.push({
            id: 'advanced-edit',
            label: 'Photo Editor',
            icon: Wand2,
            onClick: () => onAdvancedEdit(selectedImageIds[0]!), // Non-null assertion safe because singleSelection is true
            disabled: isCreatingSku,
        });
    }

    // AI Analysis (always force re-run when user initiates)
    if (onRunImageRecognition) {
        actions.push({
            id: 'ai-analysis',
            label: 'Run AI Analysis',
            icon: ScanText,
            onClick: () => onRunImageRecognition(selectedImageIds),
            disabled: isCreatingSku,
        });
    }

    // Separator before destructive actions
    actions.push({
        id: 'separator-2',
        label: '',
        icon: Package,
        onClick: () => { },
        separator: true,
    });

    // Archive action
    if (onArchive) {
        actions.push({
            id: 'archive',
            label: 'Archive Selected',
            icon: Archive,
            onClick: onArchive,
            disabled: isCreatingSku,
        });
    }

    // Delete action
    if (onDelete) {
        actions.push({
            id: 'delete',
            label: 'Delete Selected',
            icon: Trash2,
            onClick: onDelete,
            disabled: isCreatingSku,
            variant: 'destructive',
        });
    }

    // Clear selection
    if (onClearSelection) {
        actions.push({
            id: 'separator-3',
            label: '',
            icon: Package,
            onClick: () => { },
            separator: true,
        });

        actions.push({
            id: 'clear',
            label: 'Clear Selection',
            icon: Package,
            onClick: onClearSelection,
            disabled: isCreatingSku,
        });
    }

    return actions;
} 