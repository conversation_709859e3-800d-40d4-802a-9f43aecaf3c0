import { WorkflowManager, vWorkflowId, type WorkflowId } from "@convex-dev/workflow";
import { v } from "convex/values";
import { query, internalMutation, mutation } from "./_generated/server";
import { internal, components } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { marked } from "marked";
import { getCurrentUser } from "./auth";
import { getCurrentEbayUserId } from "./utils";

// Simple logger for Convex
const log = {
    info: (message: string, data?: unknown) => console.log(`[BULK-AI-INFO] ${message}`, data),
    error: (message: string, data?: unknown) => console.error(`[BULK-AI-ERROR] ${message}`, data),
    warn: (message: string, data?: unknown) => console.warn(`[BULK-AI-WARN] ${message}`, data),
};

// Initialize the workflow manager
const workflow = new WorkflowManager(components.workflow);

/**
 * Convex Workflow for bulk AI enhancement of SKU descriptions
 * Processes multiple SKUs sequentially to avoid AI API rate limits
 */
export const bulkAiEnhancementWorkflow = workflow.define({
    args: {
        skuIds: v.array(v.id("skus")),
        ebayUserId: v.string(),
        userId: v.string(),
        enhancementPrompt: v.optional(v.string()),
    },
    handler: async (step, args): Promise<{
        success: boolean;
        processedCount: number;
        successCount: number;
        failureCount: number;
        results: Array<{
            skuId: Id<"skus">;
            success: boolean;
            error?: string;
        }>;
    }> => {
        log.info("Starting bulk AI enhancement workflow", {
            skuCount: args.skuIds.length,
            ebayUserId: args.ebayUserId,
        });

        const results: Array<{
            skuId: Id<"skus">;
            success: boolean;
            error?: string;
        }> = [];

        let successCount = 0;
        let failureCount = 0;

        // Step 1: Initialize workflow tracking
        await step.runMutation(internal.bulkAiEnhancementWorkflow.initializeBulkProcessingAction, {
            skuIds: args.skuIds,
            ebayUserId: args.ebayUserId,
            userId: args.userId,
        });

        // Step 2: Process each SKU sequentially
        for (let i = 0; i < args.skuIds.length; i++) {
            const skuId = args.skuIds[i];
            if (!skuId) {
                log.error(`SKU at index ${i} is undefined, skipping`);
                continue;
            }

            log.info(`Processing SKU ${i + 1}/${args.skuIds.length}`, { skuId });

            // Update progress
            await step.runMutation(internal.bulkAiEnhancementWorkflow.updateProgressAction, {
                currentIndex: i,
                totalCount: args.skuIds.length,
                currentSkuId: skuId,
                ebayUserId: args.ebayUserId,
                userId: args.userId,
            });

            try {
                // Process individual SKU with AI
                const aiResult = await step.runAction(internal.skuAiProcessing.processSkuAiComplete, {
                    skuId: skuId,
                    enhancementPrompt: args.enhancementPrompt,
                });

                if (aiResult.success) {
                    // Wait for AI processing to be fully committed to database using polling
                    let pollAttempts = 0;
                    // Adaptive polling identical to single-SKU workflow
                    const maxPollAttempts = 34; // ≈ 70 s worst-case

                    const getPollingDelay = (attempt: number): number => {
                        if (attempt === 0) return 10_000;
                        if (attempt <= 15) return 1_000;
                        if (attempt <= 25) return 2_000;
                        return 5_000;
                    };

                    while (pollAttempts < maxPollAttempts) {
                        const delay = getPollingDelay(pollAttempts);
                        const aiCommitResult = await step.runMutation(
                            internal.bulkAiEnhancementWorkflow.checkAiProcessingCompleteAction,
                            {
                                skuId: skuId,
                                ebayUserId: args.ebayUserId,
                                userId: args.userId,
                            },
                            { runAfter: delay }
                        );

                        if (aiCommitResult.ready) {
                            // AI processing is committed, now sync to drafts
                            await step.runMutation(internal.bulkAiEnhancementWorkflow.syncSkuToDraftsAction, {
                                skuId: skuId,
                                ebayUserId: args.ebayUserId,
                                userId: args.userId,
                            });
                            break; // Success! Continue to next SKU
                        }

                        pollAttempts++;
                        if (pollAttempts >= maxPollAttempts) {
                            log.warn(`AI processing commit timeout after ~70 seconds for SKU ${i + 1}, proceeding without draft sync`, { skuId });
                            break; // Don't fail the whole workflow, just skip draft sync for this SKU
                        }
                    }

                    successCount++;
                    results.push({ skuId, success: true });
                    log.info(`Successfully processed SKU ${i + 1} and synced to drafts`, { skuId });
                } else {
                    failureCount++;
                    const error = aiResult.error || "AI processing failed";
                    results.push({ skuId, success: false, error });
                    log.error(`Failed to process SKU ${i + 1}`, { skuId, error });
                }
            } catch (error) {
                failureCount++;
                const errorMessage = error instanceof Error ? error.message : String(error);
                results.push({ skuId, success: false, error: errorMessage });
                log.error(`Exception processing SKU ${i + 1}`, { skuId, error: errorMessage });
            }

            // Small delay between SKUs to be gentle on AI APIs
            if (i < args.skuIds.length - 1) {
                await step.runMutation(internal.bulkAiEnhancementWorkflow.delayAction, {}, {
                    runAfter: 1000 // 1 second delay
                });
            }
        }

        // Step 3: Complete workflow
        const completionResult = await step.runMutation(internal.bulkAiEnhancementWorkflow.completeBulkProcessingAction, {
            ebayUserId: args.ebayUserId,
            userId: args.userId,
            successCount,
            failureCount,
            results,
        });

        log.info("Bulk AI enhancement workflow completed", {
            processedCount: args.skuIds.length,
            successCount,
            failureCount,
        });

        return {
            success: completionResult.success,
            processedCount: args.skuIds.length,
            successCount,
            failureCount,
            results,
        };
    },
    returns: v.object({
        success: v.boolean(),
        processedCount: v.number(),
        successCount: v.number(),
        failureCount: v.number(),
        results: v.array(v.object({
            skuId: v.id("skus"),
            success: v.boolean(),
            error: v.optional(v.string()),
        })),
    }),
});

/**
 * Initialize bulk processing - create activity events for tracking
 */
export const initializeBulkProcessingAction = internalMutation({
    args: {
        skuIds: v.array(v.id("skus")),
        ebayUserId: v.string(),
        userId: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            // Log bulk processing start event for each SKU
            for (const skuId of args.skuIds) {
                await ctx.runMutation(internal["internal/skuActivityEvents"].createActivityEventInternal, {
                    skuId: skuId,
                    type: "bulk_ai_processing_started",
                    eventData: {
                        totalSkus: args.skuIds.length,
                        startedAt: Date.now(),
                        source: "bulk_ai_enhancement_workflow",
                    }
                });
            }

            log.info("Initialized bulk AI processing", {
                skuCount: args.skuIds.length,
                ebayUserId: args.ebayUserId,
            });

            return { success: true };
        } catch (error) {
            log.error("Failed to initialize bulk processing", { error });
            return { success: false, error: String(error) };
        }
    },
});

/**
 * Update progress during bulk processing
 */
export const updateProgressAction = internalMutation({
    args: {
        currentIndex: v.number(),
        totalCount: v.number(),
        currentSkuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            // Log progress update
            await ctx.runMutation(internal["internal/skuActivityEvents"].createActivityEventInternal, {
                skuId: args.currentSkuId,
                type: "bulk_ai_processing_progress",
                eventData: {
                    currentIndex: args.currentIndex,
                    totalCount: args.totalCount,
                    progressPercentage: Math.round((args.currentIndex / args.totalCount) * 100),
                    updatedAt: Date.now(),
                    source: "bulk_ai_enhancement_workflow",
                }
            });

            return { success: true };
        } catch (error) {
            log.error("Failed to update progress", { error });
            return { success: false };
        }
    },
});

/**
 * Check if AI processing is fully committed to the database
 * This verifies that the SKU record has been updated with AI-enhanced content
 */
export const checkAiProcessingCompleteAction = internalMutation({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            // Get the current SKU record
            const sku = await ctx.db.get(args.skuId);
            if (!sku) {
                log.error("SKU not found for AI processing check", { skuId: args.skuId });
                return { ready: false, error: "SKU not found" };
            }

            // Check if the SKU has been updated with AI-enhanced content
            // Look for signs that AI processing has completed:
            // 1. suggestedAiTitle exists 
            // 2. suggestedAiDescription exists and has substantial content
            const hasAiTitle = sku.suggestedAiTitle && sku.suggestedAiTitle.trim().length > 0;
            const hasEnhancedDescription = sku.suggestedAiDescription &&
                sku.suggestedAiDescription.trim().length > 200; // Full description should be substantial

            const isReady = hasAiTitle && hasEnhancedDescription;

            if (isReady) {
                log.info("AI processing confirmed complete for SKU", {
                    skuId: args.skuId,
                    titleLength: sku.suggestedAiTitle?.length,
                    descriptionLength: sku.whatIsIt?.length
                });
            }

            return { ready: isReady };
        } catch (error) {
            log.error("Failed to check AI processing completion", { error, skuId: args.skuId });
            return { ready: false, error: String(error) };
        }
    },
});

/**
 * Sync enhanced SKU description to any existing drafts for this SKU
 */
export const syncSkuToDraftsAction = internalMutation({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            // Get the enhanced SKU
            const sku = await ctx.db.get(args.skuId);
            if (!sku) {
                log.error("SKU not found for draft sync", { skuId: args.skuId });
                return { success: false, error: "SKU not found" };
            }

            // Find any existing drafts for this SKU and eBay user
            const existingDrafts = await ctx.db
                .query("userListings")
                .withIndex("by_sku_id_and_ebay_user_id", q =>
                    q.eq("skuId", args.skuId)
                        .eq("ebayUserId", args.ebayUserId)
                )
                .collect();

            let updatedCount = 0;
            for (const draft of existingDrafts) {
                // Only sync if AI enhancement actually succeeded
                if (sku.suggestedAiDescription) {
                    // Update draft description with enhanced SKU description (convert markdown to HTML)
                    const description = marked.parse(sku.suggestedAiDescription) as string;

                    await ctx.db.patch(draft._id, {
                        ebayDraft: {
                            ...draft.ebayDraft,
                            Description: description,
                        },
                        lastModifiedLocally: Date.now(),
                    });
                    updatedCount++;

                    log.info("Synced enhanced SKU description to draft", {
                        skuId: args.skuId,
                        draftUuid: draft.uuid,
                        descriptionLength: description.length,
                        isMarkdownConverted: true
                    });
                } else {
                    log.warn("Skipping draft sync - no AI description available", {
                        skuId: args.skuId,
                        draftUuid: draft.uuid,
                        reason: "AI enhancement failed or incomplete"
                    });
                }
            }

            log.info("Draft sync completed", {
                skuId: args.skuId,
                draftsUpdated: updatedCount
            });

            return { success: true, draftsUpdated: updatedCount };
        } catch (error) {
            log.error("Failed to sync SKU to drafts", { error, skuId: args.skuId });
            return { success: false, error: String(error) };
        }
    },
});

/**
 * Simple delay action for workflow pacing
 */
export const delayAction = internalMutation({
    args: {},
    handler: async () => {
        // This is just a no-op mutation that allows runAfter timing
        return { success: true };
    },
});

/**
 * Complete bulk processing - log final results
 */
export const completeBulkProcessingAction = internalMutation({
    args: {
        ebayUserId: v.string(),
        userId: v.string(),
        successCount: v.number(),
        failureCount: v.number(),
        results: v.array(v.object({
            skuId: v.id("skus"),
            success: v.boolean(),
            error: v.optional(v.string()),
        })),
    },
    handler: async (ctx, args) => {
        try {
            // Log completion event for each SKU
            for (const result of args.results) {
                await ctx.runMutation(internal["internal/skuActivityEvents"].createActivityEventInternal, {
                    skuId: result.skuId,
                    type: "bulk_ai_processing_completed",
                    eventData: {
                        success: result.success,
                        error: result.error,
                        completedAt: Date.now(),
                        source: "bulk_ai_enhancement_workflow",
                        finalStats: {
                            successCount: args.successCount,
                            failureCount: args.failureCount,
                            totalProcessed: args.successCount + args.failureCount,
                        }
                    }
                });
            }

            log.info("Completed bulk AI processing", {
                successCount: args.successCount,
                failureCount: args.failureCount,
            });

            return { success: true };
        } catch (error) {
            log.error("Failed to complete bulk processing", { error });
            return { success: false, error: String(error) };
        }
    },
});

/**
 * Start the bulk AI enhancement workflow (internal)
 */
export const startBulkAiEnhancementWorkflow = internalMutation({
    args: {
        skuIds: v.array(v.id("skus")),
        ebayUserId: v.string(),
        userId: v.string(),
        enhancementPrompt: v.optional(v.string()),
    },
    returns: vWorkflowId,
    handler: async (ctx, args): Promise<WorkflowId> => {
        try {
            log.info("Starting bulk AI enhancement workflow", {
                skuCount: args.skuIds.length,
                ebayUserId: args.ebayUserId,
            });

            // Start the workflow
            const workflowId = await workflow.start(
                ctx,
                internal.bulkAiEnhancementWorkflow.bulkAiEnhancementWorkflow,
                {
                    skuIds: args.skuIds,
                    ebayUserId: args.ebayUserId,
                    userId: args.userId,
                    enhancementPrompt: args.enhancementPrompt,
                },
                {
                    onComplete: internal.bulkAiEnhancementWorkflow.workflowCompleted,
                    context: { userId: args.userId, ebayUserId: args.ebayUserId },
                }
            );

            // Insert workflow execution record for reactive UI tracking
            await ctx.db.insert("workflowExecutions", {
                workflowId: workflowId,
                userId: args.userId,
                ebayUserId: args.ebayUserId,
                type: "bulkAiEnhancementWorkflow",
                input: args,
                status: "running",
                createdAt: Date.now(),
            });

            log.info("Bulk AI enhancement workflow started", {
                skuCount: args.skuIds.length,
                workflowId
            });

            return workflowId;
        } catch (error) {
            log.error("Failed to start bulk AI workflow", { error });
            throw error;
        }
    },
});

/**
 * Workflow completion handler - updates UI tracking table reactively
 */
export const workflowCompleted = internalMutation({
    args: {
        workflowId: vWorkflowId,
        result: v.any(),
        context: v.any(),
    },
    handler: async (ctx, args) => {
        try {
            // Find the workflow execution record
            const execution = await ctx.db
                .query("workflowExecutions")
                .withIndex("by_workflow_id", q => q.eq("workflowId", args.workflowId))
                .first();

            if (!execution) {
                log.error("Bulk AI workflow execution record not found", { workflowId: args.workflowId });
                return;
            }

            // Update the execution record with results
            await ctx.db.patch(execution._id, {
                status: args.result.kind === "success" ? "completed" : "failed",
                result: args.result.kind === "success" ? args.result.returnValue : undefined,
                error: args.result.kind === "failed" ? args.result.error : undefined,
                completedAt: Date.now(),
            });

            log.info("Bulk AI workflow execution updated", {
                workflowId: args.workflowId,
                status: args.result.kind,
            });

            // Clean up the workflow
            await workflow.cleanup(ctx, args.workflowId);
        } catch (error) {
            log.error("Failed to handle bulk AI workflow completion", { error, workflowId: args.workflowId });
        }
    },
});

/**
 * Start bulk AI enhancement workflow (public)
 * Security: Uses OAuth validation to ensure user has permission for eBay account
 */
export const startBulkAiEnhancementWorkflowPublic = mutation({
    args: {
        skuIds: v.array(v.id("skus")),
        enhancementPrompt: v.optional(v.string()),
    },
    returns: vWorkflowId,
    handler: async (ctx, args): Promise<WorkflowId> => {
        // ✅ SECURITY: Validate user authentication and eBay OAuth using existing system
        const user = await getCurrentUser(ctx);
        if (!user?.userId) {
            throw new Error("Authentication required");
        }

        const ebayUserId = await getCurrentEbayUserId(ctx, user.userId);
        if (!ebayUserId) {
            throw new Error("No active eBay account found. Please select an eBay account first.");
        }

        // ✅ SECURITY: Validate user owns all the SKUs they're trying to process
        for (const skuId of args.skuIds) {
            const sku = await ctx.db.get(skuId);
            if (!sku) {
                throw new Error(`SKU ${skuId} not found`);
            }
            if (sku.ebayUserId !== ebayUserId) {
                throw new Error(`Access denied: SKU ${skuId} belongs to different eBay account`);
            }
        }

        // ✅ Use OAuth-validated user context instead of trusting client
        const secureArgs = {
            skuIds: args.skuIds,
            ebayUserId: ebayUserId,          // ✅ From OAuth/eBay context, not client
            userId: user.userId,             // ✅ From auth, not client
            enhancementPrompt: args.enhancementPrompt,
        };

        // Start the new workflow with OAuth-validated parameters
        const workflowId = await ctx.runMutation(internal.bulkAiEnhancementWorkflow.startBulkAiEnhancementWorkflow, secureArgs);

        return workflowId;
    },
});

/**
 * Get bulk AI workflow execution status reactively
 */
export const getBulkAiWorkflowExecution = query({
    args: { workflowId: vWorkflowId },
    handler: async (ctx, args) => {
        return await ctx.db
            .query("workflowExecutions")
            .withIndex("by_workflow_id", q => q.eq("workflowId", args.workflowId))
            .first();
    },
});

/**
 * Get user's active bulk AI enhancement workflows
 */
export const getUserActiveBulkAiWorkflows = query({
    args: {},
    handler: async (ctx) => {
        const user = await getCurrentUser(ctx);
        if (!user?.userId) {
            return [];
        }

        const ebayUserId = await getCurrentEbayUserId(ctx, user.userId);
        if (!ebayUserId) {
            return [];
        }

        return await ctx.db
            .query("workflowExecutions")
            .withIndex("by_user_and_type", q => q.eq("userId", user.userId).eq("type", "bulkAiEnhancementWorkflow"))
            .filter(q => q.eq(q.field("status"), "running"))
            .collect();
    },
});