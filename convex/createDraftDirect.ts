import { internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";
import { api } from "./_generated/api";
import { generateDraftUuid } from "./utils/uuid";
import { marked } from "marked";
import { applyFoundationDefaults } from "./utils/foundationUtils";
import type { ItemType } from "@/generated/trading/trading";

/**
 * Direct draft creation that bypasses authentication entirely
 * This is for testing the quick listing approval flow
 */
export const createDraftDirectly = internalMutation({
    args: {
        skuId: v.id("skus"),
        userId: v.string(),
        ebayUserId: v.string(),
    },
    returns: v.object({
        success: v.boolean(),
        message: v.string(),
        draftUuid: v.optional(v.string()),
        draftId: v.optional(v.id("userListings"))
    }),
    handler: async (ctx, args) => {
        try {
            console.log("Creating draft directly for SKU:", args.skuId);
            
            // Get the SKU
            const sku = await ctx.db.get(args.skuId);
            if (!sku) {
                throw new ConvexError("SKU not found");
            }
            
            // Check if draft already exists
            const existingDrafts = await ctx.db
                .query("userListings")
                .withIndex("by_sku_id", q => q.eq("skuId", args.skuId))
                .collect();
            
            if (existingDrafts.length > 0) {
                const firstDraft = existingDrafts[0];
                console.log("Draft already exists:", firstDraft?.uuid);
                return {
                    success: true,
                    message: "Draft already exists",
                    draftUuid: firstDraft?.uuid,
                    draftId: firstDraft?._id
                };
            }
            
            // Generate UUID for the new draft
            const draftUuid = generateDraftUuid();
            
            // Get images for this SKU
            const images = await ctx.db
                .query("images")
                .filter(q => q.eq(q.field("skuId"), args.skuId))
                .collect();
            
            const imageIds = images.map(img => img._id);
            
            // Build the base draft payload
            const baseDraft = {
                Title: sku.suggestedAiTitle || sku.whatIsIt || "Quick Listing",
                Description: sku.suggestedAiDescription
                    ? (marked.parse(sku.suggestedAiDescription) as string)
                    : "",
                SKU: sku.fullSku,
                // Intentionally leave ConditionID unset – user must confirm.
            };

            // Get the active foundation blueprint ID for this user
            const activeFoundation = await ctx.db
                .query("blueprints")
                .withIndex("by_ebay_user_and_type", q =>
                    q.eq("ebayUserId", args.ebayUserId)
                        .eq("type", "foundation")
                )
                .filter(q => q.eq(q.field("archivedAt"), undefined))
                .first();

            // Get workspace context for foundation selection
            const workspaceContext = await ctx.db
                .query("userWorkspaceContext")
                .withIndex("by_user_and_ebay_account", q =>
                    q.eq("userId", args.userId)
                        .eq("ebayUserId", args.ebayUserId)
                )
                .first();

            // Apply foundation defaults (creates default foundation if none exists)
            const foundationResult = await applyFoundationDefaults(
                ctx, 
                args.ebayUserId, 
                baseDraft, 
                { 
                    skuId: args.skuId, 
                    uuid: draftUuid,
                    userId: args.userId,
                    foundationBlueprintId: workspaceContext?.currentFoundationBlueprintId
                }
            );
            
            const ebayDraft = foundationResult.enhancedEbayDraft as Partial<ItemType>;

            // If a new foundation was created and user had no foundation selected, set it in their context
            if (foundationResult.wasNewFoundationCreated && foundationResult.foundationId && !workspaceContext?.currentFoundationBlueprintId) {
                try {
                    await ctx.runMutation(api.userWorkspaceContext.setCurrentFoundationBlueprint, {
                        userId: args.userId,
                        ebayUserId: args.ebayUserId,
                        foundationBlueprintId: foundationResult.foundationId
                    });
                    console.info('Auto-set newly created foundation in workspace context', {
                        foundationId: foundationResult.foundationId,
                        userId: args.userId
                    });
                } catch (contextError) {
                    console.warn('Failed to set foundation in workspace context (non-blocking)', {
                        error: contextError,
                        foundationId: foundationResult.foundationId
                    });
                }
            }

            // Get foundation ID (use the one from applyFoundationDefaults or fallback)
            const foundationBlueprintId = foundationResult.foundationId || activeFoundation?._id;
            
            // Create the draft directly in the database
            const draftId = await ctx.db.insert("userListings", {
                uuid: draftUuid,
                ebayUserId: args.ebayUserId,
                skuId: args.skuId,
                marketplaceId: "EBAY_US",
                foundationBlueprintId,
                ebayDraft,
                imageStatus: {
                    imageIds: imageIds,
                    ebayPictureUrls: []
                },
                lastModifiedLocally: Date.now(),
                updatedAt: Date.now(),
                workflowStatus: {
                    hasValidTitle: true,
                    hasCategory: false,
                    hasCondition: false,
                    hasDescription: true,
                    hasSetShippingPolicy: false,
                    hasSetPaymentPolicy: false,
                    hasUploadedImages: imageIds.length > 0,
                    isReadyToPublish: false
                } // Set initial workflow status
            });
            
            // Sync images to PictureDetails if we have images
            if (imageIds.length > 0) {
                try {
                    await ctx.runMutation(api.drafts.syncImageIdsWithPictureDetails, {
                        draftId: draftId,
                        imageIds: imageIds
                    });
                    console.log("Successfully synced images to PictureDetails");
                } catch (syncError) {
                    console.error("Failed to sync images to PictureDetails:", syncError);
                    // Continue anyway
                }
            }
            
            // Automatically persist machine state for direct creation
            try {
                await ctx.runMutation(api.draftStatePersist.persistMachineStateOnCreation, {
                    userListingId: draftId
                });
                console.log("Machine state persisted for direct draft creation:", draftUuid);
            } catch (error) {
                console.warn("Failed to persist machine state for direct creation (non-blocking):", {
                    draftUuid,
                    error: error instanceof Error ? error.message : String(error)
                });
            }

            console.log("Draft created successfully:", {
                draftId,
                draftUuid,
                imageCount: imageIds.length
            });
            
            return {
                success: true,
                message: "Draft created successfully",
                draftUuid,
                draftId
            };
            
        } catch (error) {
            console.error("Direct draft creation failed:", error);
            return {
                success: false,
                message: error instanceof Error ? error.message : String(error)
            };
        }
    }
});