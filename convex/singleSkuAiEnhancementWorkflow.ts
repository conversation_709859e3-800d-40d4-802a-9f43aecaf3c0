import { WorkflowManager, vWorkflowId, type WorkflowId } from "@convex-dev/workflow";
import { v } from "convex/values";
import { internalMutation, mutation } from "./_generated/server";
import { internal, components } from "./_generated/api";
import type { Id } from "./_generated/dataModel";
import { marked } from "marked";
import { getCurrentUser } from "./auth";
import { getCurrentEbayUserId } from "./utils";

// Simple logger for Convex
const log = {
    info: (message: string, data?: unknown) => console.log(`[SINGLE-SKU-AI-INFO] ${message}`, data),
    error: (message: string, data?: unknown) => console.error(`[SINGLE-SKU-AI-ERROR] ${message}`, data),
    warn: (message: string, data?: unknown) => console.warn(`[SINGLE-SKU-AI-WARN] ${message}`, data),
};

// Initialize the workflow manager
const workflow = new WorkflowManager(components.workflow);

/**
 * Single SKU AI Enhancement Workflow
 * Processes one SKU for AI enhancement and syncs to drafts
 * Much simpler than bulk workflow - designed to run in parallel
 */
export const singleSkuAiEnhancementWorkflow = workflow.define({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
        enhancementPrompt: v.optional(v.string()),
    },
    handler: async (step, args): Promise<{
        success: boolean;
        skuId: Id<"skus">;
        error?: string;
        draftsUpdated?: number;
    }> => {
        log.info("Starting single SKU AI enhancement", { skuId: args.skuId });

        // Step 1: Initialize tracking
        await step.runMutation(internal.singleSkuAiEnhancementWorkflow.initializeTrackingAction, {
            skuId: args.skuId,
            ebayUserId: args.ebayUserId,
            userId: args.userId,
        });

        try {
            // Step 2: Run AI processing
            const aiResult = await step.runAction(internal.skuAiProcessing.processSkuAiComplete, {
                skuId: args.skuId,
                enhancementPrompt: args.enhancementPrompt,
            });

            if (!aiResult.success) {
                const error = aiResult.error || "AI processing failed";
                await step.runMutation(internal.singleSkuAiEnhancementWorkflow.completeTrackingAction, {
                    skuId: args.skuId,
                    ebayUserId: args.ebayUserId,
                    userId: args.userId,
                    success: false,
                    error: error,
                });

                log.error("AI processing failed", { skuId: args.skuId, error });
                return { success: false, skuId: args.skuId, error };
            }

            // Step 3: Wait for AI processing to be fully committed using optimized polling
            let pollAttempts = 0;
            // We know the AI finishes between 10-40 s in practice. This schedule keeps traffic low
            // while still detecting the common 20-30 s completion quickly.
            // attempt 0  → 10 000 ms  (first check at 10 s)
            // attempts 1-15 → 1 000 ms (10-25 s window)
            // attempts 16-25 → 2 000 ms (25-45 s window)
            // attempts 26-33 → 5 000 ms (50-70 s window)
            const maxPollAttempts = 34; // 0-33 inclusive ≈ 70 s worst-case

            const getPollingDelay = (attempt: number): number => {
                if (attempt === 0) return 10_000;     // 10s – earliest possible completion
                if (attempt <= 15) return 1_000;      // 10-25s – high-frequency window
                if (attempt <= 25) return 2_000;      // 25-45s – normal window
                return 5_000;                         // 45-70s – tail latency
            };

            while (pollAttempts < maxPollAttempts) {
                const delay = getPollingDelay(pollAttempts);
                const aiCommitResult = await step.runMutation(
                    internal.singleSkuAiEnhancementWorkflow.checkAiProcessingCompleteAction,
                    {
                        skuId: args.skuId,
                        ebayUserId: args.ebayUserId,
                        userId: args.userId,
                    },
                    { runAfter: delay }
                );

                if (aiCommitResult.ready) {
                    // Step 4: Sync to drafts
                    const syncResult = await step.runMutation(internal.singleSkuAiEnhancementWorkflow.syncSkuToDraftsAction, {
                        skuId: args.skuId,
                        ebayUserId: args.ebayUserId,
                        userId: args.userId,
                    });

                    // Step 5: Complete tracking
                    await step.runMutation(internal.singleSkuAiEnhancementWorkflow.completeTrackingAction, {
                        skuId: args.skuId,
                        ebayUserId: args.ebayUserId,
                        userId: args.userId,
                        success: true,
                        draftsUpdated: syncResult.draftsUpdated,
                    });

                    log.info("Single SKU AI enhancement completed successfully", {
                        skuId: args.skuId,
                        draftsUpdated: syncResult.draftsUpdated
                    });

                    return {
                        success: true,
                        skuId: args.skuId,
                        draftsUpdated: syncResult.draftsUpdated
                    };
                }

                pollAttempts++;
                if (pollAttempts >= maxPollAttempts) {
                    const error = "AI processing timeout after ~70 seconds";
                    await step.runMutation(internal.singleSkuAiEnhancementWorkflow.completeTrackingAction, {
                        skuId: args.skuId,
                        ebayUserId: args.ebayUserId,
                        userId: args.userId,
                        success: false,
                        error: error,
                    });

                    log.warn(error, { skuId: args.skuId });
                    return { success: false, skuId: args.skuId, error };
                }
            }

            // This should never be reached due to the while loop, but TypeScript needs it
            return { success: false, skuId: args.skuId, error: "Unexpected workflow state" };

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            await step.runMutation(internal.singleSkuAiEnhancementWorkflow.completeTrackingAction, {
                skuId: args.skuId,
                ebayUserId: args.ebayUserId,
                userId: args.userId,
                success: false,
                error: errorMessage,
            });

            log.error("Single SKU AI enhancement workflow failed", { skuId: args.skuId, error: errorMessage });
            return { success: false, skuId: args.skuId, error: errorMessage };
        }
    },
    returns: v.object({
        success: v.boolean(),
        skuId: v.id("skus"),
        error: v.optional(v.string()),
        draftsUpdated: v.optional(v.number()),
    }),
});

/**
 * Initialize tracking for single SKU AI enhancement
 */
export const initializeTrackingAction = internalMutation({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            await ctx.runMutation(internal["internal/skuActivityEvents"].createActivityEventInternal, {
                skuId: args.skuId,
                type: "single_ai_processing_started",
                eventData: {
                    startedAt: Date.now(),
                    source: "single_sku_ai_enhancement_workflow",
                }
            });

            log.info("Initialized single SKU AI processing", { skuId: args.skuId });
            return { success: true };
        } catch (error) {
            log.error("Failed to initialize single SKU AI processing", { error, skuId: args.skuId });
            return { success: false, error: String(error) };
        }
    },
});

/**
 * Check if AI processing is fully committed to the database
 */
export const checkAiProcessingCompleteAction = internalMutation({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            const sku = await ctx.db.get(args.skuId);
            if (!sku) {
                log.error("SKU not found for AI processing check", { skuId: args.skuId });
                return { ready: false, error: "SKU not found" };
            }

            // Check if the SKU has been updated with AI-enhanced content
            const hasAiTitle = sku.suggestedAiTitle && sku.suggestedAiTitle.trim().length > 0;
            const hasEnhancedDescription = sku.suggestedAiDescription &&
                sku.suggestedAiDescription.trim().length > 200;

            const isReady = hasAiTitle && hasEnhancedDescription;

            if (isReady) {
                log.info("AI processing confirmed complete for SKU", {
                    skuId: args.skuId,
                    titleLength: sku.suggestedAiTitle?.length,
                    descriptionLength: sku.suggestedAiDescription?.length
                });
            }

            return { ready: isReady };
        } catch (error) {
            log.error("Failed to check AI processing completion", { error, skuId: args.skuId });
            return { ready: false, error: String(error) };
        }
    },
});

/**
 * Sync enhanced SKU description to any existing drafts
 */
export const syncSkuToDraftsAction = internalMutation({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
    },
    handler: async (ctx, args) => {
        try {
            const sku = await ctx.db.get(args.skuId);
            if (!sku) {
                log.error("SKU not found for draft sync", { skuId: args.skuId });
                return { success: false, error: "SKU not found", draftsUpdated: 0 };
            }

            // Find any existing drafts for this SKU and eBay user
            const existingDrafts = await ctx.db
                .query("userListings")
                .withIndex("by_sku_id_and_ebay_user_id", q =>
                    q.eq("skuId", args.skuId)
                        .eq("ebayUserId", args.ebayUserId)
                )
                .collect();

            let updatedCount = 0;
            for (const draft of existingDrafts) {
                // Only sync if AI enhancement actually succeeded
                if (sku.suggestedAiDescription) {
                    const description = marked.parse(sku.suggestedAiDescription) as string;

                    await ctx.db.patch(draft._id, {
                        ebayDraft: {
                            ...draft.ebayDraft,
                            Description: description,
                        },
                        lastModifiedLocally: Date.now(),
                    });
                    updatedCount++;

                    log.info("Synced enhanced SKU description to draft", {
                        skuId: args.skuId,
                        draftUuid: draft.uuid,
                        descriptionLength: description.length,
                    });
                } else {
                    log.warn("Skipping draft sync - no AI description available", {
                        skuId: args.skuId,
                        draftUuid: draft.uuid,
                    });
                }
            }

            log.info("Draft sync completed", {
                skuId: args.skuId,
                draftsUpdated: updatedCount
            });

            return { success: true, draftsUpdated: updatedCount };
        } catch (error) {
            log.error("Failed to sync SKU to drafts", { error, skuId: args.skuId });
            return { success: false, error: String(error), draftsUpdated: 0 };
        }
    },
});

/**
 * Complete tracking for single SKU AI enhancement
 */
export const completeTrackingAction = internalMutation({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
        userId: v.string(),
        success: v.boolean(),
        error: v.optional(v.string()),
        draftsUpdated: v.optional(v.number()),
    },
    handler: async (ctx, args) => {
        try {
            await ctx.runMutation(internal["internal/skuActivityEvents"].createActivityEventInternal, {
                skuId: args.skuId,
                type: "single_ai_processing_completed",
                eventData: {
                    success: args.success,
                    error: args.error,
                    draftsUpdated: args.draftsUpdated,
                    completedAt: Date.now(),
                    source: "single_sku_ai_enhancement_workflow",
                }
            });

            log.info("Completed single SKU AI processing tracking", {
                skuId: args.skuId,
                success: args.success
            });
            return { success: true };
        } catch (error) {
            log.error("Failed to complete single SKU AI processing tracking", { error, skuId: args.skuId });
            return { success: false, error: String(error) };
        }
    },
});

/**
 * Start single SKU AI enhancement workflow (public)
 */
export const startSingleSkuAiEnhancementWorkflow = mutation({
    args: {
        skuId: v.id("skus"),
        enhancementPrompt: v.optional(v.string()),
    },
    returns: vWorkflowId,
    handler: async (ctx, args): Promise<WorkflowId> => {
        // Security: Validate user authentication and eBay OAuth
        const user = await getCurrentUser(ctx);
        if (!user?.userId) {
            throw new Error("Authentication required");
        }

        const ebayUserId = await getCurrentEbayUserId(ctx, user.userId);
        if (!ebayUserId) {
            throw new Error("No active eBay account found. Please select an eBay account first.");
        }

        // Security: Validate user owns the SKU
        const sku = await ctx.db.get(args.skuId);
        if (!sku) {
            throw new Error(`SKU ${args.skuId} not found`);
        }
        if (sku.ebayUserId !== ebayUserId) {
            throw new Error(`Access denied: SKU ${args.skuId} belongs to different eBay account`);
        }

        // Start the workflow
        const workflowId = await workflow.start(
            ctx,
            internal.singleSkuAiEnhancementWorkflow.singleSkuAiEnhancementWorkflow,
            {
                skuId: args.skuId,
                ebayUserId: ebayUserId,
                userId: user.userId,
                enhancementPrompt: args.enhancementPrompt,
            },
            {
                onComplete: internal.singleSkuAiEnhancementWorkflow.workflowCompleted,
                context: { userId: user.userId, ebayUserId: ebayUserId },
            }
        );

        // Insert workflow execution record for tracking
        await ctx.db.insert("workflowExecutions", {
            workflowId: workflowId,
            userId: user.userId,
            ebayUserId: ebayUserId,
            type: "singleSkuAiEnhancementWorkflow",
            input: { ...args, ebayUserId, userId: user.userId },
            status: "running",
            createdAt: Date.now(),
        });

        log.info("Single SKU AI enhancement workflow started", {
            skuId: args.skuId,
            workflowId
        });

        return workflowId;
    },
});

/**
 * Workflow completion handler - updates UI tracking table reactively
 */
export const workflowCompleted = internalMutation({
    args: {
        workflowId: vWorkflowId,
        result: v.any(),
        context: v.any(),
    },
    handler: async (ctx, args) => {
        try {
            // Find the workflow execution record
            const execution = await ctx.db
                .query("workflowExecutions")
                .withIndex("by_workflow_id", q => q.eq("workflowId", args.workflowId))
                .first();

            if (!execution) {
                log.error("Single SKU AI workflow execution record not found", { workflowId: args.workflowId });
                return;
            }

            // Update the execution record with results
            await ctx.db.patch(execution._id, {
                status: args.result.kind === "success" ? "completed" : "failed",
                result: args.result.kind === "success" ? args.result.returnValue : undefined,
                error: args.result.kind === "failed" ? args.result.error : undefined,
                completedAt: Date.now(),
            });

            log.info("Single SKU AI workflow execution updated", {
                workflowId: args.workflowId,
                status: args.result.kind,
            });

            // Clean up the workflow
            await workflow.cleanup(ctx, args.workflowId);
        } catch (error) {
            log.error("Failed to handle single SKU AI workflow completion", { error, workflowId: args.workflowId });
        }
    },
});