import { cronJobs } from "convex/server";
import { internal } from "./_generated/api";
import { internalAction } from "./_generated/server";
import { v } from "convex/values";

// Batch process images that don't have recognition text yet
export const batchProcessUnrecognizedImages = internalAction({
    args: {},
    returns: v.null(),
    handler: async (ctx) => {
        "use node";

        // Check if automatic recognition is enabled
        const autoRecognitionEnabled = process.env.AUTO_IMAGE_RECOGNITION_ENABLED !== 'false';
        if (!autoRecognitionEnabled) {
            console.log('Automatic image recognition is disabled, skipping batch processing');
            return null;
        }

        try {
            console.log('Starting batch processing of unrecognized images');

            // Get images without recognition text (limit to prevent overwhelming the system)
            const unrecognizedImages = await ctx.runQuery(internal.images.getUnrecognizedImages, {
                limit: 10 // Process max 10 images per batch
            });

            if (unrecognizedImages.length === 0) {
                console.log('No unrecognized images found for batch processing');
                return null;
            }

            console.log(`Found ${unrecognizedImages.length} unrecognized images for batch processing`);

            // Process each image
            for (const image of unrecognizedImages) {
                try {
                    console.log(`Batch processing image recognition for ${image._id}`);
                    await ctx.runAction(internal.images.processImageRecognition, {
                        imageId: image._id,
                        userId: image.ebayUserId,
                        forceRerun: false,
                    });

                    // Small delay between images to prevent overwhelming the AI service
                    await new Promise(resolve => setTimeout(resolve, 1000));
                } catch (imageError) {
                    console.error(`Failed to process image ${image._id} in batch`, {
                        error: imageError instanceof Error ? imageError.message : String(imageError),
                    });
                }
            }

            console.log('Batch processing of unrecognized images completed');
        } catch (error) {
            console.error('Batch processing failed', {
                error: error instanceof Error ? error.message : String(error),
            });
        }

        return null;
    },
});

const crons = cronJobs();

// DISABLED: Batch processing cron job is no longer needed since we now process
// all images automatically on upload via runAfter(0). Keeping the code available
// for potential future use (e.g., processing historical images or handling failures).
// 
// If you need to re-enable this, uncomment the line below:
// crons.interval("batch process unrecognized images", { minutes: 30 }, internal.crons.batchProcessUnrecognizedImages, {});

export default crons; 