import { internalAction } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";

// Copy system prompt from original Next.js version
const EBAY_LISTING_SYSTEM_PROMPT = `You are an expert at creating clear, concise and accurate eBay listings and providing insightful seller assistance.

Our friend the AI image recognition model has processed images of the item that the seller photographed or found. This model sees the images in isolation, the output may not be 100% accurate. The seller may be creating a new listing from scratch, or revising and enhancing an existing listing or earlier draft. If you see an existing description it is very important to maintain the seller's voice, style and any explicit commitments or disclaimers.

Your response MUST be ONLY a valid JSON object with no other text before or after. The JSON object should conform to the following structure:
{
  "suggestedTitle": "string (max 76 characters)",
  "suggestedSubtitle": "string (max 50 characters)",
  "description": "string (GitHub Flavored Markdown). This is the exact eBay description field. ",
  "valuationEstimate": { "low": number, "best": number, "high": number },
  "buyerPersona": "string (brief description of a typical buyer searching eBay for this item)",
  "potentialBuyerQuestions": ["string (list of potential questions/concerns a buyer might ask, identify key info, be insightful, a bit of fun tongue-in-cheek (buyers can be annoying \\"tire kickers\\" or \\"time wasters\\"), emojis are ok)" --but the serious goal is to help the seller preemptively avoid any disputes or returns]
}

When providing the description, use GitHub Flavored Markdown for formatting (e.g., # for headers, * or - for lists, **bold**, *italic*, tables are good for complex specifications).

CRITICAL RULES FOR CONTENT GENERATION:
1. Base your response on the provided information and information about the product you are very certain about.
2. Be factual and specific - do not make assumptions or add information not present in the input.
3. Do not make up any information about the product. 
4. Our goal as eBay sellers is to provide extremely accurate and honest descriptions of the item being sold, which is usually in USED condition and may have flaws. Our number one priority is buyer expectations are met, not marketing hype.
4. Include specific measurements, model numbers, serial numbers, and specifications found in the image recognition text.
5. You may omit certain text that may have been captured by image recognition that is of low value, such as regulatory boilerplate text that is identical on many products.
6. eBay is a contractual marketplace; you must not add any language that creates undue commitments from the seller that may be contrary to eBay policies (e.g., fully tested, sold as-is, no returns), unless these are clearly specified by the seller.
7. If the user's existing description contains business policy information, you must retain it exactly in meaning; do not modify anything except for typos, grammar, and presentation. 
10. For 'potentialBuyerQuestions': Aim to be helpful by anticipating what a buyer might ask or misunderstand. This can include common pitfalls or areas where more detail prevents returns. A slightly humorous or knowing tone is acceptable, but the core goal is to identify crucial information gaps or potential points of confusion from a buyer's perspective. These questions should help the seller improve their listing.
11. For 'valuationEstimate': Provide a realistic range (low, best, high) based on the item's perceived condition and features from the input. If uncertain, provide a wider range or indicate uncertainty if possible within the structure. The "best" value should be an eBay price that would sell quickly and slightly beat the competition.
12. For 'suggestedSubtitle': Ensure it is attention grabbing, SEO enhanced, and strictly adheres to the 51-character limit.

VOICE:
1. If you are enhancing an existing listing, you can match the seller's voice and style.
2. If you are creating a new listing, you can use a third-person typical eBay style.
3. Your description should be ready to publish, do not add meta-commentary here.
`;

// Define interfaces for activity event data (from original Next.js version)
interface CatalogSkuCreationEventData {
  epid?: string;
  title?: string;
  aspects?: Array<{ name: string; values: string[] }>;
  source?: string;
  marketplaceId?: string;
  userId?: string | null;
  recommendedCategoryId?: string;
}

interface AiInsightsRelevantEventData {
  suggestedAiTitle?: string;
  suggestedAiDescription?: string;
  suggestedSubtitle?: string;
  valuationEstimate?: string;
  buyerPersona?: string;
  potentialBuyerQuestions?: string[];
  suggestedAiAspects?: Array<{ name: string; values: string[] }>;
}

// Helper type for product details (simplified)
interface ProductDetails {
  epid?: string;
  title?: string;
  brand?: string;
  image?: { imageUrl: string };
  aspects?: Array<{
    localizedName?: string;
    localizedValues?: string[];
  }>;
  upc?: string[];
  ean?: string[];
  isbn?: string[];
  mpn?: string[];
}

// Import proper eBay Trading API types
import { type ItemType } from "@/generated/trading/trading";

/**
 * Complete SKU AI processing action with all 9 activity event types
 * Migrated from src/lib/ebay/actions/sku-actions.ts
 */
export const processSkuAiComplete = internalAction({
  args: {
    skuId: v.id("skus"),
    existingDescription: v.optional(v.string()),
    enhancementPrompt: v.optional(v.string()),
    listingContext: v.optional(
      v.object({
        suggestedTitle: v.optional(v.string()),
        conditionLabel: v.optional(v.string()),
      })
    ),
    researchNotes: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    "use node";
    console.log("processSkuAiComplete called with skuId:", args.skuId);
    try {
      console.log("Starting AI-powered listing generation/enhancement", {
        skuId: args.skuId,
        hasExistingDescription: !!args.existingDescription,
        enhancementPrompt: args.enhancementPrompt,
        listingContext: args.listingContext,
        researchNotesCount: args.researchNotes?.length,
      });

      // Step 1: Fetch SKU Document for core details (via internal query)
      const skuDoc = await ctx.runQuery(internal.skus.getSkuDocByIdInternal, { skuId: args.skuId });
      if (!skuDoc) {
        console.error("SKU document not found", { skuId: args.skuId });
        return { success: false, error: "SKU document not found" };
      }
      let skuContext = `SKU: ${skuDoc.fullSku}\\nItem Primary Description: ${skuDoc.whatIsIt}`;

      // Extract and format aspects from productDetailsSnapshot
      let catalogAspectsString = "- None provided by catalog.";
      if (skuDoc.productDetailsSnapshot) {
        const snapshot = skuDoc.productDetailsSnapshot as ProductDetails;
        if (snapshot.aspects && snapshot.aspects.length > 0) {
          const formattedAspects = snapshot.aspects
            .map((aspect) => {
              const name = aspect.localizedName || "Unknown Aspect";
              const values = aspect.localizedValues?.join(", ") || "N/A";
              return `- ${name}: ${values}`;
            })
            .join("\\n");
          if (formattedAspects) {
            catalogAspectsString = `Based on linked eBay catalog product (ePID: ${snapshot.epid}):\\n${formattedAspects}`;
          }
        }
      }
      skuContext += `\\n\\nItem Specifics from Catalog Data:\\n${catalogAspectsString}`;

      // Step 2: Fetch and Prepare Activity Log (via internal query)
      const activityLogEvents = await ctx.runQuery(internal.skus.getSkuActivityLogInternal, { skuId: args.skuId });

      // Reverse to get chronological order (oldest first)
      const chronologicalActivityLog = [...activityLogEvents].reverse();
      console.log(`Fetched ${chronologicalActivityLog.length} activity log events for SKU.`, { skuId: args.skuId });

      // Step 3: Format Activity Log Entries (ALL 9 EVENT TYPES from original)
      let activityLogSummary = "";
      if (chronologicalActivityLog.length > 0) {
        chronologicalActivityLog.forEach((event) => {
          const eventTimestamp = new Date(event.timestamp).toLocaleString();
          let eventDetails = "";
          try {
            switch (event.type) {
              case "image_recognition_result": {
                const imageData = event.eventData as { imageRecognitionText?: string };
                eventDetails = `Image Recognition Result: ${imageData?.imageRecognitionText || "N/A"}`;
                break;
              }
              case "research_note": {
                const noteData = event.eventData as { note?: string; source?: string };
                eventDetails = `Research Note: ${noteData?.note || "N/A"}`;
                if (noteData?.source) {
                  eventDetails += ` (Source: ${noteData.source})`;
                }
                break;
              }
              case "catalog_lookup": {
                const catalogData = event.eventData as ProductDetails;
                if (catalogData && catalogData.epid) {
                  let details = `eBay Catalog Product (ePID: ${catalogData.epid}):`;
                  if (catalogData.title) details += `  Title: ${catalogData.title}`;
                  if (catalogData.brand) details += `  Brand: ${catalogData.brand}`;
                  if (catalogData.image?.imageUrl) details += `  Image URL: ${catalogData.image.imageUrl}`;

                  const identifiers: string[] = [];
                  if (catalogData.upc?.length) identifiers.push(`UPC: ${catalogData.upc.join(", ")}`);
                  if (catalogData.ean?.length) identifiers.push(`EAN: ${catalogData.ean.join(", ")}`);
                  if (catalogData.isbn?.length) identifiers.push(`ISBN: ${catalogData.isbn.join(", ")}`);
                  if (catalogData.mpn?.length) identifiers.push(`MPN: ${catalogData.mpn.join(", ")}`);
                  if (identifiers.length > 0) details += `  Identifiers: ${identifiers.join("; ")}`;

                  if (catalogData.aspects && Array.isArray(catalogData.aspects) && catalogData.aspects.length > 0) {
                    details += `  Aspects (Item Specifics):`;
                    catalogData.aspects.forEach((aspect) => {
                      const aspectName = aspect.localizedName || "Unknown Aspect";
                      let aspectValue = "N/A";
                      if (aspect.localizedValues && Array.isArray(aspect.localizedValues) && aspect.localizedValues.length > 0) {
                        aspectValue = aspect.localizedValues.join(", ");
                      }
                      details += `    - ${aspectName}: ${aspectValue}`;
                    });
                  }
                  eventDetails = details;
                } else {
                  eventDetails = "eBay Catalog Product: Data or ePID missing.";
                }
                break;
              }
              case "catalog_sku_creation_and_link": {
                const catLinkData = event.eventData as CatalogSkuCreationEventData;
                eventDetails = `SKU linked to catalog product ePID: ${catLinkData?.epid || "N/A"}, Title: ${catLinkData?.title?.substring(0, 50) || "N/A"}...`;
                if (catLinkData?.recommendedCategoryId) eventDetails += `, Suggested Category: ${catLinkData.recommendedCategoryId}`;
                if (catLinkData?.aspects && catLinkData.aspects.length > 0) {
                  eventDetails += `  Initial Catalog Aspects: ${catLinkData.aspects.map((a) => `${a.name}: ${a.values.join(",")}`).slice(0, 3).join("; ")}...`;
                }
                break;
              }
              case "ai_insights_updated": {
                const insightsData = event.eventData as AiInsightsRelevantEventData;
                let insightsSummary = "AI insights were recently updated.";
                const updatedFields = Object.keys(insightsData).filter((key) => 
                  insightsData[key as keyof AiInsightsRelevantEventData] !== undefined
                );
                if (updatedFields.length > 0) {
                  insightsSummary += ` Fields potentially affected: ${updatedFields.join(", ")}.`;
                }
                if (insightsData.suggestedAiTitle) {
                  insightsSummary += ` New AI Title: "${insightsData.suggestedAiTitle.substring(0, 40)}..."`;
                }
                if (insightsData.suggestedAiAspects && insightsData.suggestedAiAspects.length > 0) {
                  insightsSummary += `  AI Suggested Aspects: ${insightsData.suggestedAiAspects.map((a) => a.name).slice(0, 3).join(", ")}...`;
                }
                eventDetails = insightsSummary;
                break;
              }
              case "ai_description": {
                const aiDescData = event.eventData as { description?: string; prompt?: string };
                eventDetails = `AI Generated Description: ${aiDescData?.description?.substring(0, 200) || "N/A"}...`;
                if (aiDescData?.prompt) {
                  eventDetails += ` (Based on a prompt)`;
                }
                break;
              }
              case "ebay_listing_revision": {
                const ebayRevisionEventData = event.eventData as { ebayData?: ItemType; itemId?: string; source?: string };
                if (ebayRevisionEventData?.ebayData) {
                  try {
                    eventDetails = `Captured eBay Listing Data (Full Object - ItemID: ${ebayRevisionEventData.itemId || "N/A"}): \`\`\`json\n${JSON.stringify(ebayRevisionEventData.ebayData, null, 2)}\n\`\`\``;
                  } catch (stringifyError) {
                    const errorMsg = stringifyError instanceof Error ? stringifyError.message : "Unknown stringify error";
                    console.error("Error stringifying ebayData for AI prompt", {
                      skuId: args.skuId,
                      itemId: ebayRevisionEventData.itemId,
                      error: errorMsg,
                    });
                    eventDetails = `Captured eBay Listing Data (ItemID: ${ebayRevisionEventData.itemId || "N/A"}): [Error stringifying full data - see logs]`;
                  }
                } else {
                  eventDetails = `Captured eBay Listing Data (ItemID: ${ebayRevisionEventData?.itemId || "N/A"}): (No ebayData payload found)`;
                }
                break;
              }
              case "listing_description": {
                const listingDescData = event.eventData as { description?: string; itemId?: string };
                eventDetails = `Saved Listing Description: ${listingDescData?.description?.substring(0, 200) || "N/A"}...`;
                if (listingDescData?.itemId) {
                  eventDetails += ` (ItemID: ${listingDescData.itemId})`;
                }
                break;
              }
              default: {
                console.warn("Unknown activity log event type encountered for AI prompt", {
                  type: event.type,
                  skuId: args.skuId,
                });
                try {
                  eventDetails = `Event Data: ${JSON.stringify(event.eventData)?.substring(0, 150)}...`;
                } catch {
                  eventDetails = `Event Data: (Could not stringify)`;
                }
              }
            }
          } catch (e: unknown) {
            const errorMessage = e instanceof Error ? e.message : "Unknown error processing event data";
            console.error("Error formatting eventData for AI prompt", {
              type: event.type,
              skuId: args.skuId,
              error: errorMessage,
              eventDataString: JSON.stringify(event.eventData)?.substring(0, 300),
            });
            eventDetails = `Error processing event data for type ${event.type}.`;
          }
          activityLogSummary += `[${event.type.replace(/_/g, " ").toUpperCase()} - ${eventTimestamp}]: ${eventDetails}\n---\n`;
        });
      } else {
        activityLogSummary = "No activity log entries found for this SKU.\n";
      }

      // Step 4: Construct userAiContent (same as original)
      let userAiContent = `Generate or enhance an eBay listing based on the following information. Prioritize the most recent information if conflicts exist. Be mindful of eBay policies and best practices.\n\n`;

      userAiContent += `Core Item Details:\n${skuContext}\n\n`;

      const { listingContext, existingDescription, researchNotes, enhancementPrompt } = args;

      if (listingContext && Object.keys(listingContext).length > 0) {
        userAiContent += `Current Listing Context (use this information to ensure consistency and accuracy):\n\`\`\`json\n${JSON.stringify(listingContext, null, 2)}\n\`\`\`\n\n`;
      }

      if (existingDescription) {
        userAiContent += `Current Draft Description (to improve/build upon, if different from activity log):\n\`\`\`\n${existingDescription}\n\`\`\`\n\n`;
      }

      userAiContent += `Historical Activity Log for this SKU (Oldest to Newest):\n\`\`\`text\n${activityLogSummary}\`\`\`\n\n`;

      if (researchNotes && researchNotes.length > 0) {
        userAiContent += `Additional Seller Research Notes (may overlap with activity log, use as supplemental context):\n${researchNotes.join("\n\n---\n\n")}\n--- End Additional Seller Notes ---\n\n`;
      }

      if (enhancementPrompt) {
        userAiContent += `Specific Instructions for this Enhancement: ${enhancementPrompt}\n\n`;
      }

      userAiContent += `Please provide an updated JSON response with the following fields: "suggestedTitle" (max 80 chars), "suggestedSubtitle", "description", "suggestedAspects", "valuationEstimate", "buyerPersona", and "potentialBuyerQuestions". Refer to the system prompt for detailed structure and constraints for each field. For suggestedAspects, provide a name and an array of values for each aspect.`;

      console.log("User AI Content for generateObject:", {
        skuId: args.skuId,
        length: userAiContent.length,
        contentPrefix: userAiContent.substring(0, 500),
      });

      // Step 5: Call AI using your existing pattern (Google/Gemini)
      const { generateObject } = await import('ai');
      const { google } = await import('@ai-sdk/google');
      const { z } = await import('zod');

      // Use same model configuration pattern as existing implementation
      const sellerListingModel = process.env.AI_MODEL_SELLER_LISTING || 'google:gemini-2.5-flash';
      const [provider, modelName] = sellerListingModel.split(':');

      if (provider !== 'google') {
        throw new Error(`Unsupported AI provider for SKU processing: ${provider}. Only 'google' is currently supported.`);
      }

      if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
        throw new Error('GOOGLE_GENERATIVE_AI_API_KEY is not set for Gemini provider.');
      }

      if (!modelName) {
        throw new Error(`Invalid AI model format: ${sellerListingModel}. Expected format: provider:model`);
      }

      console.log(`Using AI model for SKU processing: ${sellerListingModel}`);

      const geminiModel = google(modelName);

      // Define the same schema as the original implementation
      const ListingSchema = z.object({
        suggestedTitle: z
          .string()
          .max(90)
          .describe('Compelling eBay title that maximizes searchability, STRICT MAXIMUM 76 characters.'),
        suggestedSubtitle: z
          .string()
          .max(65)
          .optional()
          .describe('Attention grabbing eBay subtitle, STRICT MAXIMUM 52 characters'),
        description: z
          .string()
          .describe('Detailed product description for eBay, GitHub Flavored Markdown.'),
        valuationEstimate: z
          .object({
            low: z.number(),
            best: z.number(),
            high: z.number(),
          })
          .optional()
          .describe('Valuation estimate with low, best, and high values'),
        buyerPersona: z.string().optional().describe('Brief description of a typical buyer persona'),
        potentialBuyerQuestions: z
          .array(z.string())
          .optional()
          .describe('List of potential buyer questions and concerns'),
        suggestedAspects: z
          .array(z.object({ name: z.string(), values: z.array(z.string()) }))
          .optional()
          .describe('List of suggested item specifics (aspects) with name and an array of values.'),
      });

      console.log(`Calling Gemini API for SKU ${args.skuId}`);

      // Retry logic for schema validation failures  
      const maxAttempts = 2; // Only 1 retry - max ~60 seconds total
      let attempt = 0;
      let lastError;
      let response;

      while (attempt < maxAttempts) {
        try {
          console.log(`AI attempt ${attempt + 1}/${maxAttempts} for SKU ${args.skuId}`);
          
          response = await generateObject({
            model: geminiModel,
            schema: ListingSchema,
            messages: [
              { role: 'system', content: EBAY_LISTING_SYSTEM_PROMPT },
              { role: 'user', content: userAiContent }
            ],
            maxTokens: 8192
          });
          
          // If we get here, schema validation passed
          break;
          
        } catch (error) {
          lastError = error;
          attempt++;
          
          if (attempt < maxAttempts) {
            console.log(`AI schema validation failed for SKU ${args.skuId}, retrying attempt ${attempt + 1}/${maxAttempts}:`, 
              error instanceof Error ? error.message : String(error));
            // Brief delay to avoid hammering the API, but backoff isn't needed for schema issues
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }
      }

      if (!response) {
        throw new Error(`AI failed after ${maxAttempts} attempts. Last error: ${lastError instanceof Error ? lastError.message : String(lastError)}`);
      }

      console.log('AI processing completed successfully', {
        skuId: args.skuId,
        titleLength: response.object.suggestedTitle?.length,
        descriptionLength: response.object.description?.length,
        hasValuation: !!response.object.valuationEstimate,
        aspectsCount: response.object.suggestedAspects?.length || 0
      });

      // Save all AI insights to the SKU document (same as original)
      if (response.object) {
        const insightsPayload = {
          skuId: args.skuId,
          suggestedTitle: response.object.suggestedTitle,
          suggestedSubtitle: response.object.suggestedSubtitle,
          suggestedAiDescription: response.object.description,
          valuationEstimate: response.object.valuationEstimate,
          buyerPersona: response.object.buyerPersona,
          potentialBuyerQuestions: response.object.potentialBuyerQuestions,
          suggestedAspects: response.object.suggestedAspects,
        };

        try {
          console.log('Attempting to save all AI insights to SKU', {
            skuId: args.skuId,
            payloadKeys: Object.keys(insightsPayload),
          });
          const saveInsightsResult = await ctx.runMutation(
            internal.skuMutations.updateSkuAiInsightsInternal,
            insightsPayload
          );
          console.log('Successfully saved AI insights to SKU', { skuId: args.skuId, result: saveInsightsResult });
        } catch (insightsSaveError) {
          console.error('Failed to save AI insights to SKU via updateSkuAiInsights mutation', {
            skuId: args.skuId,
            error: insightsSaveError instanceof Error ? {
              message: insightsSaveError.message,
              stack: insightsSaveError.stack,
              name: insightsSaveError.name,
            } : String(insightsSaveError),
            payloadAttempted: {
              title: insightsPayload.suggestedTitle,
              subtitle: insightsPayload.suggestedSubtitle,
              descriptionLength: insightsPayload.suggestedAiDescription?.length,
            },
          });
          // CRITICAL: Return failure if we can't save the AI results
          return { 
            success: false, 
            error: `AI processing succeeded but failed to save results: ${insightsSaveError instanceof Error ? insightsSaveError.message : String(insightsSaveError)}` 
          };
        }
      } else {
        console.warn('AI listing object was undefined, skipping save to Convex.', { skuId: args.skuId });
        return { success: false, error: 'AI generation returned undefined object.' };
      }

      return {
        success: true,
        data: {
          suggestedTitle: response.object.suggestedTitle,
          suggestedSubtitle: response.object.suggestedSubtitle,
          description: response.object.description,
          valuationEstimate: response.object.valuationEstimate,
          buyerPersona: response.object.buyerPersona,
          potentialBuyerQuestions: response.object.potentialBuyerQuestions,
          suggestedAspects: response.object.suggestedAspects
        }
      };

    } catch (error) {
      console.error('SKU AI processing failed', {
        skuId: args.skuId,
        error: error instanceof Error ? error.message : String(error)
      });
      return { success: false, error: String(error) };
    }
  }
});