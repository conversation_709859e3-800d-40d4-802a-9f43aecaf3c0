import { v } from "convex/values";
import { mutation, query, action, internalAction, internalQuery, internalMutation } from "./_generated/server";
import { internal, api } from "./_generated/api";
import { ConvexError } from "convex/values";
import { getCurrentUser } from "./auth";
import { Id, Doc } from "./_generated/dataModel";
import { skuDocValidator } from "./skus";
import { convertEpsToListingUrl } from "../src/lib/ebay/helpers/epsImageUrl";
import { IMAGE_RECOGNITION_PROMPT } from "../src/lib/ai/seller-prompts";
import { getActiveAccount } from "./utils";

// Error constants
const ERRORS = {
    NOT_AUTHENTICATED: "Not authenticated",
    NOT_FOUND: "Image not found or access denied",
    ACCESS_DENIED: "Access denied",
    NO_EBAY_ACCOUNT: "No active eBay account found. Please select an eBay account first.",
    API_KEY_MISSING: "Bytescale API key or account ID not found in Convex environment. Use `npx convex env set` to configure them."
};

// Define result types for our actions
type DeleteResult = {
    success: boolean;
    error?: string;
};

// Save an image uploaded to Bytescale
export const saveImage = mutation({
    args: {
        bytescaleUrl: v.string(),
        bytescaleId: v.string(),
        originalFilename: v.string(),
        width: v.optional(v.number()),
        height: v.optional(v.number()),
        fileSize: v.optional(v.number()),
        mimeType: v.optional(v.string()),
        title: v.optional(v.string()),
        tags: v.optional(v.array(v.string())),
        skuId: v.optional(v.id('skus')),
        sourceId: v.optional(v.id('sources')), // NEW: Allow direct source assignment
        skipAutoRecognition: v.optional(v.boolean()), // NEW: Allow skipping auto-recognition
        parentImageId: v.optional(v.id('images')), // NEW: Support parent-child relationship
        variantType: v.optional(v.union(
            v.literal("original"),
            v.literal("thumbnail"),
            v.literal("crop"),
            v.literal("ai_segment"),
            v.literal("ai_split"),
            v.literal("background_removed"),
            v.literal("edited")
        )), // NEW: Support variant type
    },
    returns: v.id("images"),
    handler: async (ctx, args): Promise<Id<"images">> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        // ENFORCE: Every image must have a sourceId
        if (!args.sourceId) {
            // Auto-assign to default source if not provided
            const defaultSource = await ctx.db
                .query("sources")
                .withIndex("by_ebay_user_and_code", (q) =>
                    q.eq("ebayUserId", activeEbayUserId).eq("code", "JDAI")
                )
                .first();

            if (!defaultSource) {
                throw new ConvexError("No source specified and default source 'JDAI' not found. Please create a default source or specify a source for the image.");
            }

            args.sourceId = defaultSource._id;
        }

        // Verify the source belongs to the user
        const source = await ctx.db.get(args.sourceId);
        if (!source || source.ebayUserId !== activeEbayUserId) {
            throw new ConvexError("Source not found or access denied");
        }

        // If SKU is provided, verify it belongs to the user
        if (args.skuId) {
            const sku = await ctx.db.get(args.skuId);
            if (!sku || sku.ebayUserId !== activeEbayUserId) {
                throw new ConvexError("SKU not found or access denied");
            }
        }

        const imageId = await ctx.db.insert("images", {
            ebayUserId: activeEbayUserId,
            bytescaleUrl: args.bytescaleUrl,
            bytescaleId: args.bytescaleId,
            originalFilename: args.originalFilename,
            width: args.width,
            height: args.height,
            fileSize: args.fileSize,
            mimeType: args.mimeType,
            title: args.title || args.originalFilename,
            tags: args.tags || [],
            skuId: args.skuId,
            sourceId: args.sourceId, // REQUIRED: Every image has a source
            parentImageId: args.parentImageId, // NEW: Parent-child relationship support
            variantType: args.variantType, // NEW: Variant type support
            uploadedAt: Date.now(),
            updatedAt: Date.now(),
        });

        // NEW: If image is associated with a SKU, check for active drafts and notify machines
        if (args.skuId) {
            // Schedule a task to check for active drafts and notify machines (fire-and-forget)
            ctx.scheduler.runAfter(0, internal.quickListingMachineV2.notifyMachinesOfSkuImageUpdate, {
                skuId: args.skuId.toString(),
                newImageId: imageId.toString(),
                ebayUserId: activeEbayUserId
            }).catch((error) => {
                // Log error but don't fail the main operation
                console.error('Failed to schedule SKU image notification', {
                    skuId: args.skuId,
                    imageId,
                    error: error instanceof Error ? error.message : String(error)
                });
            });
        }

        // NEW: Automatically schedule image recognition processing
        if (!args.skipAutoRecognition) {
            // Check if automatic recognition is enabled
            const autoRecognitionEnabled = process.env.AUTO_IMAGE_RECOGNITION_ENABLED !== 'false';

            if (autoRecognitionEnabled) {
                console.log(`Scheduling automatic image recognition for image ${imageId}`);
                await ctx.scheduler.runAfter(0, internal.images.processImageRecognition, {
                    imageId: imageId,
                    userId: activeEbayUserId,
                    forceRerun: false,
                });
            } else {
                console.log(`Automatic image recognition disabled for image ${imageId}`);
            }
        }

        return imageId;
    },
});

// Define reusable validator for image doc structure returned by queries
const imageDocValidator = v.object({
    _id: v.id("images"),
    _creationTime: v.number(),
    ebayUserId: v.string(),
    sourceId: v.id("sources"), // Required field for source association
    bytescaleUrl: v.string(),
    bytescaleId: v.string(),
    originalFilename: v.string(),
    width: v.optional(v.number()),
    height: v.optional(v.number()),
    fileSize: v.optional(v.number()),
    mimeType: v.optional(v.string()),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    skuId: v.optional(v.id("skus")),
    sortOrder: v.optional(v.number()),
    epsUrl: v.optional(v.string()),
    epsUploadStatus: v.optional(v.string()),
    epsUploadError: v.optional(v.string()),
    lastEpsUploadAttempt: v.optional(v.number()),
    parentImageId: v.optional(v.id("images")),
    isAiGenerated: v.optional(v.boolean()),
    variantType: v.optional(v.string()),
    aiSegmentationData: v.optional(v.any()),
    isFavorite: v.optional(v.boolean()),
    uploadedAt: v.number(),
    updatedAt: v.number(),
    imageRecognitionText: v.optional(v.string()),
    archivedAt: v.optional(v.number()), // NEW: For archive state
    deletedAt: v.optional(v.number()), // NEW: For soft delete
    cleanupError: v.optional(v.string()), // NEW: Track cleanup failures
    lastCleanupAttempt: v.optional(v.number()), // NEW: Track cleanup attempts
});

// Get all images for the current user's ACTIVE ebay account (excluding soft-deleted)
export const getUserImages = query({
    args: {},
    returns: v.array(imageDocValidator),
    handler: async (ctx): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return [];
        }
        const activeEbayUserId = account.ebayUserId;

        return await ctx.db
            .query("images")
            .withIndex("by_user_id", (q) => q.eq("ebayUserId", activeEbayUserId))
            .filter((q) => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted images
            .order("desc")
            .collect();
    },
});

// Get images for a specific source (source-level organization)
export const getSourceImages = query({
    args: { sourceId: v.id("sources") },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return [];
        }
        const activeEbayUserId = account.ebayUserId;

        // Get images directly assigned to this source (not via SKU)
        return await ctx.db
            .query("images")
            .withIndex("by_source", (q) => q.eq("sourceId", args.sourceId))
            .filter((q) => q.eq(q.field("ebayUserId"), activeEbayUserId))
            .filter((q) => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted images
            .filter((q) => q.eq(q.field("archivedAt"), undefined)) // Exclude archived images
            .order("desc")
            .collect();
    },
});

// Get all images for a source (including those assigned via SKUs)
export const getAllSourceImages = query({
    args: { sourceId: v.id("sources") },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return [];
        }
        const activeEbayUserId = account.ebayUserId;

        // Get all SKUs for this source
        const sourceSkus = await ctx.db
            .query("skus")
            .withIndex("by_sourceId", (q) => q.eq("sourceId", args.sourceId))
            .filter((q) => q.eq(q.field("ebayUserId"), activeEbayUserId))
            .collect();

        const skuIds = sourceSkus.map(sku => sku._id);

        // Get images directly assigned to source
        const directImages = await ctx.db
            .query("images")
            .withIndex("by_source", (q) => q.eq("sourceId", args.sourceId))
            .filter((q) => q.eq(q.field("ebayUserId"), activeEbayUserId))
            .filter((q) => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted images
            .filter((q) => q.eq(q.field("archivedAt"), undefined)) // Exclude archived images
            .collect();

        // Get images assigned to SKUs in this source
        const skuImages = await Promise.all(
            skuIds.map(skuId =>
                ctx.db
                    .query("images")
                    .withIndex("by_sku", (q) => q.eq("skuId", skuId))
                    .filter((q) => q.eq(q.field("ebayUserId"), activeEbayUserId))
                    .filter((q) => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted images
                    .filter((q) => q.eq(q.field("archivedAt"), undefined)) // Exclude archived images
                    .collect()
            )
        );

        // Combine and deduplicate
        const allImages = [...directImages, ...skuImages.flat()];
        const uniqueImages = Array.from(
            new Map(allImages.map(img => [img._id, img])).values()
        );

        // Sort by upload date (newest first)
        return uniqueImages.sort((a, b) => b.uploadedAt - a.uploadedAt);
    },
});

// Get images by specific tags for the ACTIVE account
export const getImagesByTags = query({
    args: {
        tags: v.array(v.string()),
    },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return [];
        }
        const activeEbayUserId = account.ebayUserId;

        // If no tags specified, just return all user images for the active account
        if (args.tags.length === 0) {
            return await ctx.db
                .query("images")
                .withIndex("by_user_id", (q) => q.eq("ebayUserId", activeEbayUserId))
                .filter((q) => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted images
                .order("desc")
                .collect();
        }

        const images = await ctx.db
            .query("images")
            .withIndex("by_user_id", (q) => q.eq("ebayUserId", activeEbayUserId))
            .filter((q) => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted images
            .collect();

        // Filter in memory
        return images.filter(image => {
            if (!image.tags) return false;
            return args.tags.some(tag => image.tags!.includes(tag));
        }).sort((a, b) => b.uploadedAt - a.uploadedAt);
    },
});

// Get images for a specific SKU for the ACTIVE account
export const getImagesBySku = query({
    args: {
        skuId: v.id("skus"),
    },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return [];
        }
        const activeEbayUserId = account.ebayUserId;

        return await ctx.db
            .query("images")
            .withIndex("by_sku_and_order", (q) => q.eq("skuId", args.skuId))
            .filter((q) => q.eq(q.field("ebayUserId"), activeEbayUserId))
            .filter((q) => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted images
            .collect();
    }
});

// Add a new internal action to delete an image from Bytescale
export const deleteBytescaleImage = internalAction({
    args: {
        bytescaleUrl: v.string(),
    },
    handler: async (_ctx, args): Promise<DeleteResult> => {
        // Access environment variables from Convex environment
        const BYTESCALE_API_KEY = process.env.BYTESCALE_API_KEY;
        const BYTESCALE_ACCOUNT_ID = process.env.BYTESCALE_ACCOUNT_ID;

        if (!BYTESCALE_API_KEY || !BYTESCALE_ACCOUNT_ID) {
            console.error("Bytescale API key or account ID not found in Convex environment");
            return { success: false, error: "API key or account ID not found in Convex environment. Use `npx convex env set` to configure them." };
        }

        try {
            console.log(`Attempting to delete Bytescale file: ${args.bytescaleUrl}`);

            // Parse the URL to extract the path
            const url = new URL(args.bytescaleUrl);

            // First check if this is a crop variant
            const isCropVariant = url.pathname.endsWith('.crop') ||
                url.pathname.includes('.crop/') ||
                url.pathname.includes('/crop/');

            let filePath = '';

            // Extract the path from the Bytescale CDN URL
            // CDN URL formats can be:
            // - https://upcdn.io/{accountId}/raw/uploads/...
            // - https://upcdn.io/{accountId}/image/uploads/...
            // - https://upcdn.io/{accountId}/image/uploads/....jpg.crop (for crop variants)

            // First, get all path segments
            const pathSegments = url.pathname.split('/');

            // Find which transformation is in use (raw, image, etc.)
            const transformationTypes = ['raw', 'image', 'crop'];
            let transformationIndex = -1;

            for (const type of transformationTypes) {
                const index = pathSegments.findIndex(segment => segment === type);
                if (index !== -1) {
                    transformationIndex = index;
                    break;
                }
            }

            // If we found a transformation, take everything after it to form the file path
            if (transformationIndex !== -1 && transformationIndex < pathSegments.length - 1) {
                filePath = '/' + pathSegments.slice(transformationIndex + 1).join('/');

                // If this is a crop variant, we should record that for logging
                if (isCropVariant) {
                    console.log(`Detected crop variant: ${filePath}`);
                }
            } else {
                // Fallback: try to extract path based on account ID position
                const accountIdIndex = pathSegments.findIndex(segment => segment === BYTESCALE_ACCOUNT_ID);
                if (accountIdIndex !== -1 && accountIdIndex + 2 < pathSegments.length) {
                    // Skip accountId and transformation type
                    filePath = '/' + pathSegments.slice(accountIdIndex + 2).join('/');
                } else {
                    // Last resort fallback
                    filePath = '/uploads/' + pathSegments[pathSegments.length - 1];
                }
            }

            // According to the Bytescale API docs, we use:
            // DELETE /v2/accounts/{accountId}/files?filePath={filePath}
            const deleteUrl = `https://api.bytescale.com/v2/accounts/${BYTESCALE_ACCOUNT_ID}/files?filePath=${encodeURIComponent(filePath)}`;

            console.log(`Deleting Bytescale file with path: ${filePath}`);
            console.log(`Full delete URL: ${deleteUrl}`);

            const response = await fetch(deleteUrl, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${BYTESCALE_API_KEY}`,
                    'Content-Type': 'application/json',
                }
            });

            // Check if the deletion was successful (204 No Content is success)
            if (response.status === 204) {
                console.log(`Successfully deleted Bytescale file: ${filePath}`);
                return { success: true };
            } else {
                let errorMessage = `HTTP error ${response.status}`;
                try {
                    const errorData = await response.json();
                    console.error("Failed to delete image from Bytescale", {
                        status: response.status,
                        error: errorData
                    });
                    errorMessage = JSON.stringify(errorData);
                } catch {
                    // Couldn't parse the error as JSON
                    console.error("Failed to delete image from Bytescale", {
                        status: response.status
                    });
                }

                return {
                    success: false,
                    error: `Failed to delete: ${errorMessage}`
                };
            }
        } catch (error) {
            console.error("Error deleting image from Bytescale", { error, url: args.bytescaleUrl });
            return { success: false, error: String(error) };
        }
    }
});

// Background cleanup action - deletes from Bytescale and then removes from Convex
export const cleanupBytescaleImage = internalAction({
    args: {
        imageId: v.id("images"),
        bytescaleUrl: v.string()
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args): Promise<DeleteResult> => {
        try {
            console.log(`Starting Bytescale cleanup for image ${args.imageId}`);

            // 1. Delete from Bytescale
            const bytescaleResult = await ctx.runAction(internal.images.deleteBytescaleImage, {
                bytescaleUrl: args.bytescaleUrl
            }) as DeleteResult;

            if (bytescaleResult.success) {
                // 2. If Bytescale deletion succeeded, permanently remove from Convex
                await ctx.runMutation(internal.images.removeFromConvex, { id: args.imageId });
                console.log(`Successfully cleaned up image ${args.imageId} from both Bytescale and Convex`);
                return { success: true };
            } else {
                // 3. If Bytescale deletion failed, log error but don't remove from Convex yet
                // This allows for manual cleanup or retry later
                console.error(`Bytescale cleanup failed for image ${args.imageId}`, {
                    error: bytescaleResult.error,
                    bytescaleUrl: args.bytescaleUrl
                });

                // Mark the image with cleanup failure info
                await ctx.runMutation(internal.images.markCleanupFailure, {
                    imageId: args.imageId,
                    error: bytescaleResult.error || 'Unknown Bytescale deletion error'
                });

                return {
                    success: false,
                    error: `Bytescale cleanup failed: ${bytescaleResult.error}`
                };
            }
        } catch (error) {
            console.error(`Error in Bytescale cleanup for image ${args.imageId}`, { error });

            // Mark the cleanup failure
            try {
                await ctx.runMutation(internal.images.markCleanupFailure, {
                    imageId: args.imageId,
                    error: String(error)
                });
            } catch (markError) {
                console.error(`Failed to mark cleanup failure for image ${args.imageId}`, { markError });
            }

            return { success: false, error: String(error) };
        }
    }
});

// Helper mutation to mark cleanup failures
export const markCleanupFailure = internalMutation({
    args: {
        imageId: v.id("images"),
        error: v.string()
    },
    handler: async (ctx, args) => {
        try {
            await ctx.db.patch(args.imageId, {
                cleanupError: args.error,
                lastCleanupAttempt: Date.now(),
                updatedAt: Date.now()
            });
        } catch (error) {
            console.error(`Failed to mark cleanup failure for ${args.imageId}`, { error });
        }
    }
});

// Admin function to clean up orphaned records (where Bytescale file no longer exists)
export const cleanupOrphanedRecords = action({
    args: {},
    returns: v.object({
        success: v.boolean(),
        cleaned: v.number(),
        errors: v.array(v.string())
    }),
    handler: async (ctx): Promise<{ success: boolean; cleaned: number; errors: string[] }> => {
        const { account } = await ctx.runQuery(internal.images.getActiveAccountForAction, {});
        if (!account) {
            return {
                success: false,
                cleaned: 0,
                errors: ["No active eBay account found"]
            };
        }
        const activeEbayUserId = account.ebayUserId;

        try {
            // Get all images for the user (including soft-deleted ones)
            const allImages = await ctx.runQuery(internal.images.getAllImagesIncludingDeleted, {
                ebayUserId: activeEbayUserId
            });

            const errors: string[] = [];
            let cleaned = 0;

            console.log(`Checking ${allImages.length} images for orphaned records`);

            for (const image of allImages) {
                try {
                    // Check if the Bytescale file still exists
                    const response = await fetch(image.bytescaleUrl, { method: 'HEAD' });

                    if (response.status === 404) {
                        // File doesn't exist in Bytescale, safe to remove from Convex
                        await ctx.runMutation(internal.images.removeFromConvex, { id: image._id });
                        console.log(`Cleaned up orphaned record for image ${image._id}`);
                        cleaned++;
                    }
                } catch (error) {
                    const errorMsg = `Failed to check/cleanup image ${image._id}: ${error}`;
                    console.error(errorMsg);
                    errors.push(errorMsg);
                }
            }

            return { success: true, cleaned, errors };
        } catch (error) {
            console.error('Error in orphaned records cleanup', { error });
            return {
                success: false,
                cleaned: 0,
                errors: [String(error)]
            };
        }
    }
});

// Helper query to get all images including soft-deleted ones (internal)
export const getAllImagesIncludingDeleted = internalQuery({
    args: { ebayUserId: v.string() },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args) => {
        return await ctx.db
            .query("images")
            .withIndex("by_user_id", (q) => q.eq("ebayUserId", args.ebayUserId))
            .collect();
    }
});

// Helper query to get active account for actions (internal)
export const getActiveAccountForAction = internalQuery({
    args: {},
    returns: v.object({
        account: v.union(v.null(), v.object({
            _id: v.id("ebayAccounts"),
            ebayUserId: v.string(),
            ebayUsername: v.string(),
            credentialsId: v.id("ebayCredentials"),
        }))
    }),
    handler: async (ctx) => {
        const { account } = await getActiveAccount(ctx, { requireAccount: false });
        return { account };
    }
});

// Add a new action to orchestrate the entire deletion process
export const safeDeleteImage = action({
    args: { id: v.id("images") },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args): Promise<DeleteResult> => {
        try {
            // 1. Get the image details from Convex
            const image = await ctx.runQuery(internal.images.getImageByIdInternal, { id: args.id }) as Doc<"images"> | null;
            if (!image) {
                return { success: false, error: "Image not found" };
            }

            // 2. First try to delete from Bytescale
            const bytescaleResult = await ctx.runAction(internal.images.deleteBytescaleImage, {
                bytescaleUrl: image.bytescaleUrl
            }) as DeleteResult;

            // 3. Only if Bytescale deletion succeeded, delete from Convex
            if (bytescaleResult.success) {
                await ctx.runMutation(internal.images.removeFromConvex, { id: args.id });
                return { success: true };
            } else {
                // If Bytescale deletion failed, don't delete from Convex
                return {
                    success: false,
                    error: `Failed to delete from Bytescale: ${bytescaleResult.error}`
                };
            }
        } catch (error) {
            console.error("Error in safe delete process", { error });
            return { success: false, error: String(error) };
        }
    }
});

// Get an image by ID (internal)
export const getImageByIdInternal = internalQuery({
    args: {
        id: v.id("images"),
        ebayUserId: v.optional(v.string()) // Optional for backward compatibility
    },
    returns: v.union(v.null(), imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images"> | null> => {
        const image = await ctx.db.get(args.id);
        if (!image) {
            return null;
        }

        // If ebayUserId is provided, verify the image belongs to that user
        if (args.ebayUserId && image.ebayUserId !== args.ebayUserId) {
            console.warn("getImageByIdInternal: Access denied", {
                imageId: args.id,
                imageEbayUserId: image.ebayUserId,
                requestedEbayUserId: args.ebayUserId
            });
            return null;
        }

        return image;
    },
});

// Get an image by ID (public) - operates on ACTIVE account
export const getImageById = query({
    args: { id: v.id("images") },
    returns: v.union(v.null(), imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images"> | null> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            // Or throw error, depending on expected behavior
            console.warn("getImageById: No active account found");
            return null;
        }
        const activeEbayUserId = account.ebayUserId;

        const image = await ctx.db.get(args.id);
        if (!image) {
            return null;
        }

        // Verify the image belongs to the ACTIVE eBay account
        if (image.ebayUserId !== activeEbayUserId) {
            console.warn("getImageById: Access denied", { imageId: args.id, imageEbayUserId: image.ebayUserId, activeEbayUserId });
            return null;
        }

        return image;
    },
});


// Get multiple images by IDs (public) - operates on ACTIVE account
export const getImagesByIds = query({
    args: { ids: v.array(v.id("images")) },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return [];
        }
        const activeEbayUserId = account.ebayUserId;

        const validIds = args.ids.filter(id => id !== undefined && id !== null);
        if (validIds.length === 0) return [];

        const images = await Promise.all(validIds.map(id => ctx.db.get(id)));

        // Filter nulls and verify ownership against ACTIVE account
        const validImages = images
            .filter((img): img is Doc<"images"> => img !== null && img.ebayUserId === activeEbayUserId)
            .sort((a, b) => {
                const aIndex = validIds.findIndex(id => id === a._id);
                const bIndex = validIds.findIndex(id => id === b._id);
                return aIndex - bIndex;
            });

        return validImages;
    },
});

// Add a helper mutation to remove image from Convex only (internal only)
export const removeFromConvex = internalMutation({
    args: { id: v.id("images") },
    handler: async (ctx, args) => {
        await ctx.db.delete(args.id);
        return true;
    },
});

// Helper mutation to delete image record (alias for removeFromConvex)
export const deleteImageRecord = internalMutation({
    args: { imageId: v.id("images") },
    handler: async (ctx, args) => {
        await ctx.db.delete(args.imageId);
        return true;
    },
});

// Delete an image - operates on ACTIVE account (soft delete)
export const deleteImage = mutation({
    args: {
        id: v.id("images"),
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args): Promise<{ success: boolean; error?: string }> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        const image = await ctx.db.get(args.id);
        if (!image || image.ebayUserId !== activeEbayUserId) {
            throw new ConvexError(ERRORS.NOT_FOUND);
        }

        try {
            // STEP 1: Soft delete - mark as deleted immediately (instant UI feedback)
            await ctx.db.patch(args.id, {
                deletedAt: Date.now(),
                updatedAt: Date.now()
            });

            // STEP 2: Schedule background cleanup of Bytescale (2 weeks later for recovery grace period)
            const twoWeeksInMs = 14 * 24 * 60 * 60 * 1000; // 2 weeks in milliseconds
            await ctx.scheduler.runAfter(twoWeeksInMs, internal.images.cleanupBytescaleImage, {
                imageId: args.id,
                bytescaleUrl: image.bytescaleUrl
            });

            console.log(`Soft deleted image ${args.id} and scheduled Bytescale cleanup in 2 weeks`);
            return { success: true };
        } catch (error) {
            console.error(`Failed to delete image ${args.id}`, { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    },
});

// Archive an image - keeps it forever but hides from workbench
export const archiveImage = mutation({
    args: {
        id: v.id("images"),
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args): Promise<{ success: boolean; error?: string }> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        const image = await ctx.db.get(args.id);
        if (!image || image.ebayUserId !== activeEbayUserId) {
            return {
                success: false,
                error: ERRORS.NOT_FOUND
            };
        }

        // Don't archive if already deleted
        if (image.deletedAt) {
            return {
                success: false,
                error: "Cannot archive a deleted image"
            };
        }

        try {
            await ctx.db.patch(args.id, {
                archivedAt: Date.now(),
                updatedAt: Date.now()
            });

            console.log(`Archived image ${args.id}`);
            return { success: true };
        } catch (error) {
            console.error("Failed to archive image", { error, imageId: args.id });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    },
});

// Update an image with provided updates
export const updateImage = mutation({
    args: {
        id: v.id("images"),
        updates: v.object({
            title: v.optional(v.string()),
            description: v.optional(v.string()),
            tags: v.optional(v.array(v.string())),
            isFavorite: v.optional(v.boolean()),
            skuId: v.optional(v.id("skus")),
            sortOrder: v.optional(v.number()),
            epsUrl: v.optional(v.string()),
            epsUploadStatus: v.optional(v.union(
                v.literal("pending"),
                v.literal("uploading"),
                v.literal("complete"),
                v.literal("failed")
            )),
        })
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args): Promise<{ success: boolean; error?: string }> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        // Get image and verify it exists
        const image = await ctx.db.get(args.id);
        if (!image) {
            return { success: false, error: "Image not found" };
        }

        // Verify ownership
        if (image.ebayUserId !== activeEbayUserId) {
            console.warn(`User ${activeEbayUserId} attempted to update image ${args.id} owned by ${image.ebayUserId}`);
            return { success: false, error: "Not authorized to update this image." };
        }

        try {
            // Add updatedAt to the updates
            const updates = {
                ...args.updates,
                updatedAt: Date.now()
            };

            // Apply the updates
            await ctx.db.patch(args.id, updates);
            console.log(`Updated image ${args.id} with new values`, { updates });

            return { success: true };
        } catch (error) {
            console.error(`Failed to update image ${args.id}`, { error });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
});

// Update image metadata - operates on ACTIVE account
export const updateImageMetadata = mutation({
    args: {
        id: v.id("images"),
        title: v.optional(v.string()),
        description: v.optional(v.string()),
        tags: v.optional(v.array(v.string())),
        isFavorite: v.optional(v.boolean()),
        skuId: v.optional(v.id("skus")),
    },
    returns: v.boolean(),
    handler: async (ctx, args): Promise<boolean> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        const image = await ctx.db.get(args.id);
        if (!image || image.ebayUserId !== activeEbayUserId) {
            throw new ConvexError(ERRORS.NOT_FOUND);
        }

        const update: Partial<Doc<"images">> = {
            updatedAt: Date.now()
        };
        if (args.title !== undefined) update.title = args.title;
        if (args.description !== undefined) update.description = args.description;
        if (args.tags !== undefined) update.tags = args.tags;
        if (args.isFavorite !== undefined) update.isFavorite = args.isFavorite;
        if (args.skuId !== undefined) update.skuId = args.skuId;

        await ctx.db.patch(args.id, update);
        return true;
    },
});

// Associate image with a SKU - operates on ACTIVE account
export const associateImageWithSku = mutation({
    args: {
        imageId: v.id("images"),
        skuId: v.id("skus")
    },
    returns: v.null(),
    handler: async (ctx, args) => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        // --- Check if the SKU document exists and belongs to the user ---
        // No longer looking up by fullSku string, directly using provided skuId
        const skuDoc = await ctx.db.get(args.skuId);

        if (!skuDoc) {
            // Error if the provided skuId doesn't correspond to an existing SKU document
            throw new ConvexError(`SKU not found for ID: ${args.skuId}`);
        }
        // Additionally, verify the SKU belongs to the active user
        if (skuDoc.ebayUserId !== activeEbayUserId) {
            throw new ConvexError(`SKU ${args.skuId} does not belong to the active user ${activeEbayUserId}.`);
        }

        // --- Check if the image exists and belongs to the user ---
        const image = await ctx.db.get(args.imageId);
        if (!image || image.ebayUserId !== activeEbayUserId) {
            throw new ConvexError(ERRORS.NOT_FOUND);
        }

        // --- Update the image with the provided skuId (keep sourceId) ---
        await ctx.db.patch(args.imageId, {
            skuId: args.skuId,
            updatedAt: Date.now()
        });

        console.log(`Associated image ${args.imageId} with SKU ID ${args.skuId}`);

        // --- RETROACTIVE LOGGING: If image has recognition text, log it to SKU activity ---
        if (image.imageRecognitionText) {
            try {
                console.log(`[RETROACTIVE] Image ${args.imageId} has recognition text - logging to SKU ${args.skuId} activity`);
                // Use direct mutation call instead of scheduling for better test reliability
                await ctx.runMutation(internal.skuMutations.logImageRecognitionResultInternal, {
                    skuId: args.skuId,
                    imageId: args.imageId,
                    imageRecognitionText: image.imageRecognitionText
                });
                console.log(`[RETROACTIVE] ✅ Successfully logged activity for image ${args.imageId}`);
            } catch (error) {
                console.error(`[RETROACTIVE] ❌ Failed to log activity for image ${args.imageId}`, {
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        } else {
            console.log(`[RETROACTIVE] Image ${args.imageId} has no recognition text - skipping activity logging`);
        }

        // --- REMOVED AUTO-INITIALIZE MACHINE: Moved to single trigger point to prevent race conditions ---
        // Machine initialization now happens from a single location after all images are processed
        console.log(`[INFO] Image ${args.imageId} associated with SKU ${args.skuId} - machine initialization handled elsewhere`);

        return null;
    },
});

// Move image to a different source (source-level organization)
export const moveImageToSource = mutation({
    args: {
        imageId: v.id("images"),
        sourceId: v.id("sources")
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args) => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        try {
            // Verify source exists and belongs to user
            const source = await ctx.db.get(args.sourceId);
            if (!source || source.ebayUserId !== activeEbayUserId) {
                return { success: false, error: "Source not found or access denied" };
            }

            // Verify image exists and belongs to user
            const image = await ctx.db.get(args.imageId);
            if (!image || image.ebayUserId !== activeEbayUserId) {
                return { success: false, error: "Image not found or access denied" };
            }

            // Update image to new source (keep SKU assignment if any)
            await ctx.db.patch(args.imageId, {
                sourceId: args.sourceId,
                updatedAt: Date.now()
            });

            console.log(`Moved image ${args.imageId} to source ${args.sourceId}`);
            return { success: true };
        } catch (error) {
            console.error("Error moving image to source", { error, imageId: args.imageId, sourceId: args.sourceId });
            return {
                success: false,
                error: error instanceof Error ? error.message : "Unknown error"
            };
        }
    },
});

// Remove SKU association from an image - operates on ACTIVE account
export const removeSkuFromImage = mutation({
    args: {
        imageId: v.id("images"),
    },
    returns: v.object({ success: v.boolean() }),
    handler: async (ctx, args): Promise<{ success: boolean }> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        const image = await ctx.db.get(args.imageId);
        if (!image || image.ebayUserId !== activeEbayUserId) {
            throw new ConvexError(ERRORS.NOT_FOUND);
        }

        await ctx.db.patch(args.imageId, {
            skuId: undefined,
            sortOrder: undefined,
            updatedAt: Date.now()
        });

        return { success: true };
    }
});

// Update image sort order for a SKU - operates on ACTIVE account
export const updateImageSortOrder = mutation({
    args: {
        imageIds: v.array(v.id("images")),
        skuId: v.id("skus"),
    },
    returns: v.object({ success: v.boolean() }),
    handler: async (ctx, args): Promise<{ success: boolean }> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        await Promise.all(args.imageIds.map(async (imageId, i) => {
            if (!imageId) {
                throw new ConvexError(`Invalid image ID at index ${i}`);
            }
            const image = await ctx.db.get(imageId);

            if (!image || image.ebayUserId !== activeEbayUserId || image.skuId !== args.skuId) {
                console.warn("Skipping image in sort order update due to mismatch", {
                    imageId: imageId.toString(),
                    imageSkuId: image?.skuId?.toString(),
                    targetSkuId: args.skuId.toString(),
                    imageAccount: image?.ebayUserId,
                    activeAccount: activeEbayUserId
                });
                return;
            }

            await ctx.db.patch(imageId, {
                sortOrder: i,
                updatedAt: Date.now()
            });
        }));

        return { success: true };
    }
});

// New function to update EPS upload status
export const updateEpsUploadStatus = mutation({
    args: {
        imageId: v.id("images"),
        status: v.union(
            v.literal("pending"),
            v.literal("uploading"),
            v.literal("complete"),
            v.literal("failed")
        ),
        epsUrl: v.optional(v.string()),
        error: v.optional(v.string())
    },
    returns: v.object({
        success: v.boolean()
    }),
    handler: async (ctx, args): Promise<{ success: boolean }> => {
        const image = await ctx.db.get(args.imageId);
        if (!image) {
            throw new ConvexError("Image not found");
        }

        const update: Record<string, string | number> = {
            epsUploadStatus: args.status,
            lastEpsUploadAttempt: Date.now(),
            updatedAt: Date.now()
        };

        if (args.epsUrl) update.epsUrl = args.epsUrl;
        if (args.error) update.epsUploadError = args.error;

        await ctx.db.patch(args.imageId, update);
        return { success: true };
    }
});

// Internal mutation to update EPS status after upload attempt
export const internalUpdateEpsStatus = internalMutation({
    args: {
        imageId: v.id("images"),
        status: v.union(
            v.literal("pending"),
            v.literal("uploading"),
            v.literal("complete"),
            v.literal("failed")
        ),
        epsUrl: v.optional(v.string()),
        error: v.optional(v.string())
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args): Promise<{ success: boolean; error?: string | undefined; }> => {
        console.log(`[internalUpdateEpsStatus] Updating image ${args.imageId} to status: ${args.status}`);
        try {
            await ctx.db.patch(args.imageId, {
                epsUploadStatus: args.status,
                epsUrl: args.epsUrl,
                epsUploadError: args.error,
                lastEpsUploadAttempt: Date.now(),
                updatedAt: Date.now()
            });
            console.log(`[internalUpdateEpsStatus] Successfully updated image ${args.imageId}`);
            return { success: true };
        } catch (error) {
            console.error(`[internalUpdateEpsStatus] Failed to update image ${args.imageId}`, { error });
            return { success: false, error: error instanceof ConvexError ? error.message : String(error) };
        }
    }
});

// Internal mutation to update image EPS URL
export const updateImageEpsUrl = internalMutation({
    args: {
        imageId: v.id('images'),
        epsUrl: v.string(),
        ebayUserId: v.string()
    },
    handler: async (ctx, { imageId, epsUrl, ebayUserId }) => {
        console.log(`[updateImageEpsUrl] Updating image ${imageId} with EPS URL: ${epsUrl}`);

        // Verify the image exists and belongs to the user
        const image = await ctx.db.get(imageId);
        if (!image || image.ebayUserId !== ebayUserId) {
            console.error(`[updateImageEpsUrl] Image ${imageId} not found or doesn't belong to user ${ebayUserId}`);
            throw new ConvexError("Image not found or access denied");
        }

        await ctx.db.patch(imageId, {
            epsUrl: epsUrl,
            epsUploadStatus: 'complete',
            lastEpsUploadAttempt: Date.now(),
            updatedAt: Date.now()
        });

        console.log(`[updateImageEpsUrl] Successfully updated image ${imageId} with EPS URL`);
    }
});

// Get unassociated images (those without a skuId) for the ACTIVE account
export const getUnassociatedImages = query({
    args: {
        // No args needed, uses active context
    },
    returns: v.array(imageDocValidator), // Use defined validator
    handler: async (ctx): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return []; // No active account
        }
        const activeEbayUserId = account.ebayUserId;

        return await ctx.db
            .query("images")
            .withIndex("by_user_id", q => q.eq("ebayUserId", activeEbayUserId))
            .filter(q => q.eq(q.field("skuId"), undefined))
            .order("desc")
            .collect();
    }
});

// Find images by their Bytescale IDs for the ACTIVE account
export const findImagesByBytescaleId = query({
    args: {
        bytescaleIds: v.array(v.string())
    },
    returns: v.array(imageDocValidator), // Use defined validator
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return []; // No active account
        }
        const activeEbayUserId = account.ebayUserId;

        if (args.bytescaleIds.length === 0) {
            return [];
        }

        // Consider optimizing this if performance is an issue for large datasets
        // Fetching all images might be slow.
        const images = await ctx.db
            .query("images")
            .withIndex("by_user_id", q => q.eq("ebayUserId", activeEbayUserId))
            .collect();

        return images.filter(img =>
            args.bytescaleIds.some(id =>
                img.bytescaleId === id ||
                (img.bytescaleUrl && img.bytescaleUrl.split('/').pop() === id)
            )
        );
    }
});

// Find an image by its EPS URL for the ACTIVE account
export const findByEpsUrl = query({
    args: { epsUrl: v.string() },
    returns: v.union(v.null(), imageDocValidator), // May return null if not found or access denied
    handler: async (ctx, args): Promise<Doc<"images"> | null> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return null; // No active account
        }
        const activeEbayUserId = account.ebayUserId;

        // Query using the new index
        const image = await ctx.db
            .query("images")
            .withIndex("by_eps_url", q => q.eq("epsUrl", args.epsUrl))
            .first();

        // Check if found and if it belongs to the active eBay account
        if (!image) {
            return null; // Not found
        }
        if (image.ebayUserId !== activeEbayUserId) {
            console.warn("findByEpsUrl: Access denied", { imageId: image._id, imageEbayUserId: image.ebayUserId, activeEbayUserId });
            return null; // Access denied
        }

        return image;
    }
});

// Find an image by its external URL (like Bytescale) for the ACTIVE account
export const findByExternalUrl = query({
    args: { externalUrl: v.string() },
    returns: v.union(v.null(), imageDocValidator), // May return null if not found or access denied
    handler: async (ctx, args): Promise<Doc<"images"> | null> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return null; // No active account
        }
        const activeEbayUserId = account.ebayUserId;

        // Query all images for the active user and filter by bytescaleUrl
        // This is less efficient than using an index, but works without schema changes
        const images = await ctx.db
            .query("images")
            .withIndex("by_user_id", q => q.eq("ebayUserId", activeEbayUserId))
            .collect();

        // Find the image with matching bytescaleUrl
        const image = images.find(img => img.bytescaleUrl === args.externalUrl);

        return image || null;
    }
});

// Store an external image URL (like Bytescale) for the ACTIVE account
export const storeExternalImage = mutation({
    args: {
        externalUrl: v.string(),
        skuId: v.optional(v.id("skus")),
        sourceId: v.optional(v.id("sources")), // NEW: Allow direct source assignment
        source: v.optional(v.string()), // Legacy: for tags
        skipAutoRecognition: v.optional(v.boolean()), // NEW: Allow skipping auto-recognition
    },
    returns: v.object({
        success: v.boolean(),
        imageId: v.optional(v.id("images")),
        error: v.optional(v.string()),
    }),
    handler: async (ctx, args): Promise<{ success: boolean; imageId?: Id<"images">; error?: string }> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        try {
            // ENFORCE: Every image must have a sourceId
            let finalSourceId = args.sourceId;
            if (!args.sourceId) {
                // Determine which default source to use based on context
                const isEbayImport = args.source === 'bytescale' || args.externalUrl.includes('ebay') || args.externalUrl.includes('eps');
                const defaultSourceCode = isEbayImport ? "EBAY" : "JDAI";
                const defaultSourceName = isEbayImport ? "eBay Import" : "Default";

                const defaultSource = await ctx.db
                    .query("sources")
                    .withIndex("by_ebay_user_and_code", (q) =>
                        q.eq("ebayUserId", activeEbayUserId).eq("code", defaultSourceCode)
                    )
                    .first();

                if (!defaultSource) {
                    return {
                        success: false,
                        error: `No source specified and default source '${defaultSourceCode}' (${defaultSourceName}) not found. Please create a default source or specify a source for the image.`
                    };
                }

                finalSourceId = defaultSource._id;
            }

            // Verify the source belongs to the user
            if (finalSourceId) {
                const source = await ctx.db.get(finalSourceId);
                if (!source || source.ebayUserId !== activeEbayUserId) {
                    return {
                        success: false,
                        error: "Source not found or access denied"
                    };
                }
            }

            // Extract filename from URL
            const url = new URL(args.externalUrl);
            const pathSegments = url.pathname.split('/');
            const filename = pathSegments[pathSegments.length - 1] || 'unknown';

            // Generate a unique bytescaleId if not present in URL
            const bytescaleId = pathSegments[pathSegments.length - 1] || `external-${Date.now()}`;

            // Insert the image record
            const imageId = await ctx.db.insert("images", {
                ebayUserId: activeEbayUserId,
                bytescaleUrl: args.externalUrl,
                bytescaleId: bytescaleId,
                originalFilename: filename,
                skuId: args.skuId,
                sourceId: finalSourceId!, // REQUIRED: Every image has a source
                title: filename,
                tags: args.source ? [args.source] : [],
                uploadedAt: Date.now(),
                updatedAt: Date.now(),
            });

            // NEW: Automatically schedule image recognition processing (same logic as saveImage)
            if (!args.skipAutoRecognition) {
                // Check if automatic recognition is enabled
                const autoRecognitionEnabled = process.env.AUTO_IMAGE_RECOGNITION_ENABLED !== 'false';

                if (autoRecognitionEnabled) {
                    console.log(`Scheduling automatic image recognition for external image ${imageId}`);
                    await ctx.scheduler.runAfter(0, internal.images.processImageRecognition, {
                        imageId: imageId,
                        userId: activeEbayUserId,
                        forceRerun: false,
                    });
                } else {
                    console.log(`Automatic image recognition disabled for external image ${imageId}`);
                }
            }

            return { success: true, imageId };
        } catch (error) {
            console.error("Error storing external image", { error, externalUrl: args.externalUrl });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
});

// Add a mutation called updateImageRecognitionText to update the imageRecognitionText field for an image
export const updateImageRecognitionText = mutation({
    args: {
        id: v.id("images"),
        imageRecognitionText: v.string(),
    },
    returns: v.boolean(),
    handler: async (ctx, args): Promise<boolean> => {
        const { account } = await getActiveAccount(ctx, { requireAccount: true });
        const activeEbayUserId = account!.ebayUserId;

        const image = await ctx.db.get(args.id);
        if (!image || image.ebayUserId !== activeEbayUserId) {
            throw new ConvexError(ERRORS.NOT_FOUND);
        }

        await ctx.db.patch(args.id, {
            imageRecognitionText: args.imageRecognitionText,
            updatedAt: Date.now(),
        });

        // CRITICAL FIX: Check if this image is already associated with a SKU
        // and trigger activity logging for the associated SKU (same logic as internal version)
        if (image.skuId) {
            try {
                console.log(`[RECOGNITION COMPLETE] Logging recognition to SKU ${image.skuId} for image ${args.id}`);
                // Use direct mutation call for consistency with internal version
                await ctx.runMutation(internal.skuMutations.logImageRecognitionResultInternal, {
                    skuId: image.skuId,
                    imageId: args.id,
                    imageRecognitionText: args.imageRecognitionText
                });
                console.log(`[RECOGNITION COMPLETE] ✅ Successfully logged activity for SKU ${image.skuId}`);
            } catch (error) {
                console.error(`[RECOGNITION COMPLETE] ❌ Failed to log activity for SKU ${image.skuId}`, {
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        } else {
            console.log(`[RECOGNITION COMPLETE] Image ${args.id} has no SKU association - no activity logging needed`);
        }

        return true;
    },
});

// NEW: Internal version for use by automatic processing
export const updateImageRecognitionTextInternal = internalMutation({
    args: {
        id: v.id("images"),
        imageRecognitionText: v.string(),
    },
    returns: v.boolean(),
    handler: async (ctx, args): Promise<boolean> => {
        const image = await ctx.db.get(args.id);
        if (!image) {
            throw new ConvexError(ERRORS.NOT_FOUND);
        }

        await ctx.db.patch(args.id, {
            imageRecognitionText: args.imageRecognitionText,
            updatedAt: Date.now(),
        });

        // CRITICAL FIX: Check if this image is already associated with a SKU
        // and trigger activity logging for the associated SKU
        if (image.skuId) {
            try {
                console.log(`[RECOGNITION COMPLETE] Logging recognition to SKU ${image.skuId} for image ${args.id}`);
                // Use direct mutation call instead of scheduling for better test reliability
                await ctx.runMutation(internal.skuMutations.logImageRecognitionResultInternal, {
                    skuId: image.skuId,
                    imageId: args.id,
                    imageRecognitionText: args.imageRecognitionText
                });
                console.log(`[RECOGNITION COMPLETE] ✅ Successfully logged activity for SKU ${image.skuId}`);
            } catch (error) {
                console.error(`[RECOGNITION COMPLETE] ❌ Failed to log activity for SKU ${image.skuId}`, {
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        } else {
            console.log(`[RECOGNITION COMPLETE] Image ${args.id} has no SKU association - no activity logging needed`);
        }

        return true;
    },
});

// NEW: Internal version for updating image metadata
export const updateImageMetadataInternal = internalMutation({
    args: {
        id: v.id("images"),
        title: v.optional(v.string()),
        description: v.optional(v.string()),
    },
    returns: v.boolean(),
    handler: async (ctx, args): Promise<boolean> => {
        const image = await ctx.db.get(args.id);
        if (!image) {
            throw new ConvexError(ERRORS.NOT_FOUND);
        }

        const updateData: Partial<{
            title: string;
            description: string;
            updatedAt: number;
        }> = {
            updatedAt: Date.now(),
        };

        if (args.title !== undefined) {
            updateData.title = args.title;
        }
        if (args.description !== undefined) {
            updateData.description = args.description;
        }

        await ctx.db.patch(args.id, updateData);
        return true;
    },
});

// Get SKU information
export const getSku = query({
    args: {
        fullSku: v.string(),
    },
    returns: v.union(v.null(), skuDocValidator),
    handler: async (ctx, args) => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            console.warn("getSku: No active eBay account found.");
            return null;
        }
        const activeEbayUserId = account.ebayUserId;

        // Query using the new index and field
        return await ctx.db
            .query("skus")
            .withIndex("by_ebayUserId_and_fullSku", q =>
                q.eq("ebayUserId", activeEbayUserId).eq("fullSku", args.fullSku)
            )
            .unique();
    }
});

// Public Action to trigger fetching and storing an image from EPS
export const fetchAndStoreEpsImage = action({
    args: {
        epsUrl: v.string(),
        skuId: v.optional(v.id("skus")),
        ebayItemId: v.optional(v.string()),
    },
    returns: v.object({ imageId: v.id("images") }),
    handler: async (ctx, args): Promise<{ imageId: Id<"images"> }> => {
        console.log(`[fetchAndStoreEpsImage] Public action called for EPS URL: ${args.epsUrl}`);
        // Actions can't use getActiveAccount directly - need to use separate query
        const user = await getCurrentUser(ctx);
        if (!user?.userId) throw new ConvexError(ERRORS.NOT_AUTHENTICATED);

        const activeAccountInfo = await ctx.runQuery(api.ebayAccounts.getActiveAccount, { userId: user.userId });
        if (!activeAccountInfo) {
            throw new ConvexError(ERRORS.NO_EBAY_ACCOUNT);
        }
        const activeEbayUserId = activeAccountInfo.ebayUserId;

        // Call the internal action, passing the resolved ebayUserId
        return await ctx.runAction(internal.images.internalFetchAndStoreImage, {
            epsUrl: args.epsUrl,
            ebayUserId: activeEbayUserId,
            skuId: args.skuId,
            ebayItemId: args.ebayItemId,
        });
    }
});

// Internal helper action to fetch image from EPS URL, upload to Bytescale, and store in Convex
export const internalFetchAndStoreImage = internalAction({
    args: {
        epsUrl: v.string(),
        ebayUserId: v.string(),
        skuId: v.optional(v.id("skus")),
        ebayItemId: v.optional(v.string()),
    },
    returns: v.object({ imageId: v.id("images") }), // Return the Convex ID
    handler: async (_ctx, args): Promise<{ imageId: Id<"images"> }> => {
        console.log(`[internalFetchAndStoreImage] Processing EPS URL: ${args.epsUrl}`);
        const BYTESCALE_API_KEY = process.env.BYTESCALE_API_KEY;
        const BYTESCALE_ACCOUNT_ID = process.env.BYTESCALE_ACCOUNT_ID;

        if (!BYTESCALE_API_KEY || !BYTESCALE_ACCOUNT_ID) {
            throw new ConvexError(ERRORS.API_KEY_MISSING);
        }

        // --- Get High-Res URL ---
        let highResEpsUrl = args.epsUrl;
        try {
            // Call the correctly named function
            const standardizedUrl = convertEpsToListingUrl(args.epsUrl);
            if (standardizedUrl && standardizedUrl !== args.epsUrl) {
                highResEpsUrl = standardizedUrl;
                console.log(`[internalFetchAndStoreImage] Standardized to high-res URL: ${highResEpsUrl}`);
            } else if (!standardizedUrl) {
                console.warn(`[internalFetchAndStoreImage] convertEpsToListingUrl returned null, using original: ${args.epsUrl}`);
            }
        } catch (urlError) {
            console.warn(`[internalFetchAndStoreImage] Failed to standardize URL, using original: ${args.epsUrl}`, { urlError });
        }
        // --- End Get High-Res URL ---

        // 1. Use Bytescale Upload from URL API with the high-res URL
        const bytescaleUploadUrl = `https://api.bytescale.com/v2/accounts/${BYTESCALE_ACCOUNT_ID}/uploads/url`;
        let bytescaleResponse;
        try {
            bytescaleResponse = await fetch(bytescaleUploadUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${BYTESCALE_API_KEY}`,
                    'Content-Type': 'application/json',
                },
                // Use the potentially standardized high-res URL
                body: JSON.stringify({ url: highResEpsUrl }),
            });

            if (!bytescaleResponse.ok) {
                const errorBody = await bytescaleResponse.text();
                console.error('Bytescale Upload from URL failed', { status: bytescaleResponse.status, errorBody });
                throw new ConvexError(`Bytescale upload failed: ${bytescaleResponse.statusText} - ${errorBody}`);
            }

        } catch (error) {
            console.error("Error during Bytescale fetch/upload", { error });
            throw new ConvexError(`Failed to fetch/upload from EPS URL: ${args.epsUrl}`); // Re-throw to indicate failure
        }

        const bytescaleData = await bytescaleResponse.json();
        console.log('[internalFetchAndStoreImage] Bytescale response', { bytescaleData });

        const { fileUrl: bytescaleUrl, filePath, originalFileName } = bytescaleData;
        if (!bytescaleUrl || !filePath) {
            throw new ConvexError('Bytescale response missing fileUrl or filePath');
        }
        const bytescaleId = filePath.split('/').pop() || 'unknown_id'; // Extract ID from path

        // Generate a better filename for imported images
        let generatedFilename = originalFileName || 'imported_from_eps.jpg';

        if (!originalFileName) {
            // Use eBay listing ID if available for meaningful filename
            if (args.ebayItemId) {
                generatedFilename = `import_from_ebay_${args.ebayItemId}.jpg`;
            } else {
                // Fallback: use timestamp to make it unique
                const timestamp = Date.now().toString().slice(-8); // Last 8 digits
                generatedFilename = `import_from_ebay_${timestamp}.jpg`;
            }
        }

        // 2. Store in Convex using an internal mutation
        // (Need to import Id type and define the internal mutation)
        const imageId = await _ctx.runMutation(internal.images.internalSaveFetchedImage, {
            bytescaleUrl: bytescaleUrl,
            bytescaleId: bytescaleId,
            originalFilename: generatedFilename,
            epsUrl: args.epsUrl,
            ebayUserId: args.ebayUserId,
            skuId: args.skuId,
            sourceId: undefined, // Will auto-assign to EBAY source for imported images
            skipAutoRecognition: false, // Always schedule recognition for imported images
        });

        console.log(`[internalFetchAndStoreImage] Stored image ${imageId} for EPS URL ${args.epsUrl}`);
        return { imageId };
    }
});

// Internal Mutation to save the fetched image data
export const internalSaveFetchedImage = internalMutation({
    args: {
        bytescaleUrl: v.string(),
        bytescaleId: v.string(),
        originalFilename: v.string(),
        epsUrl: v.string(),
        ebayUserId: v.string(),
        skuId: v.optional(v.id("skus")),
        sourceId: v.optional(v.id("sources")), // NEW: Allow source specification
        skipAutoRecognition: v.optional(v.boolean()), // NEW: Allow skipping auto-recognition
    },
    returns: v.id("images"),
    handler: async (ctx, args): Promise<Id<"images">> => {
        // ENFORCE: Every image must have a sourceId
        let finalSourceId = args.sourceId;
        if (!args.sourceId) {
            // Auto-assign to eBay import source for imported images
            const ebayImportSource = await ctx.db
                .query("sources")
                .withIndex("by_ebay_user_and_code", (q) =>
                    q.eq("ebayUserId", args.ebayUserId).eq("code", "EBAY")
                )
                .first();

            if (!ebayImportSource) {
                throw new ConvexError("No source specified and eBay import source 'EBAY' not found for imported image.");
            }

            finalSourceId = ebayImportSource._id;
        }

        const imageId = await ctx.db.insert("images", {
            ebayUserId: args.ebayUserId,
            bytescaleUrl: args.bytescaleUrl,
            bytescaleId: args.bytescaleId,
            originalFilename: args.originalFilename,
            epsUrl: args.epsUrl, // Store the original EPS URL
            skuId: args.skuId, // Store associated SKU if provided
            sourceId: finalSourceId!, // REQUIRED: Every image has a source
            uploadedAt: Date.now(),
            updatedAt: Date.now(),
            // Other fields like width, height, etc., are optional and might be null here
        });

        // NEW: Automatically schedule image recognition processing (same logic as saveImage)
        if (!args.skipAutoRecognition) {
            // Check if automatic recognition is enabled
            const autoRecognitionEnabled = process.env.AUTO_IMAGE_RECOGNITION_ENABLED !== 'false';

            if (autoRecognitionEnabled) {
                console.log(`Scheduling automatic image recognition for imported image ${imageId}`);
                await ctx.scheduler.runAfter(0, internal.images.processImageRecognition, {
                    imageId: imageId,
                    userId: args.ebayUserId,
                    forceRerun: false,
                });
            } else {
                console.log(`Automatic image recognition disabled for imported image ${imageId}`);
            }
        }

        return imageId;
    }
});

// Product identifiers are now handled during SKU creation in ensureSku

// NEW: Internal action for retroactive logging of image recognition to SKU activity
export const logImageRecognitionToSkuActivity = internalAction({
    args: {
        skuId: v.id("skus"),
        imageId: v.id("images"),
        imageRecognitionText: v.string()
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args) => {
        try {
            console.log(`[RETROACTIVE] Logging image recognition to SKU activity for image ${args.imageId} → SKU ${args.skuId}`);

            await ctx.runMutation(internal.skuMutations.logImageRecognitionResultInternal, {
                skuId: args.skuId,
                imageId: args.imageId,
                imageRecognitionText: args.imageRecognitionText
            });

            console.log(`[RETROACTIVE] ✅ Successfully logged image recognition to SKU activity`);
            return { success: true };
        } catch (error) {
            console.error(`[RETROACTIVE] ❌ Failed to log image recognition to SKU activity`, {
                error: error instanceof Error ? error.message : String(error)
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
});

// NEW: Internal action for automatic image recognition processing
export const processImageRecognition = internalAction({
    args: {
        imageId: v.id("images"),
        userId: v.string(),
        forceRerun: v.optional(v.boolean()), // NEW: Allow forcing re-run even if already processed
    },
    returns: v.object({
        success: v.boolean(),
        error: v.optional(v.string()),
    }),
    handler: async (ctx, args) => {
        "use node";

        try {
            const logPrefix = args.forceRerun ? '[FORCE RERUN]' : '[AUTO]';
            console.log(`${logPrefix} Starting image recognition for image ${args.imageId}`);

            // Get the image details first
            const image = await ctx.runQuery(internal.images.getImageByIdInternal, {
                id: args.imageId
            });

            if (!image || !image.bytescaleUrl) {
                console.log(`${logPrefix} Image ${args.imageId} not found or missing bytescaleUrl`);
                return { success: false, error: "Image not found or missing bytescaleUrl" };
            }

            // Check if image already has recognition text (unless forcing re-run)
            if (image.imageRecognitionText && !args.forceRerun) {
                console.log(`${logPrefix} Image ${args.imageId} already has recognition text, skipping`);
                return { success: true };
            }

            if (args.forceRerun && image.imageRecognitionText) {
                console.log(`${logPrefix} Re-running image recognition for ${args.imageId} (previously processed)`);
            }

            // Import the image recognition logic from the existing action
            const { generateObject } = await import('ai');
            const { z } = await import('zod');
            const { getDownsizedBytescaleUrl } = await import('../src/lib/bytescale/url');

            // Simple error handling function to avoid importing config
            const handleAIError = async <T>(fn: () => Promise<T>, context: string): Promise<T> => {
                try {
                    return await fn();
                } catch (error) {
                    console.error(`AI Error in ${context}:`, error);
                    throw error;
                }
            };

            // Using centralized IMAGE_RECOGNITION_PROMPT from seller-prompts.ts

            // Zod Schema for Gemini Image Analysis Output (fixed to handle null values)
            const ImageAnalysisSchema = z.object({
                identifiedItem: z.string().describe('The primary item identified in the image.'),
                friendlyTitle: z
                    .string()
                    .describe(
                        "A clean, user-friendly filename-style title (e.g., 'vintage_leather_chair', 'red_ceramic_mug', 'apple_macbook_pro'). Use underscores instead of spaces, keep it concise but descriptive.",
                    ),
                transcribedText: z
                    .string()
                    .nullable()
                    .optional()
                    .describe('All readable text transcribed from labels or packaging.'),
                brand: z.string().nullable().optional().describe('Brand of the item, if visible and identifiable.'),
                model: z.string().nullable().optional().describe('Model name or number, if visible.'),
                serialNumber: z.string().nullable().optional().describe('Serial number, if visible.'),
                confidence: z
                    .enum(['high', 'medium', 'low'])
                    .optional()
                    .describe('Confidence level of the identification.'),
                otherPossibilities: z
                    .array(z.object({ item: z.string(), confidence: z.enum(['high', 'medium', 'low']) }))
                    .optional()
                    .describe('Other possible identifications with confidence levels.'),
                // Product identifiers (UPC, EAN, ISBN, etc.) for eBay catalog lookup
                identifiers: z
                    .array(z.string())
                    .optional()
                    .describe('Product identifiers like UPC, EAN, or ISBN numbers if clearly visible and readable. Only include complete, confident readings.'),
            });

            // Check for API key
            if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
                throw new Error('GOOGLE_GENERATIVE_AI_API_KEY is not set for Gemini provider.');
            }

            // Use the centralized image recognition prompt
            const systemPrompt = IMAGE_RECOGNITION_PROMPT;
            const downsizedUrl = getDownsizedBytescaleUrl(image.bytescaleUrl, 768, 768);

            console.log(`Processing image recognition for ${args.imageId} with downsized URL`);

            // Configure AI model for image recognition
            // Uses AI_MODEL_LISTING_IMAGE_RECOGNITION environment variable (format: provider:model)
            // Falls back to 'google:gemini-2.5-flash' if not configured
            // Get the model from environment variable with fallback
            const imageRecognitionModel = process.env.AI_MODEL_LISTING_IMAGE_RECOGNITION || 'google:gemini-2.5-flash';

            // Parse the model string (format: provider:model)
            const [provider, modelName] = imageRecognitionModel.split(':');
            if (!provider || !modelName) {
                throw new Error(`Invalid AI model format: ${imageRecognitionModel}. Expected format: provider:model (e.g., google:gemini-2.5-flash)`);
            }
            if (provider !== 'google') {
                throw new Error(`Unsupported AI provider for image recognition: ${provider}. Only 'google' is currently supported.`);
            }

            console.log(`Using AI model for image recognition: ${imageRecognitionModel}`);

            // Create the Gemini model with configurable model name
            const { google } = await import('@ai-sdk/google');
            const geminiVisionModel = google(modelName, {
                structuredOutputs: false,
            });

            if (!geminiVisionModel) {
                throw new Error('Could not get listing image recognition model instance.');
            }

            console.log(`Calling Gemini API for image ${args.imageId}`);
            const geminiResult = await handleAIError(async () => {
                const { object } = await generateObject({
                    model: geminiVisionModel,
                    schema: ImageAnalysisSchema,
                    messages: [
                        {
                            role: 'system',
                            content: systemPrompt,
                        },
                        {
                            role: 'user',
                            content: [
                                { type: 'image', image: new URL(downsizedUrl) },
                            ],
                        },
                    ],
                    maxTokens: 4096,
                });
                return object;
            }, 'gemini-image-analysis');

            if (!geminiResult) {
                throw new Error('Gemini image analysis failed to produce a valid result.');
            }

            console.log(`Gemini analysis successful for ${args.imageId}:`, {
                identifiedItem: geminiResult.identifiedItem,
                friendlyTitle: geminiResult.friendlyTitle,
                confidence: geminiResult.confidence,
                hasBrand: !!geminiResult.brand,
                hasModel: !!geminiResult.model,
                hasTranscribedText: !!geminiResult.transcribedText,
            });

            // Update the image with recognition text
            await ctx.runMutation(internal.images.updateImageRecognitionTextInternal, {
                id: args.imageId,
                imageRecognitionText: JSON.stringify(geminiResult, null, 2),
            });

            // Update the image title with the AI-generated friendly title if available
            if (geminiResult.friendlyTitle) {
                try {
                    // Create a clean description, handling null values
                    const brandInfo = geminiResult.brand ? ` (${geminiResult.brand})` : '';
                    const modelInfo = geminiResult.model ? ` - ${geminiResult.model}` : '';
                    const description = `AI-identified: ${geminiResult.identifiedItem}${brandInfo}${modelInfo}`;

                    await ctx.runMutation(internal.images.updateImageMetadataInternal, {
                        id: args.imageId,
                        title: geminiResult.friendlyTitle,
                        description: description,
                    });
                    console.log(`Updated image title with AI-generated friendly title: ${geminiResult.friendlyTitle}`);
                } catch (titleUpdateError) {
                    console.error('Failed to update image title with AI-generated friendly title', {
                        imageId: args.imageId,
                        error: titleUpdateError,
                    });
                }
            }

            // Log to SKU activity if image is associated with a SKU
            // NOTE: Images uploaded via quick listing flow are associated with SKUs AFTER recognition
            // Retroactive logging is handled in associateImageWithSku()
            if (image.skuId) {
                try {
                    console.log(`Logging image analysis result to SKU activity log for SKU ${image.skuId}`);
                    await ctx.runMutation(internal.skuMutations.logImageRecognitionResultInternal, {
                        skuId: image.skuId,
                        imageId: args.imageId,
                        imageRecognitionText: JSON.stringify(geminiResult, null, 2),
                    });
                    console.log(`Successfully logged image analysis to SKU activity log`);
                } catch (skuLogError) {
                    console.error(`Failed to log image analysis result to SKU activity log`, {
                        skuId: image.skuId,
                        error: skuLogError,
                    });
                }
            } else {
                console.log(`Image ${args.imageId} not associated with SKU - will be logged retroactively when associated`);
            }

            console.log(`Automatic image recognition completed successfully for image ${args.imageId}`);
            return { success: true };

        } catch (error) {
            console.error(`Automatic image recognition failed for image ${args.imageId}`, {
                error: error instanceof Error ? error.message : String(error),
            });
            return {
                success: false,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    },
});

// NEW: Internal query to get images without recognition text for batch processing
export const getUnrecognizedImages = internalQuery({
    args: {
        limit: v.optional(v.number()),
    },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const limit = args.limit || 50;

        // Get images that don't have recognition text and were uploaded recently
        const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days ago

        return await ctx.db
            .query("images")
            .filter((q) =>
                q.and(
                    q.eq(q.field("imageRecognitionText"), undefined),
                    q.gt(q.field("uploadedAt"), cutoffTime)
                )
            )
            .order("desc")
            .take(limit);
    },
});

// NEW: Public action to trigger image recognition (replaces Next.js server action)
export const runImageRecognitionOnImage = action({
    args: {
        imageId: v.id("images"),
        forceRerun: v.optional(v.boolean()) // NEW: Allow forcing re-run even if already processed
    },
    returns: v.object({
        imageRecognitionText: v.optional(v.string()),
        alreadyProcessed: v.boolean(),
        success: v.boolean(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args): Promise<{
        imageRecognitionText?: string;
        alreadyProcessed: boolean;
        success: boolean;
        error?: string;
    }> => {
        try {
            const user = await getCurrentUser(ctx);
            if (!user?.userId) {
                throw new ConvexError("Authentication required");
            }

            // Get active account info
            const activeAccountInfo = await ctx.runQuery(api.ebayAccounts.getActiveAccount, { userId: user.userId });
            if (!activeAccountInfo) {
                throw new ConvexError("No active eBay account found. Please select an eBay account first.");
            }

            // Verify the image exists and belongs to the user
            const image = await ctx.runQuery(internal.images.getImageByIdInternal, {
                id: args.imageId,
                ebayUserId: activeAccountInfo.ebayUserId
            });

            if (!image) {
                throw new ConvexError("Image not found or access denied");
            }

            // Check if already processed (unless forcing re-run)
            if (image.imageRecognitionText && !args.forceRerun) {
                return {
                    imageRecognitionText: image.imageRecognitionText,
                    alreadyProcessed: true,
                    success: true
                };
            }

            // Run the image recognition (with force parameter)
            const result = await ctx.runAction(internal.images.processImageRecognition, {
                imageId: args.imageId,
                userId: activeAccountInfo.ebayUserId,
                forceRerun: args.forceRerun || false
            });

            if (result.success) {
                // Get the updated image to return the recognition text
                const updatedImage = await ctx.runQuery(internal.images.getImageByIdInternal, {
                    id: args.imageId,
                    ebayUserId: activeAccountInfo.ebayUserId
                });

                return {
                    imageRecognitionText: updatedImage?.imageRecognitionText,
                    alreadyProcessed: false,
                    success: true
                };
            } else {
                return {
                    alreadyProcessed: false,
                    success: false,
                    error: result.error
                };
            }
        } catch (error) {
            console.error(`Failed to run image recognition for ${args.imageId}`, {
                error: error instanceof Error ? error.message : String(error)
            });

            return {
                alreadyProcessed: false,
                success: false,
                error: error instanceof ConvexError ? error.message : (error instanceof Error ? error.message : String(error))
            };
        }
    }
});

// NEW: Manual trigger for batch processing (for admin use)
export const triggerBatchImageRecognition = action({
    args: {
        limit: v.optional(v.number()),
        forceReprocess: v.optional(v.boolean()),
    },
    returns: v.object({
        success: v.boolean(),
        processed: v.number(),
        failed: v.number(),
        skipped: v.number(),
    }),
    handler: async (ctx, args) => {
        "use node";

        const limit = args.limit || 20;
        const forceReprocess = args.forceReprocess || false;

        let processed = 0;
        let failed = 0;
        let skipped = 0;

        try {
            console.log(`Starting manual batch image recognition (limit: ${limit}, force: ${forceReprocess})`);

            // Get images to process
            let imagesToProcess;
            if (forceReprocess) {
                // Get all recent images regardless of recognition status
                imagesToProcess = await ctx.runQuery(internal.images.getRecentImages, { limit });
            } else {
                // Get only unrecognized images
                imagesToProcess = await ctx.runQuery(internal.images.getUnrecognizedImages, { limit });
            }

            console.log(`Found ${imagesToProcess.length} images to process`);

            for (const image of imagesToProcess) {
                try {
                    // Skip if already has recognition text and not forcing reprocess
                    if (image.imageRecognitionText && !forceReprocess) {
                        console.log(`Skipping image ${image._id} - already has recognition text`);
                        skipped++;
                        continue;
                    }

                    console.log(`Processing image recognition for ${image._id}`);
                    const result = await ctx.runAction(internal.images.processImageRecognition, {
                        imageId: image._id,
                        userId: image.ebayUserId,
                        forceRerun: forceReprocess,
                    });

                    if (result.success) {
                        processed++;
                    } else {
                        failed++;
                        console.error(`Failed to process ${image._id}: ${result.error}`);
                    }

                    // Small delay between images
                    await new Promise(resolve => setTimeout(resolve, 100));
                } catch (imageError) {
                    failed++;
                    console.error(`Exception processing image ${image._id}`, {
                        error: imageError instanceof Error ? imageError.message : String(imageError),
                    });
                }
            }

            console.log(`Batch processing completed: ${processed} processed, ${failed} failed, ${skipped} skipped`);
            return { success: true, processed, failed, skipped };

        } catch (error) {
            console.error('Manual batch processing failed', {
                error: error instanceof Error ? error.message : String(error),
            });
            return { success: false, processed, failed, skipped };
        }
    },
});

// NEW: Internal query to get recent images for manual batch processing
export const getRecentImages = internalQuery({
    args: {
        limit: v.optional(v.number()),
    },
    returns: v.array(imageDocValidator),
    handler: async (ctx, args): Promise<Doc<"images">[]> => {
        const limit = args.limit || 50;

        // Get recent images (last 30 days)
        const cutoffTime = Date.now() - (30 * 24 * 60 * 60 * 1000);

        return await ctx.db
            .query("images")
            .filter((q) => q.gt(q.field("uploadedAt"), cutoffTime))
            .order("desc")
            .take(limit);
    },
});

/**
 * Internal query to get all images for a specific SKU  
 * Used by the quick listing machine to populate draft images
 */
export const getImagesBySkuIdInternal = internalQuery({
    args: {
        skuId: v.id("skus"),
        ebayUserId: v.string(),
    },
    returns: v.array(imageDocValidator),
    handler: async (ctx, { skuId, ebayUserId }): Promise<Doc<"images">[]> => {
        return await ctx.db
            .query("images")
            .filter((q) => q.eq(q.field("skuId"), skuId))
            .filter((q) => q.eq(q.field("ebayUserId"), ebayUserId))
            .filter((q) => q.eq(q.field("deletedAt"), undefined))
            .order("desc")
            .collect();
    },
});

// Check if an image has child images (has been edited/cropped)
export const hasChildren = query({
    args: {
        imageId: v.id("images")
    },
    returns: v.boolean(),
    handler: async (ctx, args): Promise<boolean> => {
        const { account } = await getActiveAccount(ctx);
        if (!account) {
            return false;
        }

        const child = await ctx.db
            .query("images")
            .withIndex("by_parent_image", q => q.eq("parentImageId", args.imageId))
            .filter(q => q.eq(q.field("deletedAt"), undefined)) // Exclude soft-deleted children
            .first();

        return child !== null;
    },
});

// Associate multiple images with a draft and notify quick listing machine
export const associateImagesWithDraft = mutation({
    args: {
        imageIds: v.array(v.id("images")),
        draftUuid: v.string()
    },
    returns: v.object({
        success: v.boolean(),
        associatedCount: v.number(),
        error: v.optional(v.string())
    }),
    handler: async (ctx, args) => {
        const { imageIds, draftUuid } = args;

        try {
            const { account } = await getActiveAccount(ctx, { requireAccount: true });
            const activeEbayUserId = account!.ebayUserId;

            // Get the draft to find associated SKU
            const draft = await ctx.db
                .query('userListings')
                .filter(q => q.eq(q.field('uuid'), draftUuid))
                .unique();

            if (!draft || draft.ebayUserId !== activeEbayUserId) {
                return {
                    success: false,
                    associatedCount: 0,
                    error: "Draft not found or access denied"
                };
            }

            if (!draft.skuId) {
                return {
                    success: false,
                    associatedCount: 0,
                    error: "Draft has no associated SKU"
                };
            }

            // Store previous image IDs
            const previousImageIds = draft.imageStatus?.imageIds || [];

            // Associate all images with the SKU first
            let associatedCount = 0;
            for (const imageId of imageIds) {
                try {
                    const image = await ctx.db.get(imageId);
                    if (image && image.ebayUserId === activeEbayUserId) {
                        await ctx.db.patch(imageId, {
                            skuId: draft.skuId,
                            updatedAt: Date.now()
                        });
                        associatedCount++;
                    }
                } catch (error) {
                    console.error(`Failed to associate image ${imageId} with SKU`, { error });
                }
            }

            // Update draft with new image list
            const newImageIds = [...previousImageIds, ...imageIds];
            await ctx.db.patch(draft._id, {
                imageStatus: {
                    ...(draft.imageStatus || {}),
                    imageIds: newImageIds,
                    lastEbayUploadAttempt: Date.now()
                },
                updatedAt: Date.now(),
                lastModifiedLocally: Date.now()
            });

            // Schedule machine notification (fire-and-forget)
            ctx.scheduler.runAfter(0, internal.quickListingMachineV2.notifyMachineOfImageUpdate, {
                draftUuid: draft.uuid,
                imageIds: newImageIds.map(id => id.toString()),
                previousImageIds: previousImageIds.map(id => id.toString())
            }).catch((error) => {
                console.error('Failed to schedule machine notification for draft images', {
                    draftUuid: draft.uuid,
                    error: error instanceof Error ? error.message : String(error)
                });
            });

            console.log(`Associated ${associatedCount} images with draft ${draftUuid}`, {
                draftUuid,
                associatedCount,
                totalImages: newImageIds.length
            });

            return {
                success: true,
                associatedCount,
            };

        } catch (error) {
            console.error('Failed to associate images with draft', {
                draftUuid,
                imageIds,
                error: error instanceof Error ? error.message : String(error)
            });

            return {
                success: false,
                associatedCount: 0,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    },
});
