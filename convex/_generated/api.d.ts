/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type * as activityEventTypes from "../activityEventTypes.js";
import type * as adoption from "../adoption.js";
import type * as aiActions from "../aiActions.js";
import type * as auth from "../auth.js";
import type * as authOperations from "../authOperations.js";
import type * as blueprints from "../blueprints.js";
import type * as bulkAiEnhancementWorkflow from "../bulkAiEnhancementWorkflow.js";
import type * as categorySuggestions from "../categorySuggestions.js";
import type * as clearEbayResponses from "../clearEbayResponses.js";
import type * as constants_marketplaceMappings from "../constants/marketplaceMappings.js";
import type * as createDraftDirect from "../createDraftDirect.js";
import type * as createRevisionDraft from "../createRevisionDraft.js";
import type * as crons from "../crons.js";
import type * as draftCreationPublic from "../draftCreationPublic.js";
import type * as draftStatePersist from "../draftStatePersist.js";
import type * as drafts_utils from "../drafts/utils.js";
import type * as drafts from "../drafts.js";
import type * as ebay from "../ebay.js";
import type * as ebayAccountSelection from "../ebayAccountSelection.js";
import type * as ebayAccounts from "../ebayAccounts.js";
import type * as ebayActionHelpers from "../ebayActionHelpers.js";
import type * as ebayAppAuth from "../ebayAppAuth.js";
import type * as ebayAuth from "../ebayAuth.js";
import type * as ebayAuthInternal from "../ebayAuthInternal.js";
import type * as ebayAuthUtils from "../ebayAuthUtils.js";
import type * as ebayContext from "../ebayContext.js";
import type * as ebayListingWorkflow from "../ebayListingWorkflow.js";
import type * as ebayListings from "../ebayListings.js";
import type * as ebayListingsActions from "../ebayListingsActions.js";
import type * as ebayProductActions from "../ebayProductActions.js";
import type * as ebayPublishing from "../ebayPublishing.js";
import type * as ebayPublishingShared from "../ebayPublishingShared.js";
import type * as ebayResponseMutations from "../ebayResponseMutations.js";
import type * as ebayRestFactory from "../ebayRestFactory.js";
import type * as ebayTradingClient from "../ebayTradingClient.js";
import type * as ebayTradingFactory from "../ebayTradingFactory.js";
import type * as env from "../env.js";
import type * as epsUploadService from "../epsUploadService.js";
import type * as fulfillment from "../fulfillment.js";
import type * as helpers_ebayApiHelper from "../helpers/ebayApiHelper.js";
import type * as image_variants from "../image_variants.js";
import type * as images from "../images.js";
import type * as internal_adminDuplicateTokens from "../internal/adminDuplicateTokens.js";
import type * as internal_draftCreation from "../internal/draftCreation.js";
import type * as internal_draftMutations from "../internal/draftMutations.js";
import type * as internal_ebayAccounts from "../internal/ebayAccounts.js";
import type * as internal_ebayAuth from "../internal/ebayAuth.js";
import type * as internal_ebayCredentials from "../internal/ebayCredentials.js";
import type * as internal_images from "../internal/images.js";
import type * as internal_skuActivityEvents from "../internal/skuActivityEvents.js";
import type * as lib_skuGeneration from "../lib/skuGeneration.js";
import type * as listingOperations from "../listingOperations.js";
import type * as listingsActions from "../listingsActions.js";
import type * as metadata from "../metadata.js";
import type * as model_appAuth from "../model/appAuth.js";
import type * as model_auth from "../model/auth.js";
import type * as model_context from "../model/context.js";
import type * as model_ebay from "../model/ebay.js";
import type * as model_ebayAuth from "../model/ebayAuth.js";
import type * as model_errors from "../model/errors.js";
import type * as model_users from "../model/users.js";
import type * as notes from "../notes.js";
import type * as oauthHelpers from "../oauthHelpers.js";
import type * as rateTables from "../rateTables.js";
import type * as sellerPrivileges from "../sellerPrivileges.js";
import type * as singleSkuAiEnhancementWorkflow from "../singleSkuAiEnhancementWorkflow.js";
import type * as skuActionsPublic from "../skuActionsPublic.js";
import type * as skuAiProcessing from "../skuAiProcessing.js";
import type * as skuMutations from "../skuMutations.js";
import type * as skus from "../skus.js";
import type * as testApprovalInternal from "../testApprovalInternal.js";
import type * as trading_account_profile from "../trading_account_profile.js";
import type * as userWorkspaceContext from "../userWorkspaceContext.js";
import type * as users from "../users.js";
import type * as utils_foundationUtils from "../utils/foundationUtils.js";
import type * as utils_marketplace from "../utils/marketplace.js";
import type * as utils_tradingApiErrors from "../utils/tradingApiErrors.js";
import type * as utils_uuid from "../utils/uuid.js";
import type * as utils from "../utils.js";
import type * as validators from "../validators.js";

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  activityEventTypes: typeof activityEventTypes;
  adoption: typeof adoption;
  aiActions: typeof aiActions;
  auth: typeof auth;
  authOperations: typeof authOperations;
  blueprints: typeof blueprints;
  bulkAiEnhancementWorkflow: typeof bulkAiEnhancementWorkflow;
  categorySuggestions: typeof categorySuggestions;
  clearEbayResponses: typeof clearEbayResponses;
  "constants/marketplaceMappings": typeof constants_marketplaceMappings;
  createDraftDirect: typeof createDraftDirect;
  createRevisionDraft: typeof createRevisionDraft;
  crons: typeof crons;
  draftCreationPublic: typeof draftCreationPublic;
  draftStatePersist: typeof draftStatePersist;
  "drafts/utils": typeof drafts_utils;
  drafts: typeof drafts;
  ebay: typeof ebay;
  ebayAccountSelection: typeof ebayAccountSelection;
  ebayAccounts: typeof ebayAccounts;
  ebayActionHelpers: typeof ebayActionHelpers;
  ebayAppAuth: typeof ebayAppAuth;
  ebayAuth: typeof ebayAuth;
  ebayAuthInternal: typeof ebayAuthInternal;
  ebayAuthUtils: typeof ebayAuthUtils;
  ebayContext: typeof ebayContext;
  ebayListingWorkflow: typeof ebayListingWorkflow;
  ebayListings: typeof ebayListings;
  ebayListingsActions: typeof ebayListingsActions;
  ebayProductActions: typeof ebayProductActions;
  ebayPublishing: typeof ebayPublishing;
  ebayPublishingShared: typeof ebayPublishingShared;
  ebayResponseMutations: typeof ebayResponseMutations;
  ebayRestFactory: typeof ebayRestFactory;
  ebayTradingClient: typeof ebayTradingClient;
  ebayTradingFactory: typeof ebayTradingFactory;
  env: typeof env;
  epsUploadService: typeof epsUploadService;
  fulfillment: typeof fulfillment;
  "helpers/ebayApiHelper": typeof helpers_ebayApiHelper;
  image_variants: typeof image_variants;
  images: typeof images;
  "internal/adminDuplicateTokens": typeof internal_adminDuplicateTokens;
  "internal/draftCreation": typeof internal_draftCreation;
  "internal/draftMutations": typeof internal_draftMutations;
  "internal/ebayAccounts": typeof internal_ebayAccounts;
  "internal/ebayAuth": typeof internal_ebayAuth;
  "internal/ebayCredentials": typeof internal_ebayCredentials;
  "internal/images": typeof internal_images;
  "internal/skuActivityEvents": typeof internal_skuActivityEvents;
  "lib/skuGeneration": typeof lib_skuGeneration;
  listingOperations: typeof listingOperations;
  listingsActions: typeof listingsActions;
  metadata: typeof metadata;
  "model/appAuth": typeof model_appAuth;
  "model/auth": typeof model_auth;
  "model/context": typeof model_context;
  "model/ebay": typeof model_ebay;
  "model/ebayAuth": typeof model_ebayAuth;
  "model/errors": typeof model_errors;
  "model/users": typeof model_users;
  notes: typeof notes;
  oauthHelpers: typeof oauthHelpers;
  rateTables: typeof rateTables;
  sellerPrivileges: typeof sellerPrivileges;
  singleSkuAiEnhancementWorkflow: typeof singleSkuAiEnhancementWorkflow;
  skuActionsPublic: typeof skuActionsPublic;
  skuAiProcessing: typeof skuAiProcessing;
  skuMutations: typeof skuMutations;
  skus: typeof skus;
  testApprovalInternal: typeof testApprovalInternal;
  trading_account_profile: typeof trading_account_profile;
  userWorkspaceContext: typeof userWorkspaceContext;
  users: typeof users;
  "utils/foundationUtils": typeof utils_foundationUtils;
  "utils/marketplace": typeof utils_marketplace;
  "utils/tradingApiErrors": typeof utils_tradingApiErrors;
  "utils/uuid": typeof utils_uuid;
  utils: typeof utils;
  validators: typeof validators;
}>;
declare const fullApiWithMounts: typeof fullApi;

export declare const api: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApiWithMounts,
  FunctionReference<any, "internal">
>;

export declare const components: {
  workflow: {
    journal: {
      load: FunctionReference<
        "query",
        "internal",
        { workflowId: string },
        {
          journalEntries: Array<{
            _creationTime: number;
            _id: string;
            step: {
              args: any;
              argsSize: number;
              completedAt?: number;
              functionType: "query" | "mutation" | "action";
              handle: string;
              inProgress: boolean;
              name: string;
              runResult?:
                | { kind: "success"; returnValue: any }
                | { error: string; kind: "failed" }
                | { kind: "canceled" };
              startedAt: number;
              workId?: string;
            };
            stepNumber: number;
            workflowId: string;
          }>;
          logLevel: "DEBUG" | "TRACE" | "INFO" | "REPORT" | "WARN" | "ERROR";
          ok: boolean;
          workflow: {
            _creationTime: number;
            _id: string;
            args: any;
            generationNumber: number;
            logLevel?: any;
            name?: string;
            onComplete?: { context?: any; fnHandle: string };
            runResult?:
              | { kind: "success"; returnValue: any }
              | { error: string; kind: "failed" }
              | { kind: "canceled" };
            startedAt?: any;
            state?: any;
            workflowHandle: string;
          };
        }
      >;
      startStep: FunctionReference<
        "mutation",
        "internal",
        {
          generationNumber: number;
          name: string;
          retry?:
            | boolean
            | { base: number; initialBackoffMs: number; maxAttempts: number };
          schedulerOptions?: { runAt?: number } | { runAfter?: number };
          step: {
            args: any;
            argsSize: number;
            completedAt?: number;
            functionType: "query" | "mutation" | "action";
            handle: string;
            inProgress: boolean;
            name: string;
            runResult?:
              | { kind: "success"; returnValue: any }
              | { error: string; kind: "failed" }
              | { kind: "canceled" };
            startedAt: number;
            workId?: string;
          };
          workflowId: string;
          workpoolOptions?: {
            defaultRetryBehavior?: {
              base: number;
              initialBackoffMs: number;
              maxAttempts: number;
            };
            logLevel?: "DEBUG" | "TRACE" | "INFO" | "REPORT" | "WARN" | "ERROR";
            maxParallelism?: number;
            retryActionsByDefault?: boolean;
          };
        },
        {
          _creationTime: number;
          _id: string;
          step: {
            args: any;
            argsSize: number;
            completedAt?: number;
            functionType: "query" | "mutation" | "action";
            handle: string;
            inProgress: boolean;
            name: string;
            runResult?:
              | { kind: "success"; returnValue: any }
              | { error: string; kind: "failed" }
              | { kind: "canceled" };
            startedAt: number;
            workId?: string;
          };
          stepNumber: number;
          workflowId: string;
        }
      >;
    };
    workflow: {
      cancel: FunctionReference<
        "mutation",
        "internal",
        { workflowId: string },
        null
      >;
      cleanup: FunctionReference<
        "mutation",
        "internal",
        { workflowId: string },
        boolean
      >;
      complete: FunctionReference<
        "mutation",
        "internal",
        {
          generationNumber: number;
          runResult:
            | { kind: "success"; returnValue: any }
            | { error: string; kind: "failed" }
            | { kind: "canceled" };
          workflowId: string;
        },
        null
      >;
      create: FunctionReference<
        "mutation",
        "internal",
        {
          maxParallelism?: number;
          onComplete?: { context?: any; fnHandle: string };
          startAsync?: boolean;
          workflowArgs: any;
          workflowHandle: string;
          workflowName: string;
        },
        string
      >;
      getStatus: FunctionReference<
        "query",
        "internal",
        { workflowId: string },
        {
          inProgress: Array<{
            _creationTime: number;
            _id: string;
            step: {
              args: any;
              argsSize: number;
              completedAt?: number;
              functionType: "query" | "mutation" | "action";
              handle: string;
              inProgress: boolean;
              name: string;
              runResult?:
                | { kind: "success"; returnValue: any }
                | { error: string; kind: "failed" }
                | { kind: "canceled" };
              startedAt: number;
              workId?: string;
            };
            stepNumber: number;
            workflowId: string;
          }>;
          logLevel: "DEBUG" | "TRACE" | "INFO" | "REPORT" | "WARN" | "ERROR";
          workflow: {
            _creationTime: number;
            _id: string;
            args: any;
            generationNumber: number;
            logLevel?: any;
            name?: string;
            onComplete?: { context?: any; fnHandle: string };
            runResult?:
              | { kind: "success"; returnValue: any }
              | { error: string; kind: "failed" }
              | { kind: "canceled" };
            startedAt?: any;
            state?: any;
            workflowHandle: string;
          };
        }
      >;
    };
  };
};
